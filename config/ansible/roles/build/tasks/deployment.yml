---

- name: Generate types for your Cloudflare
  command: pnpm typegen
  args:
    chdir: '{{ workspace }}'

- name: pnpm install
  command: pnpm install
  args:
    chdir: '{{ workspace }}'

- name: pnpm build
  command: pnpm build
  args:
    chdir: '{{ workspace }}'

- name: pnpm wrangler pages deploy
  expect:
    command: pnpm wrangler pages deploy --commit-dirty=true
    responses:
      "Would you like to create it?": "{{ branch }}\n"
  args:
    chdir: '{{ workspace }}'
