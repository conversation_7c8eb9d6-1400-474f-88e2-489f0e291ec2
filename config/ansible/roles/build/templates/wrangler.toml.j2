#:schema node_modules/wrangler/config-schema.json
name = "{{ stage }}-{{ project_name }}-web"
compatibility_date = "2024-10-22"
pages_build_output_dir = "./build/client"


[[d1_databases]]
binding = "DB"
database_name = "{{ db_name }}"
database_id = "{{ db_id }}"
preview_database_id = "{{ db_name }}-preview"
migrations_table = "d1_migrations"
migrations_dir = "./app/db/migrations"

[[r2_buckets]]
binding = 'R2'
bucket_name = '{{ stage }}-{{ project_name }}-media'

######## PREVIEW environment config ########
[[env.preview.d1_databases]]
binding = "DB"
database_name = "{{ db_name }}"
database_id = "{{ db_id }}"
preview_database_id = "{{ db_name }}-preview"
migrations_table = "d1_migrations"
migrations_dir = "./app/db/migrations"

[[env.preview.r2_buckets]]
binding = 'R2'
bucket_name = '{{ stage }}-{{ project_name }}-media'

[env.preview.vars]
SECRET = "{{ secret }}"
RESET_PASSWORD_SECRET = "{{ reset_password_secret }}"
RESEND_API_KEY = "{{ resend_api_key }}"
RESEND_FROM_EMAIL = "{{ resend_from_email }}"
RESEND_CONTACT_TO_EMAIL = "{{ resend_contact_to_email }}"

######## PRODUCTION environment config ########
[[env.production.d1_databases]]
binding = "DB"
database_name = "{{ db_name }}"
database_id = "{{ db_id }}"
preview_database_id = "{{ db_name }}-preview"
migrations_table = "d1_migrations"
migrations_dir = "./app/db/migrations"

[[env.production.r2_buckets]]
binding = 'R2'
bucket_name = '{{ stage }}-{{ project_name }}-media'

[env.production.vars]
SECRET = "{{ secret }}"
RESET_PASSWORD_SECRET = "{{ reset_password_secret }}"
RESEND_API_KEY = "{{ resend_api_key }}"
RESEND_FROM_EMAIL = "{{ resend_from_email }}"
RESEND_CONTACT_TO_EMAIL = "{{ resend_contact_to_email }}"
