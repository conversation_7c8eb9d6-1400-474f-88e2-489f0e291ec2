import {
  vitePlugin as remix,
  cloudflareDevProxyVitePlugin as remixCloudflareDevProxy
} from "@remix-run/dev"
import { defineConfig } from "vite"
import tsconfigPaths from "vite-tsconfig-paths"

declare module "@remix-run/cloudflare" {
  interface Future {
    v3_singleFetch: true
  }
}

export default defineConfig({
  plugins: [
    remixCloudflareDevProxy(),
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: true,
        v3_lazyRouteDiscovery: true
      }
    }),
    tsconfigPaths()
  ],
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        sourcemap: true,
        sourcemapExcludeSources: false
      }
    }
  },
  optimizeDeps: {
    entries: ["./app/**/*.{ts,tsx}"],
    include: ["react", "react-dom", "react-i18next", "i18next"],
    exclude: [],
    esbuildOptions: {
      sourcemap: true,
      sourcesContent: true,
      keepNames: true
    }
  },
  resolve: {
    dedupe: ["react", "react-dom", "i18next", "react-i18next"]
  },
  server: {
    fs: {
      strict: false
    }
  }
})
