#!/bin/bash

# Check if 'wrangler' command is available
if ! command -v wrangler &> /dev/null; then
    echo "Wrangler is not installed. Installing..."

    # Check if Node.js and npm are installed
    if ! command -v npm &> /dev/null; then
        echo "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi

    # Install Wrangler using npm
    npm install -g @cloudflare/wrangler

    # Verify installation
    if ! command -v wrangler &> /dev/null; then
        echo "Wrangler installation failed. Please check for errors."
        exit 1
    fi

    echo "Wrangler installed successfully."
else
    echo "Wrangler is already installed."
fi

# Check Cloudflare credentials
wrangler whoami
# Ask user for confirmation
read -rp "Is this the correct Cloudflare account? (yes/no): " answer

# Convert answer to lowercase
answer="${answer,,}"

if [[ "$answer" == "yes" || "$answer" == "y" ]]; then
    echo "Continuing script..."
    # Your main script logic goes here
else
    echo "Exiting script."
    echo "Please run wrangler login to correct account."
    exit 1
fi

# Prompt the user for the deploy stage
echo "Enter the deploy stage (e.g., latest, demo, live):"
read -r stage

# Validate the input
if [[ -z "$stage" ]]; then
  echo "Error: Deploy stage cannot be empty."
  exit 1
fi

# Prompt the user for the deploy tag
echo "Enter the deploy tag: migrate, seed, deploy (e.g., migrate, seeds, OR migrate,seeds ):"
read -r tag

# Validate the input
if [[ -z "$tag" ]]; then
  echo "Error: Deploy tag cannot be empty."
  exit 1
fi

# Confirmation message
echo "You are about to deploy to '$stage' with tag '$tag'..."

# Execute the build script with environment variables
WORKSPACE=$(pwd) STAGE=$stage TAG=$tag ./config/jenkins/build.sh
