@tailwind base;
@tailwind components;
@tailwind utilities;

.btn:not(.btn-sm),
.btn.btn-md,
.input {
  min-height: 2.8em;
  height: 2.8em;
  @apply rounded-md;
}

html {
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  .btn.md\:btn-md {
    min-height: 2.8em;
    height: 2.8em;
  }
}

.alert {
  @apply rounded-md py-2;
}

.text{
  font-size: 18px;
}


.titleOnBackgroundImage {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold;
}

.shortDescriptionOnBackgroundImage {
  @apply text-lg sm:text-xl md:text-2xl lg:text-3xl text-center;
}

.title{
  @apply text-xl sm:text-[24px] md:text-[26px] lg:text-[32px] font-bold leading-[35px] lg:leading-[40px];
}
.readyToJoinTitle{
  @apply text-xl sm:text-[24px] md:text-[26px] lg:text-[32px] leading-[35px] lg:leading-[40px];
}

.subTitle{
  @apply text-[18px] sm:text-[20px] md:text-[22px] lg:text-[24px] font-bold leading-[35px] lg:leading-[40px];
}

.shortDescription{
  @apply text-center text-[14px] sm:text-[15px] md:text-[17px] lg:text-[20px] leading-[30px] lg:leading-[35px];
}

.description{
  @apply  text-[14px] sm:text-[15px] md:text-[17px] lg:text-[20px] leading-[30px] lg:leading-[35px];
}

