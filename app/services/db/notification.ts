import * as notifications from "~/db/schema/notification"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import { eq } from "drizzle-orm"

export default class NotificationService extends BaseService<
  typeof notifications
> {
  private readonly notification = notifications.notifications
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...notifications } }))
  }

  public async create(param: any) {
    return await this.db.insert(this.notification).values(param).returning()
  }

  public async getNotificationsByUserId(param: any) {
    const { userId, page = 1, pageSize = 5 } = param
    const notifications = await this.db.query.notifications.findMany({
      where: eq(this.notification.user_id, userId),
      orderBy: (notifications, { desc }) => desc(notifications.created_at),
      offset: (page - 1) * pageSize,
      limit: pageSize
    })
    const paginate = await this.getPaginate({
      table: this.notification,
      filter: eq(this.notification.user_id, userId),
      page,
      pageSize
    })
    return { data: notifications, paginate }
  }

  public async update(id: number) {
    return await this.db
      .update(this.notification)
      .set({ isRead: true })
      .where(eq(this.notification.id, id))
      .returning()
  }
}
