import { drizzle } from "drizzle-orm/d1"
import { BaseService } from "."
import * as comments from "~/db/schema/comments"
import * as users from "~/db/schema/users"
import { eq } from "drizzle-orm"

export default class CommentService extends BaseService<typeof comments> {
  private readonly comment = comments.comments
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...comments, ...users } }))
  }

  public async create(param: any) {
    return await this.db.insert(this.comment).values(param).returning()
  }

  public async getCommentsByProjectId(projectId: number) {
    const comments = await this.db.query.comments.findMany({
      where: eq(this.comment.project_id, projectId),
      with: {
        user: true
      }
    })
    return comments ?? []
  }
}
