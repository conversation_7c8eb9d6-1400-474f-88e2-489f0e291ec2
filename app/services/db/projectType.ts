import { drizzle } from "drizzle-orm/d1"
import * as schema from "~/db/schema/projectTypes"
import { BaseService } from "."

export default class ProjectTypeService extends BaseService<typeof schema> {
  private readonly projectType = schema.projectTypes

  public constructor(db: D1Database) {
    super(drizzle(db, { schema }))
  }
  public async getMany() {
    return this.db.query.projectTypes.findMany()
  }
}
