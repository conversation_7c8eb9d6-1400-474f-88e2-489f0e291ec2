import * as projects from "~/db/schema/projects"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import { and, eq, not, sql, S<PERSON><PERSON>rapper } from "drizzle-orm"
import * as implementations from "~/db/schema/implementations"
import { ERole } from "~/enums/EUserRole"
import { assigns } from "~/db/schema/assigns"
import { requestLONs } from "~/db/schema/requestLON"

export default class OverviewService extends BaseService<
  typeof projects & typeof implementations
> {
  private readonly project = projects.projects
  private readonly implementation = implementations.implementations
  public constructor(db: D1Database) {
    super(
      drizzle(db, {
        schema: { ...projects, ...implementations }
      })
    )
  }
  public async getProjectOverview(param?: any) {
    const filters: (SQLWrapper | undefined)[] = []
    if (param?.user_id) {
      filters.push(eq(this.project.user_id, param.user_id))
    }
    if (
      param?.user?.role === ERole.Administrator ||
      param?.user?.role === ERole.Secretariat
    ) {
      filters.push(not(eq(this.project.status, "draft")))
    }
    if (
      param?.user?.role === ERole.InternalReviewer ||
      param?.user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.project.id} IN (select project_id from ${assigns} where user_id = ${param?.user.id})`
      )
    }
    if (param?.user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.project.id} IN (select project_id from ${requestLONs} where user_id = ${param?.user.id})`
      )
    }
    const filter = and(...filters)
    const projects = await this.db.query.projects.findMany({
      where: filter
    })

    const projectApproved = projects.filter(item => item?.status === "approved")

    const totalExpectCarbon = projectApproved.reduce(
      (total, project) => total + Number(project.estimate_emission_reduction),
      0
    )

    const totalProject = projects.length
    const totalDraftProject = projects.filter(
      item => item.status === "draft"
    ).length
    const totalSubmittedProject = projects.filter(
      item => item?.status === "submitted"
    ).length
    const totalUnderReviewProject = projects.filter(
      item => item?.status === "under_review"
    ).length

    const totalRejectProject = projects.filter(
      item => item?.status === "rejected"
    ).length
    const totalApprovedProject = projects.filter(
      item => item?.status === "approved"
    ).length

    return {
      totalProject,
      totalDraftProject,
      totalSubmittedProject,
      totalUnderReviewProject,
      totalRejectProject,
      totalApprovedProject,
      totalExpectCarbon
    }
  }
  public async getCarbonCreditOverview(param?: any) {
    const filters: (SQLWrapper | undefined)[] = [
      eq(this.implementation.status, "approved")
    ]

    if (param?.user_id) {
      filters.push(eq(this.implementation.user_id, param.user_id))
    }
    const filter = and(...filters)

    const implementationRecords = await this.db.query.implementations.findMany({
      where: filter
    })

    const totalIssuedCarbon = implementationRecords.reduce(
      (total, implementation) => total + Number(implementation.carbon_credit),
      0
    )

    const totalRetiredCarbon = implementationRecords.reduce(
      (total, implementation) => total + Number(implementation.share_co2),
      0
    )

    return {
      totalIssuedCarbon,
      totalRetiredCarbon
    }
  }
}
