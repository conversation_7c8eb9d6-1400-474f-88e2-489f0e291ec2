import * as requestLONs from "~/db/schema/requestLON"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import * as users from "~/db/schema/users"
import * as projects from "~/db/schema/projects"
import { and, eq } from "drizzle-orm"

export default class RequestLNOService extends BaseService<typeof requestLONs> {
  private readonly requestLON = requestLONs.requestLONs
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...requestLONs, ...users, ...projects } }))
  }
  public async create(param: { user_ids: number[]; project_id: number }) {
    const projectExists = await this.db
      .select()
      .from(projects.projects)
      .where(eq(projects.projects.id, param.project_id))
      .execute()
    if (projectExists.length === 0) {
      throw new Error(`Project with id ${param.project_id} does not exist`)
    }

    const assignments = []

    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()
      if (userExists.length === 0) {
        console.warn(`User with id ${user_id} does not exist, skipping...`)
        continue
      }

      const existingAssignment = await this.db
        .select()
        .from(requestLONs.requestLONs)
        .where(
          and(
            eq(requestLONs.requestLONs.user_id, user_id),
            eq(requestLONs.requestLONs.project_id, param.project_id)
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} is already assigned to project ${param.project_id}, skipping...`
        )
        continue
      }

      const requestLON = await this.db
        .insert(requestLONs.requestLONs)
        .values({
          user_id,
          project_id: param.project_id
        })
        .returning()
        .execute()

      if (requestLON.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...requestLON[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }

    return assignments
  }
}
