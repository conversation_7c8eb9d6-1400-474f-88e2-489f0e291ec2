import { eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import * as ideaNotes from "~/db/schema/ideaNotes"
import { BaseService } from "."

export default class IdeaNoteService extends BaseService<typeof ideaNotes> {
  private readonly ideaNote = ideaNotes.ideaNotes

  public constructor(db: D1Database) {
    super(
      drizzle(db, {
        schema: { ...ideaNotes }
      })
    )
  }
  public async create(param: any) {
    return await this.db.insert(this.ideaNote).values(param).returning()
  }
  public async update(id: number, param: any) {
    return await this.db
      .update(this.ideaNote)
      .set(param)
      .where(eq(this.ideaNote.id, id))
      .returning()
  }
}
