import { drizzle } from "drizzle-orm/d1"
import * as schema from "~/db/schema/projectSectors"
import { BaseService } from "."

export default class ProjectSectorService extends BaseService<typeof schema> {
  private readonly projectSector = schema.projectSectors

  public constructor(db: D1Database) {
    super(drizzle(db, { schema }))
  }

  public async getMany() {
    return this.db.query.projectSectors.findMany()
  }
}
