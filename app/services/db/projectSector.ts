import { eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import * as schema from "~/db/schema"
import type { IProjectSector } from "~/types"
import { BaseService } from "."

export class ProjectSectorService extends BaseService<typeof schema> {
  private readonly projectSector = schema.projectSectors

  public constructor(db: D1Database) {
    super(drizzle(db, { schema }))
  }

  public async create(param: IProjectSector) {
    return await this.db.insert(this.projectSector).values(param).returning()
  }

  public async update(id: number, param: Partial<IProjectSector>) {
    return await this.db
      .update(this.projectSector)
      .set(param)
      .where(eq(this.projectSector.id, id))
      .returning()
  }

  public async delete(id: number) {
    return await this.db
      .delete(this.projectSector)
      .where(eq(this.projectSector.id, id))
      .returning()
  }

  public async getOne(id: number) {
    return this.db.query.projectSectors.findFirst({
      where: eq(this.projectSector.id, id)
    })
  }

  public async getMany() {
    return this.db.query.projectSectors.findMany()
  }
}
