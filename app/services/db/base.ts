import { count, type SQL } from "drizzle-orm"
import type {
  SQLiteTableWithColumns,
  TableConfig
} from "drizzle-orm/sqlite-core"
import type { DrizzleD1 } from "~/types"

interface IPaginate<T extends TableConfig> {
  table: SQLiteTableWithColumns<T>
  filter: SQL | undefined
  page: number
  pageSize: number
}

export class BaseService<T extends Record<string, unknown>> {
  protected readonly db: DrizzleD1<T>

  constructor(db: DrizzleD1<T>) {
    this.db = db
  }

  protected first<T>(arr: T[]) {
    return arr.length > 0 ? arr[0] : null
  }

  protected async getPaginate<T extends TableConfig>(param: IPaginate<T>) {
    const res = await this.db
      .select({ count: count() })
      .from(param.table)
      .where(param.filter)

    let total = 0
    let pageCount = 1
    const pageSize = param.pageSize
    if (res.length > 0) {
      total = res[0].count
      if (res[0].count >= pageSize) {
        pageCount = Math.floor(total / pageSize)
        if (total % pageSize !== 0) pageCount += 1
      }
    }
    return { page: param.page, pageSize, pageCount, total }
  }
}
