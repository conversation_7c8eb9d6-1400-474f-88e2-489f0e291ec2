import { and, eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import * as assigns from "~/db/schema/assigns"
import * as users from "~/db/schema/users"
import * as projects from "~/db/schema/projects"
import { BaseService } from "."

export default class AssignService extends BaseService<typeof assigns> {
  private readonly assign = assigns.assigns

  public constructor(db: D1Database) {
    super(
      drizzle(db, {
        schema: { ...assigns, ...users, ...projects }
      })
    )
  }
  public async create(param: {
    user_ids: number[]
    project_id: number
    support_document: string
    isInternalReviewer: boolean
  }) {
    const projectExists = await this.db
      .select()
      .from(projects.projects)
      .where(eq(projects.projects.id, param.project_id))
      .execute()
    if (projectExists.length === 0) {
      throw new Error(`Project with id ${param.project_id} does not exist`)
    }

    const assignments = []

    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()
      if (userExists.length === 0) {
        console.warn(`User with id ${user_id} does not exist, skipping...`)
        continue
      }

      const existingAssignment = await this.db
        .select()
        .from(assigns.assigns)
        .where(
          and(
            eq(assigns.assigns.user_id, user_id),
            eq(assigns.assigns.project_id, param.project_id)
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} is already assigned to project ${param.project_id}, skipping...`
        )
        continue
      }

      const assignment = await this.db
        .insert(assigns.assigns)
        .values({
          user_id,
          project_id: param.project_id,
          support_document: param.support_document,
          isInternalReviewer: param.isInternalReviewer
        })
        .returning()
        .execute()

      if (assignment.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...assignment[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }

    return assignments
  }

  public async update(id: number, param: any) {
    const assigns = await this.db
      .update(this.assign)
      .set(param)
      .where(eq(this.assign.id, id))
      .returning()
    return assigns.length > 0 ? assigns[0] : null
  }
  public async getByProjectId(projectId: number) {
    return this.db.query.assigns.findMany({
      where: eq(this.assign.project_id, projectId),
      with: {
        user: true
      }
    })
  }
}
