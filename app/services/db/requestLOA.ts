import * as requestLOAs from "~/db/schema/requestLOA"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import * as users from "~/db/schema/users"
import * as pdds from "~/db/schema/pdds"
import { and, eq } from "drizzle-orm"

export default class RequestLOAService extends BaseService<typeof requestLOAs> {
  private readonly requestLOA = requestLOAs.requestLOAs

  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...requestLOAs, users, pdds } }))
  }

  public async create(param: { user_ids: number[]; pdd_id: number }) {
    const pddExists = await this.db
      .select()
      .from(pdds.pdds)
      .where(eq(pdds.pdds.id, param.pdd_id))
      .execute()

    if (pddExists.length === 0) {
      throw new Error(`PDD with id ${param?.pdd_id} does not exist`)
    }
    const assignments = []
    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()

      if (userExists.length === 0) {
        console.warn(`User with ${user_id} does not exist, skipping...`)
        continue
      }
      const existingAssignment = await this.db
        .select()
        .from(requestLOAs.requestLOAs)
        .where(
          and(
            eq(requestLOAs.requestLOAs.user_id, user_id),
            eq(requestLOAs.requestLOAs.pdd_id, param.pdd_id)
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} is already assigned to pdd ${param.pdd_id}, skipping...`
        )
        continue
      }
      const requestLOA = await this.db
        .insert(requestLOAs.requestLOAs)
        .values({
          user_id,
          pdd_id: param.pdd_id
        })
        .returning()
        .execute()

      if (requestLOA.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...requestLOA[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }
    return assignments
  }
}
