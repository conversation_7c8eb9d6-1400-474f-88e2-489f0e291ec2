import * as pddAssigns from "~/db/schema/pddAssigns"
import * as users from "~/db/schema/users"
import * as pdds from "~/db/schema/pdds"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import { and, eq } from "drizzle-orm"

export default class PddAssignService extends BaseService<typeof pddAssigns> {
  private readonly pddAssign = pddAssigns.pddAssigns
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...pddAssigns, ...users } }))
  }

  public async create(param: {
    user_ids: number[]
    pdd_id: number
    support_document: string
    isInternalReviewer: boolean
  }) {
    const pddExists = await this.db
      .select()
      .from(pdds.pdds)
      .where(eq(pdds.pdds.id, param.pdd_id))
      .execute()
    if (pddExists.length === 0) {
      throw new Error(`PDD with id ${param.pdd_id} does not exist`)
    }
    const assignments = []
    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()
      if (userExists.length === 0) {
        console.warn(`User with id ${user_id} does not exist, skipping...`)
        continue
      }
      const existingAssignment = await this.db
        .select()
        .from(pddAssigns.pddAssigns)
        .where(
          and(
            eq(pddAssigns.pddAssigns.user_id, user_id),
            eq(pddAssigns.pddAssigns.pdd_id, param.pdd_id)
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} already has an assignment for PDD with id ${param.pdd_id}, skipping...`
        )
        continue
      }
      const assignment = await this.db
        .insert(pddAssigns.pddAssigns)
        .values({
          user_id,
          pdd_id: param.pdd_id,
          support_document: param.support_document,
          isInternalReviewer: param.isInternalReviewer
        })
        .returning()
        .execute()

      if (assignment.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...assignment[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }
  }

  public async update(id: number, param: any) {
    return await this.db
      .update(this.pddAssign)
      .set(param)
      .where(eq(this.pddAssign.id, id))
      .returning()
  }

  public async getByPDDID(id: number) {
    return await this.db.query.pddAssigns.findMany({
      where: eq(pddAssigns.pddAssigns.pdd_id, id),
      with: {
        user: true
      }
    })
  }
}
