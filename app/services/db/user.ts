import {
  and,
  asc,
  count,
  desc,
  eq,
  gte,
  inArray,
  lte,
  type SQL,
  type SQLWrapper
} from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import { paginate, sortColumn } from "~/components/table/query"
import * as organizations from "~/db/schema/organizations"
import * as schema from "~/db/schema/users"
import type { ERole } from "~/enums/EUserRole"
import { hash, verify } from "~/lib/password"
import type { I<PERSON>reate<PERSON>ser, IGetManyUser, IUser } from "~/types"
import { BaseService } from "."

export default class UserService extends BaseService<typeof schema> {
  private readonly user = schema.users

  public constructor(db: D1Database) {
    super(drizzle(db, { schema }))
  }

  async hasSuper() {
    return await this.db
      .select({ id: this.user.id })
      .from(this.user)
      .where(eq(this.user.isSuper, true))
      .limit(1)
      .then(res => res.length > 0)
  }

  async add(param: ICreateUser) {
    return await this.db
      .insert(this.user)
      .values({
        ...param,
        password: await hash(param.password)
      })
      .returning()
      .then(res => this.first(res))
  }

  public async create(param: any) {
    const password = await hash(param.password)

    const organization = organizations.organizations // <-- Ensure this exists in your schema
    let org_id = param["org.id"]

    if (!org_id) {
      const [org] = await this.db
        .insert(organization)
        .values({
          name: param["org.name"],
          address: param["org.address"] || "",
          province: param["org.province"] || "",
          district: param["org.district"] || null,
          commune: param["org.commune"] || null,
          postal_code: param["org.postal_code"] || null,
          email: param["org.email"] || "",
          phone: param["org.phone"] || "",
          website: param["org.website"] || ""
        })
        .returning({ id: organizations.organizations.id })

      org_id = org.id // Get the newly created organization ID
    }
    const res = await this.db
      .insert(this.user)
      .values({ ...param, password, org_id })
      .returning()
    return res.length > 0 ? res[0] : null
  }
  public async createUserBySecretariat(param: any) {
    const password = await hash(param.password)
    const res = await this.db
      .insert(this.user)
      .values({ ...param, password })
      .returning()
    return res.length > 0 ? res[0] : null
  }
  public async update(id: number, param: Partial<IUser>) {
    const values = {
      ...param,
      password: param.password ? await hash(param.password) : undefined
    }
    const users = await this.db
      .update(this.user)
      .set(values)
      .where(eq(this.user.id, id))
      .returning()
    return users.length > 0 ? users[0] : null
  }

  public async updateUserInformation(id: number, param: Partial<IUser>) {
    const values = {
      ...param
    }
    const users = await this.db
      .update(this.user)
      .set(values)
      .where(eq(this.user.id, id))
      .returning()
    return users.length > 0 ? users[0] : null
  }

  public async updateResetTokenKey(email: string, param: Partial<IUser>) {
    const values = {
      ...param
    }
    const users = await this.db
      .update(this.user)
      .set(values)
      .where(eq(this.user?.email, email))
      .returning()
    return users.length > 0 ? users[0] : null
  }

  public async delete(id: number) {
    const users = await this.db
      .delete(this.user)
      .where(eq(this.user.id, id))
      .returning({ id: this.user.id })
    return users.length > 0 ? users[0] : null
  }

  public async checkEmail(email: string) {
    const res = await this.db
      .select({ id: this.user.id })
      .from(this.user)
      .where(eq(this.user.email, email))
    return res.length > 0
  }

  public async getById(id: number) {
    const user = await this.db.query.users.findFirst({
      where: eq(this.user.id, id)
    })
    return user ? user : null
  }

  public async getByRole(role: ERole) {
    const user = await this.db.query.users.findMany({
      where: eq(this.user.role, role)
    })
    return user ? user : null
  }

  public async getByRoles(roles: ERole[]) {
    const users = await this.db.query.users.findMany({
      where: inArray(this.user.role, roles),
      orderBy: this.user.role
    })
    return users ? users : null
  }

  public async getByEmail(email: string) {
    const user = await this.db.query.users.findFirst({
      where: eq(this.user.email, email)
    })
    return user ? user : null
  }

  public async authenticate(email: string, rawPassword: string) {
    const user = await this.getByEmail(email)
    if (!user) return null
    const match = await verify(rawPassword, user.password)
    const { password, ...safeUser } = user
    return match ? safeUser : null
  }

  public async updatePassword(email: string, password: string) {
    const hashedPassword = await hash(password)
    return await this.db
      .update(this.user)
      .set({ password: hashedPassword })
      .where(eq(this.user.email, email))
      .returning()
  }

  public async getMany(param?: IGetManyUser) {
    const { page = 1, pageSize = 25, sort = "DESC" } = param || {}
    const filters: (SQLWrapper | undefined)[] = [eq(this.user.isSuper, false)]
    if (param?.status) filters.push(eq(this.user.status, param.status))
    if (param?.isAdmin) filters.push(eq(this.user.isAdmin, param.isAdmin))
    if (param?.fromDate) {
      const toDate = param?.toDate ? param.toDate : new Date()
      filters.push(gte(this.user.created_at, param.fromDate))
      filters.push(lte(this.user.created_at, toDate))
    }
    const sortBy = sort === "ASC" ? asc(this.user.id) : desc(this.user.id)
    const table = this.user
    const filter = and(...filters)
    const users = await this.db.query.users.findMany({
      where: filter,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      orderBy: sortBy
    })
    const paginate = await this.getPaginate({ table, filter, page, pageSize })
    return { data: users, paginate }
  }

  //table

  public async paginateTable(option: {
    filter?: SQL
    page: number
    limit: number
    sort: string
  }) {
    const result = await this.db.query.users.findMany({
      ...paginate(option.page, option.limit),
      where: and(option.filter),
      orderBy: sortColumn(option.sort, {
        id: this.user.id,
        name: this.user.firstName,
        created_at: this.user.created_at
      })
    })

    const total = await this.db
      .select({ count: count() })
      .from(this.user)
      .where(option?.filter)

    let _total = 0
    if (total.length > 0) {
      _total = total[0].count
    }
    return { total: _total, result }
  }
}
