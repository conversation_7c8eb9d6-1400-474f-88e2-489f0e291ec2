import {
  and,
  asc,
  count,
  desc,
  eq,
  gte,
  lte,
  not,
  type SQL,
  sql,
  type SQLWrapper
} from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import { paginate, sortColumn } from "~/components/table/query"
import { assigns } from "~/db/schema/assigns"
import * as ideaNotes from "~/db/schema/ideaNotes"
import * as projects from "~/db/schema/projects"
import * as projectSectors from "~/db/schema/projectSectors"
import * as projectTypes from "~/db/schema/projectTypes"
import { requestLONs } from "~/db/schema/requestLON"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"
import type { AuthUser } from "~/types"
import { BaseService } from "."

export default class ProjectService extends BaseService<typeof projects> {
  private readonly project = projects.projects

  public constructor(db: D1Database) {
    super(
      drizzle(db, {
        schema: {
          ...projects,
          ...projectTypes,
          ...projectSectors,
          ...ideaNotes
        }
      })
    )
  }

  public async create(param: any) {
    const ideaNote = ideaNotes.ideaNotes
    let idea_note_id
    const [res] = await this.db
      .insert(ideaNote)
      .values({
        ghg: param["con.ghg"],
        positive: param["con.positive"],
        dev_prio: param["con.dev_prio"],
        total_area: param["con.total_area"],
        estimated_er: param["con.estimated_er"],
        start_date: param["con.start_date"],
        end_date: param["con.end_date"],
        first_date_delivery: param["con.first_date_delivery"],
        technology_tranfer: param["con.technology_transfer"],
        economic_benefit: param["con.economic_benefits"],
        status: EStatus.Draft
      })
      .returning({ id: ideaNotes.ideaNotes.id })
    idea_note_id = res.id
    const [project] = await this.db
      .insert(this.project)
      .values({
        ...param,
        idea_note_id
      })
      .returning()
    return { project }
  }

  public async update(id: number, param: any) {
    const projects = await this.db
      .update(this.project)
      .set(param)
      .where(eq(this.project.id, id))
      .returning()
    return projects.length > 0 ? projects[0] : null
  }

  public async delete(id: number) {
    const projects = await this.db
      .delete(this.project)
      .where(eq(this.project.id, id))
      .returning({ id: this.project.id })
    return projects.length > 0 ? projects[0] : null
  }

  public async getById(id: number) {
    const project = await this.db.query.projects.findFirst({
      where: eq(this.project.id, id),
      with: {
        type: {
          columns: {
            id: true,
            name: true
          }
        },
        sector: {
          columns: {
            id: true,
            name: true,
            type: true
          }
        },
        ideaNote: true
      }
    })

    return project ?? null
  }

  applyRolePermissionFilter = (user: AuthUser | undefined) => {
    const filters: (SQLWrapper | undefined)[] = []

    if (
      user?.role === ERole.Administrator ||
      user?.role === ERole.Secretariat
    ) {
      filters.push(not(eq(this.project.status, "draft")))
    }
    if (
      user?.role === ERole.InternalReviewer ||
      user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.project.id} IN (select project_id from ${assigns} where user_id = ${user.id})`
      )
    }
    if (user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.project.id} IN (select project_id from ${requestLONs} where user_id = ${user.id})`
      )
    }

    return filters
  }

  public async getMany(param?: any) {
    const { page = 1, pageSize = 25, sort = "DESC" } = param || {}
    const filters: (SQLWrapper | undefined)[] = []
    if (param?.status) filters.push(eq(this.project.status, param.status))
    if (param?.fromDate) {
      const toDate = param?.toDate ? param.toDate : new Date()
      filters.push(gte(this.project.created_at, param.fromDate))
      filters.push(lte(this.project.created_at, toDate))
    }
    if (param?.user_id) {
      filters.push(eq(this.project.user_id, param.user_id))
    }

    if (param.user) {
      filters.push(...this.applyRolePermissionFilter(param.user))
    }

    const sortBy = sort === "ASC" ? asc(this.project.id) : desc(this.project.id)
    const table = this.project
    const filter = and(...filters)
    const projects = await this.db.query.projects.findMany({
      where: filter,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      orderBy: sortBy,
      with: {
        type: {
          columns: {
            id: true,
            name: true
          }
        },
        sector: {
          columns: {
            id: true,
            name: true,
            type: true
          }
        }
      }
    })
    const paginate = await this.getPaginate({ table, filter, page, pageSize })
    return { data: projects, paginate }
  }

  // table
  public async paginateTable(option: {
    filter?: SQL
    page: number
    limit: number
    sort: string
    user?: AuthUser
  }) {
    const result = await this.db.query.projects.findMany({
      ...paginate(option.page, option.limit),
      where: and(option.filter, ...this.applyRolePermissionFilter(option.user)),
      orderBy: sortColumn(option.sort, {
        id: projects.projects.id,
        name: projects.projects.name,
        created_at: projects.projects.created_at
      }),
      with: {
        sector: true,
        ideaNote: true
      }
    })

    const total = await this.db
      .select({ count: count() })
      .from(this.project)
      .where(option?.filter)

    let _total = 0
    if (total.length > 0) {
      _total = total[0].count
    }
    return { total: _total, result }
  }
}
