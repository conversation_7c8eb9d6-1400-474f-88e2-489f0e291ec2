import { drizzle } from "drizzle-orm/d1"
import * as implementationAssigns from "~/db/schema/implementationAssigns"
import * as implementations from "~/db/schema/implementations"
import * as users from "~/db/schema/users"
import { BaseService } from "."
import { and, eq } from "drizzle-orm"

export default class ImplementationAssignService extends BaseService<
  typeof implementationAssigns
> {
  private readonly implementationAssign =
    implementationAssigns.implementationAssigns

  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...implementationAssigns, ...users } }))
  }

  public async create(param: {
    user_ids: number[]
    implementation_id: number
    support_document: string
    isInternalReviewer: boolean
  }) {
    const implementationExists = await this.db
      .select()
      .from(implementations.implementations)
      .where(eq(implementations.implementations.id, param.implementation_id))
      .execute()
    if (implementationExists.length === 0) {
      throw new Error(
        `Implementation with id ${param.implementation_id} does not exist`
      )
    }
    const assignments = []

    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()
      if (userExists.length === 0) {
        console.warn(`User with id ${user_id} does not exist, skipping...`)
        continue
      }

      const existingAssignment = await this.db
        .select()
        .from(implementationAssigns.implementationAssigns)
        .where(
          and(
            eq(implementationAssigns.implementationAssigns.user_id, user_id),
            eq(
              implementationAssigns.implementationAssigns.implementation_id,
              param.implementation_id
            )
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} is already assigned to project, skipping...`
        )
        continue
      }

      const assignment = await this.db
        .insert(implementationAssigns.implementationAssigns)
        .values({
          user_id,
          implementation_id: param.implementation_id,
          support_document: param.support_document,
          isInternalReviewer: param.isInternalReviewer
        })
        .returning()
        .execute()

      if (assignment.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...assignment[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }
  }
  public async update(id: number, param: any) {
    const implementationAssigns = await this.db
      .update(this.implementationAssign)
      .set(param)
      .where(eq(this.implementationAssign.id, id))
      .returning()
    return implementationAssigns.length > 0 ? implementationAssigns[0] : null
  }

  public async getByImplementationId(implementation_id: number) {
    return this.db.query.implementationAssigns.findMany({
      where: eq(this.implementationAssign.implementation_id, implementation_id),
      with: {
        user: true
      }
    })
  }
}
