import * as requestLPEs from "~/db/schema/requestLPE"
import { BaseService } from "."
import { drizzle } from "drizzle-orm/d1"
import * as users from "~/db/schema/users"
import * as implementations from "~/db/schema/implementations"
import { and, eq } from "drizzle-orm"

export default class RequestLPEService extends BaseService<typeof requestLPEs> {
  private readonly requestLOA = requestLPEs.requestLPEs

  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...requestLPEs, users, implementations } }))
  }

  public async create(param: {
    user_ids: number[]
    implementation_id: number
  }) {
    const pddExists = await this.db
      .select()
      .from(implementations.implementations)
      .where(eq(implementations.implementations.id, param.implementation_id))
      .execute()

    if (pddExists.length === 0) {
      throw new Error(`PDD with id ${param?.implementation_id} does not exist`)
    }
    const assignments = []
    for (const user_id of param.user_ids) {
      const userExists = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.id, user_id))
        .execute()

      if (userExists.length === 0) {
        console.warn(`User with ${user_id} does not exist, skipping...`)
        continue
      }
      const existingAssignment = await this.db
        .select()
        .from(requestLPEs.requestLPEs)
        .where(
          and(
            eq(requestLPEs.requestLPEs.user_id, user_id),
            eq(
              requestLPEs.requestLPEs.implementation_id,
              param.implementation_id
            )
          )
        )
        .execute()
      if (existingAssignment.length > 0) {
        console.warn(
          `User with id ${user_id} is already assigned to implementaion ${param.implementation_id}, skipping...`
        )
        continue
      }
      const requestLPE = await this.db
        .insert(requestLPEs.requestLPEs)
        .values({
          user_id,
          implementation_id: param.implementation_id
        })
        .returning()
        .execute()

      if (requestLPE.length > 0) {
        const assignedUser = await this.db
          .select()
          .from(users.users)
          .where(eq(users.users.id, user_id))
          .execute()

        assignments.push({
          ...requestLPE[0],
          user: assignedUser.length > 0 ? assignedUser[0] : null
        })
      }
    }
    return assignments
  }
}
