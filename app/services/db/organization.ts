import { eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import * as schema from "~/db/schema/organizations"
import type { IOrganization } from "~/types"
import { BaseService } from "."

export default class OrganizationService extends BaseService<typeof schema> {
  private readonly organization = schema.organizations
  public constructor(db: D1Database) {
    super(drizzle(db, { schema }))
  }

  public async getAllOrganizations() {
    const organizations = await this.db.query.organizations.findMany()
    return organizations ? organizations : null
  }
  public async getById(id: number): Promise<IOrganization | any | null> {
    const organization = await this.db.query.organizations.findFirst({
      where: eq(this.organization.id, id)
    })

    return organization ?? null
  }
  public async checkEmail(email: string) {
    const res = await this.db
      .select({ id: this.organization.id })
      .from(this.organization)
      .where(eq(this.organization.email, email))
    return res.length > 0
  }
}
