import {
  and,
  count,
  eq,
  not,
  type SQL,
  sql,
  type SQLWrapper
} from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import { paginate, sortColumn } from "~/components/table/query"
import { pddAssigns } from "~/db/schema/pddAssigns"
import * as pdds from "~/db/schema/pdds"
import * as projects from "~/db/schema/projects"
import { requestLOAs } from "~/db/schema/requestLOA"
import { ERole } from "~/enums/EUserRole"
import type { AuthUser } from "~/types"
import { BaseService } from "."

export default class PDDService extends BaseService<typeof pdds> {
  private readonly pdd = pdds.pdds
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...pdds, ...projects } }))
  }

  public async create(param: any) {
    return await this.db.insert(this.pdd).values(param).returning()
  }

  public async getById(id: number) {
    const pdd = await this.db.query.pdds.findFirst({
      where: eq(pdds.pdds.id, id),
      with: {
        project: true
      }
    })
    return pdd ? pdd : null
  }
  public async getByProjectID(id: number) {
    const pdd = await this.db.query.pdds.findMany({
      where: eq(pdds.pdds.project_id, id)
    })
    return pdd ? pdd : null
  }
  public async update(id: number, param: any) {
    return await this.db
      .update(this.pdd)
      .set(param)
      .where(eq(this.pdd.id, id))
      .returning()
  }
  public async getMany(param?: any) {
    const filters: (SQLWrapper | undefined)[] = []
    if (
      param?.user?.role === ERole.Administrator ||
      param?.user?.role === ERole.Secretariat
    ) {
      filters.push(not(eq(this.pdd.status, "draft")))
    }
    if (
      param?.user?.role === ERole.InternalReviewer ||
      param?.user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.pdd.id} IN (select pdd_id from ${pddAssigns} where user_id = ${param.user.id})`
      )
    }
    if (param?.user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.pdd.id} IN (select pdd_id from ${requestLOAs} where user_id = ${param.user.id})`
      )
    }
    const filter = and(...filters)
    const pdds = await this.db.query.pdds.findMany({
      where: filter,
      with: {
        project: true
      },
      orderBy: (pdds, { desc }) => desc(pdds.created_at)
    })
    return pdds
  }

  applyRolePermissionFilter = (user: AuthUser | undefined) => {
    const filters: (SQLWrapper | undefined)[] = []
    if (
      user?.role === ERole.Administrator ||
      user?.role === ERole.Secretariat
    ) {
      filters.push(not(eq(this.pdd.status, "draft")))
    }
    if (
      user?.role === ERole.InternalReviewer ||
      user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.pdd.id} IN (select pdd_id from ${pddAssigns} where user_id = ${user.id})`
      )
    }
    if (user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.pdd.id} IN (select pdd_id from ${requestLOAs} where user_id = ${user.id})`
      )
    }
    return filters
  }

  //table
  public async paginateTable(option: {
    filter?: SQL
    page: number
    limit: number
    sort: string
    user?: AuthUser
  }) {
    const result = await this.db.query.pdds.findMany({
      ...paginate(option.page, option.limit),
      where: and(option.filter, ...this.applyRolePermissionFilter(option.user)),
      orderBy: sortColumn(option.sort, {
        id: pdds.pdds.id,
        created_at: pdds?.pdds?.created_at,
        methodology_name: pdds?.pdds?.methodology_name
      })
    })

    const total = await this.db
      .select({ count: count() })
      .from(this.pdd)
      .where(option.filter)
    let _total = 0
    if (total.length > 0) {
      _total = total[0].count
    }
    return { total: _total, result }
  }
}
