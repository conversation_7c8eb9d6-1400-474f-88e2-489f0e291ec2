import { and, count, eq, type SQL, sql, type <PERSON><PERSON>Wrapper } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import { paginate, sortColumn } from "~/components/table/query"
import { implementationAssigns } from "~/db/schema/implementationAssigns"
import * as implementations from "~/db/schema/implementations"
import * as project from "~/db/schema/projects"
import { requestLPEs } from "~/db/schema/requestLPE"
import * as user from "~/db/schema/users"
import { ERole } from "~/enums/EUserRole"
import type { AuthUser } from "~/types"
import { BaseService } from "."

export default class ImplementationService extends BaseService<
  typeof implementations
> {
  private readonly implementation = implementations.implementations
  public constructor(db: D1Database) {
    super(drizzle(db, { schema: { ...implementations, ...project, ...user } }))
  }
  public async create(param: any) {
    return await this.db.insert(this.implementation).values(param).returning()
  }
  public async getById(id: number) {
    const implementation = await this.db.query.implementations.findFirst({
      where: eq(implementations.implementations.id, id),
      with: {
        project: true,
        user: true
      }
    })

    return implementation ? implementation : null
  }

  public async getByProjectId(id: number) {
    const implementation = await this.db.query.implementations.findMany({
      where: eq(implementations.implementations.project_id, id),
      with: {
        user: true
      },
      orderBy: (implementations, { desc }) => desc(implementations.created_at)
    })

    return implementation ? implementation : null
  }

  public async getMany(param?: any) {
    const filters: (SQLWrapper | undefined)[] = []
    if (
      param?.user?.role === ERole.InternalReviewer ||
      param?.user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.implementation.id} IN (select implementation_id from ${implementationAssigns} where user_id = ${param.user.id})`
      )
    }
    if (param?.user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.implementation.id} IN (select implementation_id from ${requestLPEs} where user_id = ${param.user.id})`
      )
    }
    const filter = and(...filters)
    const implementations = await this.db.query.implementations.findMany({
      where: filter,
      with: {
        project: true
      },
      orderBy: (implementations, { desc }) => desc(implementations.created_at)
    })
    return implementations
  }

  //Table

  applyRolePermissionFilter = (user: AuthUser | undefined) => {
    const filters: (SQLWrapper | undefined)[] = []
    if (
      user?.role === ERole.InternalReviewer ||
      user?.role === ERole.IndependentAuditor
    ) {
      filters.push(
        sql`${this.implementation.id} IN (select implementation_id from ${implementationAssigns} where user_id = ${user.id})`
      )
    }
    if (user?.role === ERole.Coordinator) {
      filters.push(
        sql`${this.implementation.id} IN (select implementation_id from ${requestLPEs} where user_id = ${user.id})`
      )
    }
    return filters
  }

  public async paginateTable(option: {
    filter?: SQL
    page: number
    limit: number
    sort: string
    user?: AuthUser
  }) {
    const result = await this.db.query.implementations.findMany({
      ...paginate(option.page, option.limit),
      where: and(option.filter, ...this.applyRolePermissionFilter(option.user)),
      with: {
        project: true
      },
      orderBy: sortColumn(option.sort, {
        id: implementations.implementations.id
      })
    })
    const total = await this.db
      .select({ count: count() })
      .from(this.implementation)
      .where(option.filter)
    let _total = 0
    if (total.length > 0) {
      _total = total[0].count
    }
    return { total: _total, result }
  }

  public async update(id: number, param: any) {
    return await this.db
      .update(this.implementation)
      .set(param)
      .where(eq(this.implementation.id, id))
      .returning()
  }
}
