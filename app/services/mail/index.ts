import { Resend } from "resend"
import type { CloudflareENV } from "~/types"
import { OTPService } from "../otp"
import mailTemplate from "./template"

export type UserBasic = {
  id: number
  email: string
  firstName: string
  lastName: string
}

interface ISendLink {
  user: UserBasic
  link: string
}

export class MailService {
  private readonly siteName = "Cambodia Carbon Registry"
  private env: CloudflareENV

  constructor(env: CloudflareENV) {
    this.env = env
  }

  private async send(to: string[], subject: string, html: string) {
    const resend = new Resend(this.env.RESEND_API_KEY)
    const from = this.env.RESEND_FROM_EMAIL
    return await resend.emails.send({ from, to, subject, html })
  }

  async sendOtpEmail(email: string) {
    const code = await new OTPService(this.env).generate()
    const subject = `Your ${this.siteName} account code`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>Thanks for creating an account with ${this.siteName}. \
      In order to complete your account registration, you’ll first need to confirm your email \
      by using the follow Code.</p> \
      <p style="font-size: 5em; font-weight: bold; margin: 3em 0;">${code}</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }
  async UserAccountHasBeenApproveRejectEmailNotification(
    email: string,
    active: boolean
  ) {
    const subject = `Your ${this.siteName} account`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>Thanks for creating an account with ${this.siteName}. \
      ${
        active
          ? "We are happy to inform you that your account has been <b>Approved</b>"
          : "We are sorry to inform you that your account has been <b>Rejected</b>"
      }.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async NewUserRegisterEmailNotification(email: string, url: string) {
    const subject = `Your ${this.siteName} account`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>There is a new register an account on ${this.siteName}. \
      <a href=${url}>Click here</a> to view the details.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async NewProjectSubmitEmailNotification(email: string, url: string) {
    const subject = `Your ${this.siteName} account`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>There is a new project has been submiited ${this.siteName}. \
      <a href=${url}>Click here</a> to view the details.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async ProjectHasBeenReviewEmailNotification(email: string) {
    const subject = `Your ${this.siteName} account`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>Thanks for submitted a project with ${this.siteName}. \
     We inform you that your project has been <b>Under Review</b>     
     .</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async ProjectHasBeenApproveRejectEmailNotification(
    email: string,
    active: boolean
  ) {
    const subject = `Your ${this.siteName} account`
    const header = `<h1>${this.siteName}</h1>`
    const body = `
    <p>${
      active
        ? "We are happy to inform you that your project has been <b>Approved</b>"
        : "We are sorry to inform you that your project has been <b>Rejected</b>"
    }.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }
  async AssignReviewersEmailNotification(email: string, url: string) {
    const subject = `Assign Reviewer`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>There is a new project has been assigned to you. \
      <a href=${url}>Click here</a> to review the project.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async AssigneeApproveRejectEmailNotification(
    email: string,
    url: string,
    active: boolean
  ) {
    const subject = `Internal Reviewer ${active ? "Approved" : "Rejected"}`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>Internal Reviewer has ${
      active ? "Approved" : "Rejected"
    } the project that you have been assigned. \
      <a href=${url}>Click here</a> to review the project.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }

  async ResetPasswordEmailNotification(email: string, url: string) {
    const subject = `Reset Password`
    const header = `<h1>${this.siteName}</h1>`
    const body = `<p>Please Click the link below to Reset Password \
      <a href=${url}>Click here</a> to reset the password.</p>`
    const footer = `<p>Thanks,<br />${this.siteName} team</p>`
    const html = mailTemplate({ header, body, footer })
    return await this.send([email], subject, html)
  }
}
