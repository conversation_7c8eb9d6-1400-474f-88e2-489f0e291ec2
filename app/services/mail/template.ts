interface Props {
  header: string
  body: string
  footer: string
}

export default function mailTemplate({ header, body, footer }: Props) {
  return `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <style>
    @media only screen and (max-width: 600px) {
      .container {
        margin: 1rem;
      }
    }
    @media only screen and (min-width: 600px) {
      .container {
        margin: 5rem auto;
      }
    }
  </style>
</head>

<body style="margin: 0; padding: 0; font-size: large; font-family:'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;">
  <div class="container" style="max-width: 40rem; background: white;">
    <div style="border-bottom: 1px solid #DDD; margin-top: 1rem; margin-bottom: 2rem;"></div>
    <div>${header}</div>
    <div>${body}</div>
    <div>${footer}</div>
  </div>
</body>
</html>`
}
