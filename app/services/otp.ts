import { generateTOTP, verifyTOTP } from "@epic-web/totp"
import type { CloudflareENV } from "~/types"

export class OTPService {
  private env: CloudflareENV

  constructor(env: CloudflareENV) {
    this.env = env
  }

  async generate() {
    const otp = await generateTOTP({ secret: this.env.SECRET, period: 180 })
    return otp.otp
  }

  async verify(code: string): Promise<boolean> {
    const delta = await verifyTOTP({
      otp: code,
      secret: this.env.SECRET,
      period: 180
    })
    return delta !== null
  }
}
