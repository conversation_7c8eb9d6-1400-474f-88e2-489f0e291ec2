import {
  createCookieSessionStorage,
  type Session,
  type SessionStorage
} from "@remix-run/cloudflare"
import { Authenticator } from "remix-auth"
import { FormStrategy } from "remix-auth-form"
import { EStatus } from "~/enums/EStatus"
import type { AuthUser, CloudflareENV } from "~/types"
import UserService from "./db/user"

export function getSessionStorage(env: CloudflareENV) {
  return createCookieSessionStorage({
    cookie: {
      name: "_session",
      sameSite: "lax",
      maxAge: 604800, // seconds (7days)
      path: "/",
      httpOnly: true,
      secrets: [env.SECRET],
      secure: env.WORKER_ENV === "production"
    }
  })
}

export function getAuthenticator(env: CloudflareENV) {
  const sessionStorage = getSessionStorage(env)
  const db = env.DB
  const authenticator = new Authenticator<AuthUser>(sessionStorage)
  const strategy = new FormStrategy(async ({ form }) => {
    const email = form.get("email")?.toString()
    const password = form.get("password")?.toString()
    if (!email) throw new Error("Email is required")
    if (!password) throw new Error("Password is required")
    const authUser = await new UserService(db).authenticate(email, password)
    if (authUser?.active === false && authUser?.status === EStatus.Review)
      throw new Error("Account is not yet approve")
    if (authUser?.active === false && authUser?.status === EStatus.Reject)
      throw new Error("Account is disactive")
    if (!authUser) throw new Error("Login is incorrect")
    return authUser
  })
  authenticator.use(strategy, "user-pass")
  return { authenticator, sessionStorage }
}

export async function setCookie(storage: SessionStorage, session: Session) {
  return { "Set-Cookie": await storage.commitSession(session) }
}

export async function getCookie(request: Request, storage: SessionStorage) {
  return await storage.getSession(request.headers.get("Cookie"))
}
