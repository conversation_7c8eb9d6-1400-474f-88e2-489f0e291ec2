CREATE TABLE `assigns` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`project_id` integer NOT NULL,
	`support_document` text NOT NULL,
	`status` text DEFAULT 'pending',
	`review_document` text,
	`isInternalReviewer` integer NOT NULL,
	`assigned_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `comments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer,
	`project_id` integer,
	`comment` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOR<PERSON><PERSON><PERSON> KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOR<PERSON><PERSON><PERSON> KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `idea_notes` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`ghg` integer,
	`positive` text,
	`dev_prio` text,
	`start_date` text,
	`end_date` text,
	`first_date_delivery` text,
	`technology_transfer` text,
	`economic_benefit` text,
	`status` text,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer
);
--> statement-breakpoint
CREATE TABLE `implementation_assigns` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`implementation_id` integer NOT NULL,
	`support_document` text NOT NULL,
	`review_document` text,
	`status` text DEFAULT 'pending',
	`isInternalReviewer` integer NOT NULL,
	`assigned_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`implementation_id`) REFERENCES `implementations`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `implementations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer,
	`project_id` integer,
	`co2` integer NOT NULL,
	`share_co2` integer NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`type_of_carbon_mechanism` text NOT NULL,
	`other_type_of_carbon_mechanism` text,
	`user_of_ghg_ers` text NOT NULL,
	`carbon_credit` integer NOT NULL,
	`status` text DEFAULT 'LPE_Request' NOT NULL,
	`review_implementation_document` text,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `notifications` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`type` text NOT NULL,
	`user_id` integer,
	`data` integer NOT NULL,
	`link` text,
	`active` integer DEFAULT false NOT NULL,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `organizations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`address` text NOT NULL,
	`province` text NOT NULL,
	`commune` text,
	`district` text,
	`postal_code` text,
	`email` text NOT NULL,
	`phone` text NOT NULL,
	`website` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer
);
--> statement-breakpoint
CREATE TABLE `pdd_assigns` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`pdd_id` integer NOT NULL,
	`support_document` text NOT NULL,
	`review_document` text,
	`status` text DEFAULT 'pending',
	`isInternalReviewer` integer NOT NULL,
	`assigned_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`pdd_id`) REFERENCES `pdds`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `pdds` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`project_id` integer,
	`positive_list` text NOT NULL,
	`share_ghg_ers` text,
	`start_date` text,
	`end_date` text,
	`type_of_carbon_mechanism` text,
	`other_type_of_carbon_mechanism` text,
	`user_of_ghg_ers` text,
	`environmental_integrity_detail` text,
	`status` text DEFAULT 'draft' NOT NULL,
	`review_pdd_document` text,
	`reject_reason` text,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `project_sectors` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`description` text,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer
);
--> statement-breakpoint
CREATE TABLE `project_types` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`sector_id` integer,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`sector_id`) REFERENCES `project_sectors`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `projects` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`org_id` integer,
	`sector_id` integer,
	`type_id` integer,
	`user_id` integer,
	`idea_note_id` integer,
	`uid` text,
	`name` text,
	`country` text,
	`province` text,
	`city` text,
	`total_project_area` text,
	`lat` real,
	`lng` real,
	`description` text,
	`status` text DEFAULT 'draft' NOT NULL,
	`location` text,
	`review_project_document` text,
	`reject_reason` text,
	`cover_img` text,
	`start_date` integer,
	`end_date` integer,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`org_id`) REFERENCES `organizations`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`sector_id`) REFERENCES `project_sectors`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`type_id`) REFERENCES `project_types`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`idea_note_id`) REFERENCES `idea_notes`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `requestLOAs` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer,
	`pdd_id` integer,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`pdd_id`) REFERENCES `pdds`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `requestLONs` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer,
	`project_id` integer,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `requestLPEs` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer,
	`implementation_id` integer,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`implementation_id`) REFERENCES `implementations`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`org_id` integer,
	`accountType` text,
	`job_title` text,
	`firstName` text NOT NULL,
	`lastName` text NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`nationality` text,
	`passport_national_id` text,
	`phone` text,
	`photoUrl` text,
	`national_id_passport_document` text,
	`business_registration_document` text,
	`organization_authorization_letter_document` text,
	`status` text DEFAULT 'review' NOT NULL,
	`role` text DEFAULT 'project_developer',
	`active` integer DEFAULT false NOT NULL,
	`isSuper` integer DEFAULT false NOT NULL,
	`isAdmin` integer DEFAULT false NOT NULL,
	`resetTokenKey` text,
	`created_at` integer DEFAULT (unixepoch()*1000) NOT NULL,
	`updated_at` integer,
	FOREIGN KEY (`org_id`) REFERENCES `organizations`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `organizations_email_unique` ON `organizations` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);