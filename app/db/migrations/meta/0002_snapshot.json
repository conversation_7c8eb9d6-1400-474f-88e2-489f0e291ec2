{"version": "6", "dialect": "sqlite", "id": "00ef57e8-58f5-403b-88d8-9bd7d5270994", "prevId": "762931be-79a1-4c6f-b22d-cb6e60520161", "tables": {"assigns": {"name": "assigns", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "support_document": {"name": "support_document", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "review_document": {"name": "review_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isInternalReviewer": {"name": "isInternalReviewer", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "assigned_at": {"name": "assigned_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}}, "indexes": {}, "foreignKeys": {"assigns_user_id_users_id_fk": {"name": "assigns_user_id_users_id_fk", "tableFrom": "assigns", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assigns_project_id_projects_id_fk": {"name": "assigns_project_id_projects_id_fk", "tableFrom": "assigns", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"comments_user_id_users_id_fk": {"name": "comments_user_id_users_id_fk", "tableFrom": "comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "comments_project_id_projects_id_fk": {"name": "comments_project_id_projects_id_fk", "tableFrom": "comments", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "idea_notes": {"name": "idea_notes", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "ghg": {"name": "ghg", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "positive": {"name": "positive", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "dev_prio": {"name": "dev_prio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_area": {"name": "total_area", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "estimated_er": {"name": "estimated_er", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_date_delivery": {"name": "first_date_delivery", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "technology_transfer": {"name": "technology_transfer", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "economic_benefit": {"name": "economic_benefit", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "implementation_assigns": {"name": "implementation_assigns", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "implementation_id": {"name": "implementation_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "support_document": {"name": "support_document", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "review_document": {"name": "review_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "isInternalReviewer": {"name": "isInternalReviewer", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "assigned_at": {"name": "assigned_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}}, "indexes": {}, "foreignKeys": {"implementation_assigns_user_id_users_id_fk": {"name": "implementation_assigns_user_id_users_id_fk", "tableFrom": "implementation_assigns", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "implementation_assigns_implementation_id_implementations_id_fk": {"name": "implementation_assigns_implementation_id_implementations_id_fk", "tableFrom": "implementation_assigns", "tableTo": "implementations", "columnsFrom": ["implementation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "implementations": {"name": "implementations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "co2": {"name": "co2", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "share_co2": {"name": "share_co2", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type_of_carbon_mechanism": {"name": "type_of_carbon_mechanism", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "other_type_of_carbon_mechanism": {"name": "other_type_of_carbon_mechanism", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_of_ghg_ers": {"name": "user_of_ghg_ers", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "carbon_credit": {"name": "carbon_credit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'LPE_Request'"}, "review_implementation_document": {"name": "review_implementation_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"implementations_user_id_users_id_fk": {"name": "implementations_user_id_users_id_fk", "tableFrom": "implementations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "implementations_project_id_projects_id_fk": {"name": "implementations_project_id_projects_id_fk", "tableFrom": "implementations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "notifications": {"name": "notifications", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "data": {"name": "data", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "organizations": {"name": "organizations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "province": {"name": "province", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "commune": {"name": "commune", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "district": {"name": "district", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"organizations_email_unique": {"name": "organizations_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "pdd_assigns": {"name": "pdd_assigns", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "pdd_id": {"name": "pdd_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "support_document": {"name": "support_document", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "review_document": {"name": "review_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "isInternalReviewer": {"name": "isInternalReviewer", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "assigned_at": {"name": "assigned_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}}, "indexes": {}, "foreignKeys": {"pdd_assigns_user_id_users_id_fk": {"name": "pdd_assigns_user_id_users_id_fk", "tableFrom": "pdd_assigns", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pdd_assigns_pdd_id_pdds_id_fk": {"name": "pdd_assigns_pdd_id_pdds_id_fk", "tableFrom": "pdd_assigns", "tableTo": "pdds", "columnsFrom": ["pdd_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "pdds": {"name": "pdds", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "lat": {"name": "lat", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "lng": {"name": "lng", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "kml_file": {"name": "kml_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "methodology_name": {"name": "methodology_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "methodology_link": {"name": "methodology_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "baseline_scenario": {"name": "baseline_scenario", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_scenario": {"name": "project_scenario", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "baseline_emission_factor": {"name": "baseline_emission_factor", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "annual_emission_reduction": {"name": "annual_emission_reduction", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "lifetime_emission_reduction": {"name": "lifetime_emission_reduction", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "leakage_assessment": {"name": "leakage_assessment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "additionally_demonstration": {"name": "additionally_demonstration", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "baseline_emission_methodology_file": {"name": "baseline_emission_methodology_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "baseline_emission_data_file": {"name": "baseline_emission_data_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "carbon_stock_assessment_file": {"name": "carbon_stock_assessment_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "leakage_assessment_file": {"name": "leakage_assessment_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "additionality_assessment_file": {"name": "additionality_assessment_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "monitoring_plan": {"name": "monitoring_plan", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "monitoring_frequency": {"name": "monitoring_frequency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_method": {"name": "tool_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "detailed_monitoring_plan_file": {"name": "detailed_monitoring_plan_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "community_involvement": {"name": "community_involvement", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stakeholder_consultations": {"name": "stakeholder_consultations", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "community_consultation_records_file": {"name": "community_consultation_records_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "environmental_impact_assessment": {"name": "environmental_impact_assessment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "social_impact_assessment": {"name": "social_impact_assessment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "risk_mitigation_measures": {"name": "risk_mitigation_measures", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_development_cost": {"name": "project_development_cost", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "operational_cost": {"name": "operational_cost", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expected_revenue": {"name": "expected_revenue", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "funding_source": {"name": "funding_source", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "review_pdd_document": {"name": "review_pdd_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reject_reason": {"name": "reject_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "step": {"name": "step", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"pdds_project_id_projects_id_fk": {"name": "pdds_project_id_projects_id_fk", "tableFrom": "pdds", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "project_sectors": {"name": "project_sectors", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "project_types": {"name": "project_types", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sector_id": {"name": "sector_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"project_types_sector_id_project_sectors_id_fk": {"name": "project_types_sector_id_project_sectors_id_fk", "tableFrom": "project_types", "tableTo": "project_sectors", "columnsFrom": ["sector_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "projects": {"name": "projects", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "org_id": {"name": "org_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sector_id": {"name": "sector_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "type_id": {"name": "type_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "idea_note_id": {"name": "idea_note_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "province": {"name": "province", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_project_area": {"name": "total_project_area", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "lat": {"name": "lat", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "lng": {"name": "lng", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "review_project_document": {"name": "review_project_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reject_reason": {"name": "reject_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_img": {"name": "cover_img", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "kml_file": {"name": "kml_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_step": {"name": "project_step", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'project-information'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"projects_org_id_organizations_id_fk": {"name": "projects_org_id_organizations_id_fk", "tableFrom": "projects", "tableTo": "organizations", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_sector_id_project_sectors_id_fk": {"name": "projects_sector_id_project_sectors_id_fk", "tableFrom": "projects", "tableTo": "project_sectors", "columnsFrom": ["sector_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_type_id_project_types_id_fk": {"name": "projects_type_id_project_types_id_fk", "tableFrom": "projects", "tableTo": "project_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_user_id_users_id_fk": {"name": "projects_user_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_idea_note_id_idea_notes_id_fk": {"name": "projects_idea_note_id_idea_notes_id_fk", "tableFrom": "projects", "tableTo": "idea_notes", "columnsFrom": ["idea_note_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "requestLOAs": {"name": "requestLOAs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "pdd_id": {"name": "pdd_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"requestLOAs_user_id_users_id_fk": {"name": "requestLOAs_user_id_users_id_fk", "tableFrom": "requestLOAs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requestLOAs_pdd_id_pdds_id_fk": {"name": "requestLOAs_pdd_id_pdds_id_fk", "tableFrom": "requestLOAs", "tableTo": "pdds", "columnsFrom": ["pdd_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "requestLONs": {"name": "requestLONs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"requestLONs_user_id_users_id_fk": {"name": "requestLONs_user_id_users_id_fk", "tableFrom": "requestLONs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requestLONs_project_id_projects_id_fk": {"name": "requestLONs_project_id_projects_id_fk", "tableFrom": "requestLONs", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "requestLPEs": {"name": "requestLPEs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "implementation_id": {"name": "implementation_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"requestLPEs_user_id_users_id_fk": {"name": "requestLPEs_user_id_users_id_fk", "tableFrom": "requestLPEs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requestLPEs_implementation_id_implementations_id_fk": {"name": "requestLPEs_implementation_id_implementations_id_fk", "tableFrom": "requestLPEs", "tableTo": "implementations", "columnsFrom": ["implementation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "org_id": {"name": "org_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "accountType": {"name": "accountType", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "passport_national_id": {"name": "passport_national_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "photoUrl": {"name": "photoUrl", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "national_id_passport_document": {"name": "national_id_passport_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "business_registration_document": {"name": "business_registration_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "organization_authorization_letter_document": {"name": "organization_authorization_letter_document", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'review'"}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'project_developer'"}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "isSuper": {"name": "is<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "isAdmin": {"name": "isAdmin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "resetTokenKey": {"name": "resetToken<PERSON>ey", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch()*1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {"users_org_id_organizations_id_fk": {"name": "users_org_id_organizations_id_fk", "tableFrom": "users", "tableTo": "organizations", "columnsFrom": ["org_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}