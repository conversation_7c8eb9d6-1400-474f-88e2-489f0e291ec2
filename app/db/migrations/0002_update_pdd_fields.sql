ALTER TABLE `pdds` ADD `address` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `lat` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `lng` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `kml_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `methodology_name` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `methodology_link` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `version` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `baseline_scenario` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `project_scenario` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `baseline_emission_factor` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `annual_emission_reduction` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `lifetime_emission_reduction` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `leakage_assessment` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `additionally_demonstration` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `baseline_emission_methodology_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `baseline_emission_data_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `carbon_stock_assessment_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `leakage_assessment_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `additionality_assessment_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `monitoring_plan` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `monitoring_frequency` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `tool_method` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `detailed_monitoring_plan_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `community_involvement` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `stakeholder_consultations` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `community_consultation_records_file` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `environmental_impact_assessment` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `social_impact_assessment` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `risk_mitigation_measures` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `project_development_cost` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `operational_cost` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `expected_revenue` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `funding_source` text;--> statement-breakpoint
ALTER TABLE `pdds` ADD `step` text;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `positive_list`;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `share_ghg_ers`;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `type_of_carbon_mechanism`;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `other_type_of_carbon_mechanism`;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `user_of_ghg_ers`;--> statement-breakpoint
ALTER TABLE `pdds` DROP COLUMN `environmental_integrity_detail`;