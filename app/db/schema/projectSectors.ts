import { sql } from "drizzle-orm"
import {
  type AnySQLiteColumn,
  integer,
  sqliteTable,
  text
} from "drizzle-orm/sqlite-core"

export const projectSectors = sqliteTable("project_sectors", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  abbr: text("abbr").notNull(),
  parentId: integer("parent_id").references(
    (): AnySQLiteColumn => projectSectors.id
  ),
  description: text("description"),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
