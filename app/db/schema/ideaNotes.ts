import { sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"

export const ideaNotes = sqliteTable("idea_notes", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  ghg: integer("ghg"),
  positive: text("positive"),
  dev_prio: text("dev_prio"),
  total_area: text("total_area"),
  estimated_er: text("estimated_er"),
  start_date: text("start_date"),
  end_date: text("end_date"),
  first_date_delivery: text("first_date_delivery"),
  technology_tranfer: text("technology_transfer"),
  economic_benefit: text("economic_benefit"),
  status: text("status", {
    enum: ["pending", "approved", "rejected", "draft"]
  }),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
