import { integer, sqliteTable } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { sql } from "drizzle-orm"
import { pdds } from "./pdds"

export const requestLOAs = sqliteTable("requestLOAs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id").references(() => users.id),
  pdd_id: integer("pdd_id").references(() => pdds.id),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
