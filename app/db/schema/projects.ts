import { relations, sql } from "drizzle-orm"
import { integer, real, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { organizations } from "./organizations"
import { projectSectors } from "./projectSectors"
import { projectTypes } from "./projectTypes"
import { users } from "./users"
import { ideaNotes } from "./ideaNotes"
import { Project_STEPS } from "~/enums/EProjectStep"
import { EProjectStatus } from "~/enums/EProjectStatus"

export const projects = sqliteTable("projects", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  org_id: integer("org_id").references(() => organizations.id),
  sector_id: integer("sector_id").references(() => projectSectors.id),
  type_id: integer("type_id").references(() => projectTypes.id),
  user_id: integer("user_id").references(() => users.id),
  idea_note_id: integer("idea_note_id").references(() => ideaNotes.id),
  uid: text("uid"),
  name: text("name"),
  country: text("country"),
  province: text("province"),
  city: text("city"),
  total_project_area: text("total_project_area"),
  lat: real("lat"),
  lng: real("lng"),
  description: text("description"),
  address: text("address"),
  status: text("status", {
    enum: [
      EProjectStatus.Draft,
      EProjectStatus.PendingLnoRequest,
      EProjectStatus.PendingReviewLnoRequest,
      EProjectStatus.LnoIssuingRequest,
      EProjectStatus.SignedLNO,
      EProjectStatus.Submitted,
      EProjectStatus.UnderReview,
      EProjectStatus.Approved,
      EProjectStatus.Rejected,
      EProjectStatus.Verified,
      EProjectStatus.PendingLOARequest,
      EProjectStatus.InvalidLOARequest,
      EProjectStatus.ReviewedPDD,
      EProjectStatus.PendingIssuingLOARequest,
      EProjectStatus.SignedLOA,
      EProjectStatus.RejectedLOARequestModification
    ]
  })
    .notNull()
    .default(EProjectStatus.Draft),
  location: text("location"),
  review_project_document: text("review_project_document"),
  reject_reason: text("reject_reason"),
  cover_img: text("cover_img"),
  kml_file: text("kml_file"),
  project_step: text("project_step", {
    enum: ["project-information", "project-alignments", "lno-request"]
  })
    .notNull()
    .default(Project_STEPS.PROJECT_INFORMATION),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})

export const projectsRelations = relations(projects, ({ one }) => ({
  type: one(projectTypes, {
    fields: [projects.type_id],
    references: [projectTypes.id]
  }),
  sector: one(projectSectors, {
    fields: [projects.sector_id],
    references: [projectSectors.id]
  }),
  ideaNote: one(ideaNotes, {
    fields: [projects.idea_note_id], // Project's Primary Key
    references: [ideaNotes.id] // Links to Concept Note's Foreign Key
  })
}))
