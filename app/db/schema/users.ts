import { sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { organizations } from "~/db/schema/organizations"
import { EAccountType } from "~/enums/EAccountType"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"

export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  org_id: integer("org_id").references(() => organizations.id),
  accountType: text("accountType", {
    enum: [
      EAccountType.GovernmentEnity,
      EAccountType.ProjectDeveloper,
      EAccountType.ThirdPartyVerifier
    ]
  }),
  job_title: text("job_title"),
  firstName: text("firstName").notNull(),
  lastName: text("lastName").notNull(),
  email: text("email").unique().notNull(),
  password: text("password").notNull(),
  nationality: text("nationality"),
  passport_national_id: text("passport_national_id"),
  phone: text("phone"),
  photoUrl: text("photoUrl"),
  national_id_passport_document: text("national_id_passport_document"),
  business_registration_document: text("business_registration_document"),
  organization_authorization_letter_document: text(
    "organization_authorization_letter_document"
  ),
  status: text("status", {
    enum: [EStatus.Approve, EStatus.Reject, EStatus.Review]
  })
    .notNull()
    .default(EStatus.Review),
  role: text("role", {
    enum: [
      ERole.Administrator,
      ERole.IndependentAuditor,
      ERole.InternalReviewer,
      ERole.Secretariat,
      ERole.ProjectDeveloper,
      ERole.Coordinator
    ]
  }).default(ERole.ProjectDeveloper),
  active: integer("active", { mode: "boolean" }).notNull().default(false),
  isSuper: integer("isSuper", { mode: "boolean" }).notNull().default(false),
  isAdmin: integer("isAdmin", { mode: "boolean" }).notNull().default(false),
  resetTokenKey: text("resetTokenKey"),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
