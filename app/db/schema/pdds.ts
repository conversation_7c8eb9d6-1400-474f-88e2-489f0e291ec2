import { relations, sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { projects } from "./projects"

export const pdds = sqliteTable("pdds", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  project_id: integer("project_id").references(() => projects.id),
  address: text("address"),
  lat: text("lat"),
  lng: text("lng"),
  kml_file: text("kml_file"),
  start_date: text("start_date"),
  end_date: text("end_date"),
  methodology_name: text("methodology_name"),
  methodology_link: text("methodology_link"),
  version: text("version"),
  baseline_scenario: text("baseline_scenario"),
  project_scenario: text("project_scenario"),
  baseline_emission_factor: text("baseline_emission_factor"),
  annual_emission_reduction: text("annual_emission_reduction"),
  lifetime_emission_reduction: text("lifetime_emission_reduction"),
  leakage_assessment: text("leakage_assessment"),
  additionally_demonstration: text("additionally_demonstration"),
  baseline_emission_methodology_file: text(
    "baseline_emission_methodology_file"
  ),
  baseline_emission_data_file: text("baseline_emission_data_file"),
  carbon_stock_assessment_file: text("carbon_stock_assessment_file"),
  leakage_assessment_file: text("leakage_assessment_file"),
  additionality_assessment_file: text("additionality_assessment_file"),
  monitoring_plan: text("monitoring_plan"),
  monitoring_frequency: text("monitoring_frequency"),
  tool_method: text("tool_method"),
  detailed_monitoring_plan_file: text("detailed_monitoring_plan_file"),
  community_involvement: text("community_involvement"),
  stakeholder_consultations: text("stakeholder_consultations"),
  community_consultation_records_file: text(
    "community_consultation_records_file"
  ),
  environmental_impact_assessment: text("environmental_impact_assessment"),
  social_impact_assessment: text("social_impact_assessment"),
  risk_mitigation_measures: text("risk_mitigation_measures"),
  project_development_cost: text("project_development_cost"),
  operational_cost: text("operational_cost"),
  expected_revenue: text("expected_revenue"),
  funding_source: text("funding_source"),
  status: text("status", {
    enum: ["draft", "submitted", "pending", "approved", "rejected"]
  })
    .notNull()
    .default("draft"),
  review_pdd_document: text("review_pdd_document"),
  reject_reason: text("reject_reason"),
  step: text("step"),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})

export const pddsRelations = relations(pdds, ({ one }) => ({
  project: one(projects, {
    fields: [pdds.project_id],
    references: [projects.id]
  })
}))
