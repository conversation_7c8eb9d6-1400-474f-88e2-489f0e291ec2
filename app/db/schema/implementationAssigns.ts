import { relations, sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { implementations } from "./implementations"

export const implementationAssigns = sqliteTable("implementation_assigns", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id")
    .references(() => users.id)
    .notNull(),
  implementation_id: integer("implementation_id")
    .references(() => implementations.id)
    .notNull(),
  support_document: text("support_document").notNull(),
  review_document: text("review_document"),
  status: text("status", {
    enum: ["pending", "approved", "reject"]
  }).default("pending"),
  isInternalReviewer: integer("isInternalReviewer", {
    mode: "boolean"
  }).notNull(),
  implementation_assigned_at: integer("assigned_at", { mode: "timestamp_ms" }) // Optional: Track when the assignment happened
    .notNull()
    .default(sql`(unixepoch()*1000)`)
})

export const implementationAssignsRelations = relations(
  implementationAssigns,
  ({ one }) => ({
    user: one(users, {
      fields: [implementationAssigns.user_id],
      references: [users.id]
    }),
    implementation: one(implementations, {
      fields: [implementationAssigns.implementation_id],
      references: [implementations.id]
    })
  })
)
