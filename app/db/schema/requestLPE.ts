import { integer, sqliteTable } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { sql } from "drizzle-orm"
import { implementations } from "./implementations"

export const requestLPEs = sqliteTable("requestLPEs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id").references(() => users.id),
  implementation_id: integer("implementation_id").references(
    () => implementations.id
  ),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
