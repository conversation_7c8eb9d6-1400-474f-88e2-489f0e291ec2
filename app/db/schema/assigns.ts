import { relations, sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { projects } from "./projects"
import { users } from "./users"

export const assigns = sqliteTable("assigns", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id")
    .references(() => users.id)
    .notNull(),
  project_id: integer("project_id")
    .references(() => projects.id)
    .notNull(),
  support_document: text("support_document").notNull(),
  status: text("status", {
    enum: ["pending", "approved", "rejected"]
  }).default("pending"),
  review_document: text("review_document"),
  isInternalReviewer: integer("isInternalReviewer", {
    mode: "boolean"
  }).notNull(),
  assigned_at: integer("assigned_at", { mode: "timestamp_ms" }) // Optional: Track when the assignment happened
    .notNull()
    .default(sql`(unixepoch()*1000)`)
})

export const assignsRelations = relations(assigns, ({ one }) => ({
  user: one(users, {
    fields: [assigns.user_id],
    references: [users.id]
  }),
  project: one(projects, {
    fields: [assigns.project_id],
    references: [projects.id]
  })
}))
