import { relations, sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { pdds } from "./pdds"

export const pddAssigns = sqliteTable("pdd_assigns", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id")
    .references(() => users.id)
    .notNull(),
  pdd_id: integer("pdd_id")
    .references(() => pdds.id)
    .notNull(),
  support_document: text("support_document").notNull(),
  review_document: text("review_document"),
  status: text("status", {
    enum: ["pending", "approved", "rejected"]
  }).default("pending"),
  isInternalReviewer: integer("isInternalReviewer", {
    mode: "boolean"
  }).notNull(),
  assigned_at: integer("assigned_at", { mode: "timestamp_ms" }) // Optional: Track when the assignment happened
    .notNull()
    .default(sql`(unixepoch()*1000)`),

  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`)
})

export const pddAssignsRelations = relations(pddAssigns, ({ one }) => ({
  user: one(users, {
    fields: [pddAssigns.user_id],
    references: [users.id]
  }),
  pdd: one(pdds, {
    fields: [pddAssigns.pdd_id],
    references: [pdds.id]
  })
}))
