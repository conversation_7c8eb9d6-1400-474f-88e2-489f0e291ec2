import { integer, sqliteTable } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { projects } from "./projects"
import { sql } from "drizzle-orm"

export const requestLONs = sqliteTable("requestLONs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id").references(() => users.id),
  project_id: integer("project_id").references(() => projects.id),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})
