import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { projects } from "./projects"
import { relations, sql } from "drizzle-orm"

export const implementations = sqliteTable("implementations", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  user_id: integer("user_id").references(() => users.id),
  project_id: integer("project_id").references(() => projects.id),
  co2: integer("co2").notNull(),
  share_co2: integer("share_co2").notNull(),
  start_date: text("start_date").notNull(),
  end_date: text("end_date").notNull(),
  type_of_carbon_mechanism: text("type_of_carbon_mechanism").notNull(),
  other_type_of_carbon_mechanism: text("other_type_of_carbon_mechanism"),
  user_of_ghg_ers: text("user_of_ghg_ers").notNull(),
  carbon_credit: integer("carbon_credit").notNull(),
  status: text("status", {
    enum: ["LPE_Request", "LPE_Request__Review", "Signed_LPE", "Rejected_LPE"]
  })
    .notNull()
    .default("LPE_Request"),
  review_implementation_document: text("review_implementation_document"),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`),
  updated_at: integer("updated_at", { mode: "timestamp_ms" }).$onUpdateFn(
    () => new Date()
  )
})

export const implementationsRelations = relations(
  implementations,
  ({ one }) => ({
    user: one(users, {
      fields: [implementations.user_id],
      references: [users.id]
    }),
    project: one(projects, {
      fields: [implementations.project_id],
      references: [projects.id]
    })
  })
)
