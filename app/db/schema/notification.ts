import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"
import { users } from "./users"
import { sql } from "drizzle-orm"

export const notifications = sqliteTable("notifications", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  type: text("type").notNull(),
  user_id: integer("user_id").references(() => users.id),
  data: integer("data").notNull(),
  link: text("link"),
  isRead: integer("active", { mode: "boolean" }).notNull().default(false),
  created_at: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch()*1000)`)
})
