import { IconSvgProps } from "../table/Table"

export const RejectIcon = ({
  size = 24,
  width,
  height,
  ...props
}: IconSvgProps) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 0C4.81331 0 3.65328 0.351894 2.66658 1.01118C1.67989 1.67047 0.910851 2.60754 0.456725 3.7039C0.00259971 4.80025 -0.11622 6.00666 0.115291 7.17054C0.346802 8.33443 0.918247 9.40352 1.75736 10.2426C2.59648 11.0818 3.66557 11.6532 4.82946 11.8847C5.99335 12.1162 7.19975 11.9974 8.2961 11.5433C9.39246 11.0891 10.3295 10.3201 10.9888 9.33342C11.6481 8.34672 12 7.18669 12 6C11.9983 4.40922 11.3656 2.88407 10.2408 1.75921C9.11593 0.63436 7.59078 0.0016799 6 0ZM8.63423 4.94192L5.40346 8.17269C5.3606 8.2156 5.3097 8.24965 5.25367 8.27287C5.19764 8.2961 5.13758 8.30805 5.07692 8.30805C5.01627 8.30805 4.95621 8.2961 4.90018 8.27287C4.84415 8.24965 4.79325 8.2156 4.75039 8.17269L3.36577 6.78808C3.27917 6.70147 3.23051 6.58401 3.23051 6.46154C3.23051 6.33906 3.27917 6.2216 3.36577 6.135C3.45237 6.0484 3.56983 5.99974 3.69231 5.99974C3.81479 5.99974 3.93224 6.0484 4.01885 6.135L5.07692 7.19365L7.98115 4.28885C8.02404 4.24596 8.07494 4.21195 8.13097 4.18874C8.187 4.16553 8.24705 4.15359 8.30769 4.15359C8.36834 4.15359 8.42839 4.16553 8.48442 4.18874C8.54044 4.21195 8.59135 4.24596 8.63423 4.28885C8.67711 4.33173 8.71113 4.38263 8.73434 4.43866C8.75754 4.49469 8.76949 4.55474 8.76949 4.61538C8.76949 4.67603 8.75754 4.73608 8.73434 4.79211C8.71113 4.84813 8.67711 4.89904 8.63423 4.94192Z"
        fill="#DF1C41"
      />
    </svg>
  )
}
