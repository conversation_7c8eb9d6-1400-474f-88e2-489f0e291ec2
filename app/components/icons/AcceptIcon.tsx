import { IconSvgProps } from "../table/Table"

export const AcceptIcon = ({
  size = 24,
  width,
  height,
  ...props
}: IconSvgProps) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_6047_664)">
        <path
          d="M8 2C6.81331 2 5.65328 2.35189 4.66658 3.01118C3.67989 3.67047 2.91085 4.60754 2.45673 5.7039C2.0026 6.80025 1.88378 8.00666 2.11529 9.17054C2.3468 10.3344 2.91825 11.4035 3.75736 12.2426C4.59648 13.0818 5.66557 13.6532 6.82946 13.8847C7.99335 14.1162 9.19975 13.9974 10.2961 13.5433C11.3925 13.0891 12.3295 12.3201 12.9888 11.3334C13.6481 10.3467 14 9.18669 14 8C13.9983 6.40922 13.3656 4.88407 12.2408 3.75921C11.1159 2.63436 9.59078 2.00168 8 2ZM10.6342 6.94192L7.40346 10.1727C7.3606 10.2156 7.3097 10.2496 7.25367 10.2729C7.19764 10.2961 7.13758 10.3081 7.07692 10.3081C7.01627 10.3081 6.95621 10.2961 6.90018 10.2729C6.84415 10.2496 6.79325 10.2156 6.75039 10.1727L5.36577 8.78808C5.27917 8.70147 5.23051 8.58401 5.23051 8.46154C5.23051 8.33906 5.27917 8.2216 5.36577 8.135C5.45237 8.0484 5.56983 7.99974 5.69231 7.99974C5.81479 7.99974 5.93224 8.0484 6.01885 8.135L7.07692 9.19365L9.98115 6.28885C10.024 6.24596 10.0749 6.21195 10.131 6.18874C10.187 6.16553 10.247 6.15359 10.3077 6.15359C10.3683 6.15359 10.4284 6.16553 10.4844 6.18874C10.5404 6.21195 10.5914 6.24596 10.6342 6.28885C10.6771 6.33173 10.7111 6.38263 10.7343 6.43866C10.7575 6.49469 10.7695 6.55474 10.7695 6.61538C10.7695 6.67603 10.7575 6.73608 10.7343 6.79211C10.7111 6.84813 10.6771 6.89904 10.6342 6.94192Z"
          fill="#0C8442"
        />
      </g>
      <defs>
        <clipPath id="clip0_6047_664">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
