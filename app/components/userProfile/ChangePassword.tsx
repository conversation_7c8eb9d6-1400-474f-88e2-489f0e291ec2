import { useTranslation } from "react-i18next"
import InputPassword from "../ui/inputPassword"

const ChangePasswordComponent = ({ user }: { user: any }) => {
  const { t } = useTranslation()

  return (
    <>
      <div className="space-y-2">
        <div>
          <InputPassword
            label={"Old Password"}
            name="oldPassword"
            isRequired
            placeholder={"Enter Old Password"}
            // {...getErrorField("password", data?.error)}
          />
        </div>
        <div>
          <InputPassword
            label={"New Password"}
            isRequired
            name="password"
            placeholder={"Enter New Password"}
            // {...getErrorField("password", data?.error)}
          />
        </div>

        <div>
          <InputPassword
            label={t("auth.registration.confirm_password")}
            name="confirmPassword"
            isRequired
            placeholder={t("auth.registration.enter_confirm_password")}
            // {...getError<PERSON>ield("password", data?.error)}
          />
          <input type="hidden" name="user_id" value={user?.id}></input>
          <input type="hidden" name="user_email" value={user?.email}></input>
        </div>
      </div>
    </>
  )
}

export default ChangePasswordComponent
