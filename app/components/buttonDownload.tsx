import dayjs from "dayjs"

const DownloadButton = ({
  projectID,
  projectName,
  totalVolumeAuthorized,
  totalVolumeReceviedPositiveExamination,
  remainVolume,
  type
}: {
  projectID: number | null
  projectName: string | null
  totalVolumeAuthorized?: number
  totalVolumeReceviedPositiveExamination?: number
  remainVolume?: number
  type: string
}) => {
  const date = dayjs().format("YYYY-MM-DD")

  const handleDownload = async () => {
    let url = ""
    let data = {
      date,
      projectID,
      projectName,
      totalVolumeAuthorized,
      totalVolumeReceviedPositiveExamination,
      remainVolume
    }
    let filename = ""

    if (type === "letter-of-authorization") {
      url = "/letter-of-authorization-pdf"
      data = {
        ...data
      }
      filename = "letterOfAuthorization.pdf"
    } else if (type === "no-objection-letter") {
      url = "/no-objection-letter-pdf"
      filename = "noObjectionLetter.pdf"
    } else if (type === "letter-of-positive-examination") {
      url = "/letter-of-positive-examination-pdf"
      data = {
        ...data
      }
      filename = "letterOfPositiveExamination.pdf"
    } else {
      console.error("Invalid document type")
      return
    }

    try {
      const response = await fetch(url, {
        method: "POST",
        body: JSON.stringify(data)
      })

      if (!response.ok) throw new Error("Failed to generate PDF")

      const blob = await response.blob()
      const link = document.createElement("a")
      link.href = URL.createObjectURL(blob)
      link.download = filename
      link.click()
    } catch (error: any) {
      console.error(error.message)
    }
  }

  return (
    <div>
      <p className="text-[18px] font-bold">
        {type === "no-objection-letter"
          ? "No Objection Letter"
          : type === "letter-of-authorization"
          ? "Letter of authorization"
          : "Letter of Positive Examination"}
      </p>

      <p className="text-[16px]">
        Please download the document by clicking this&nbsp;
        <a
          href="#"
          onClick={e => {
            e.preventDefault()
            handleDownload()
          }}
          style={{
            color: "blue",
            textDecoration: "underline",
            cursor: "pointer"
          }}>
          Link
        </a>
      </p>
    </div>
  )
}

export default DownloadButton
