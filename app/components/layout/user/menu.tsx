import { Listbox, ListboxItem } from "@heroui/react"
import { clsx } from "clsx"
import { Factory, LayoutDashboard } from "lucide-react"

export default function SideMenu({ pathname }: { pathname: string }) {
  const activeCls = "bg-primary text-white"

  return (
    <div className="p-2">
      <Listbox aria-label="Menu">
        <ListboxItem
          key="overview"
          href="/user"
          className={clsx(pathname === "/user" && activeCls)}
          startContent={<LayoutDashboard size={18} />}>
          Overview
        </ListboxItem>
        <ListboxItem
          key="users"
          as="a"
          href="/user/projects"
          className={clsx(pathname === "/user/projects" && activeCls)}
          startContent={<Factory size={18} />}>
          Projects
        </ListboxItem>
      </Listbox>
    </div>
  )
}
