import { X } from "lucide-react"

interface Props {
  title: string
  backref: string
}

export default function Forehead({ title, backref }: Props) {
  return (
    <>
      <div
        className={[
          "h-16",
          "flex",
          "justify-between",
          "items-center",
          "border-b",
          "border-b-base-200",
          "pb-4",
          "mb-10"
        ].join(" ")}>
        <h2 className="font-bold text-lg">{title}</h2>
        <a href={backref} title="Back" className="btn btn-square btn-ghost">
          <X size={24} />
        </a>
      </div>
    </>
  )
}
