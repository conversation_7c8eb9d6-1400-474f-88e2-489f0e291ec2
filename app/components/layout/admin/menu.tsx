import { Listbox, ListboxItem } from "@heroui/react"
import { clsx } from "clsx"
import { Contact, Factory, LayoutDashboard, Settings } from "lucide-react"

export default function SideMenu({ pathname }: { pathname: string }) {
  const activeCls = "bg-primary text-white"

  return (
    <div className="p-2">
      <Listbox aria-label="Menu">
        <ListboxItem
          key="overview"
          href="/admin"
          className={clsx(pathname === "/admin" && activeCls)}
          startContent={<LayoutDashboard size={18} />}>
          Overview
        </ListboxItem>
        <ListboxItem
          key="users"
          as="a"
          href="/admin/users"
          className={clsx(pathname === "/admin/users" && activeCls)}
          startContent={<Contact size={18} />}>
          Users
        </ListboxItem>
        <ListboxItem
          key="projects"
          as="a"
          href="/admin/projects"
          className={clsx(pathname === "/admin/projects" && activeCls)}
          startContent={<Factory size={18} />}>
          Projects
        </ListboxItem>
        <ListboxItem
          key="settings"
          as="a"
          href="/admin/settings"
          className={clsx(pathname.startsWith("/admin/settings") && activeCls)}
          startContent={<Settings size={18} />}>
          Settings
        </ListboxItem>
      </Listbox>
    </div>
  )
}
