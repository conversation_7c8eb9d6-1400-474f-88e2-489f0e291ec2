import { Button } from "@heroui/react"
import { X } from "lucide-react"

interface Props {
  title: string
  backref?: string
}

export default function Forehead({ title, backref }: Props) {
  return (
    <>
      <div
        className={[
          "h-16",
          "flex",
          "justify-between",
          "items-center",
          "border-b",
          "border-b-base-200",
          "pb-4",
          "mb-10"
        ].join(" ")}>
        <h2 className="font-bold text-lg">{title}</h2>
        <Button
          as={"a"}
          href={backref ? backref : "#"}
          size="sm"
          variant="light"
          isIconOnly={true}
          aria-label="Back"
          title="Back">
          <X size={24} />
        </Button>
      </div>
    </>
  )
}
