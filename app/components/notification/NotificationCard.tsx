import { Dot } from "lucide-react"
import { notificationDescription } from "~/utils/notification"
import NotificationIconBg from "./NotificationIcon"
import dayjs from "dayjs"
import relativeTime from "dayjs/plugin/relativeTime"
import { useFetcher, useNavigate } from "@remix-run/react"

export type INotification = {
  type: string | null
  user_id: number | null
  data: number | null
  id: number
  link: string | null
  isRead: boolean
  created_at: Date
}
const NotificationCard = ({
  setIsOpenNotification,
  notification,
  index
}: {
  setIsOpenNotification: (value: boolean) => void
  notification: INotification
  index: number
}) => {
  dayjs.extend(relativeTime)
  const navigate = useNavigate()
  const fetcher = useFetcher()
  const handleReadNotification = (itemId: number) => {
    fetcher.submit({ id: itemId }, { method: "post", action: "/notification" })
  }

  return (
    <>
      <div className="grid" key={index}>
        <div className="flex">
          <div
            className="flex-1 cursor-pointer"
            onClick={() => {
              setIsOpenNotification(false)
              navigate(notification?.link || "")
              handleReadNotification(notification.id)
            }}>
            <div className="flex items-center">
              <NotificationIconBg type={notification?.type} />
              <div className="flex-1">
                <div className="grid gap-y-1 pl-3 pr-2">
                  <div>
                    <p className="text-[16px]">
                      {notificationDescription(notification?.type)}
                    </p>
                  </div>
                  <div>
                    <p className="text-[13px] text-gray-400">
                      {dayjs(notification.created_at).fromNow()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            {!notification.isRead && (
              <div>
                <Dot
                  className="cursor-pointer"
                  size="40"
                  stroke="blue"
                  onClick={() => handleReadNotification(notification.id)}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default NotificationCard
