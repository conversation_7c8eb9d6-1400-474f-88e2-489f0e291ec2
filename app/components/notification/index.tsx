import { useLoaderData, useNavigate } from "@remix-run/react"
import NotificationCard from "./NotificationCard"
import { loader } from "~/routes/_page"

const Notifications = ({
  setIsOpenNotification
}: {
  setIsOpenNotification?: any
}) => {
  const { notifications } = useLoaderData<typeof loader>()
  const navigate = useNavigate()
  return (
    <div className="px-1 py-2 w-96 max-h-[30rem] overflow-auto grid grid-cols-1 gap-y-5">
      <div className="w-full flex justify-center items-center">
        <p className="text-xl font-bold">Notifications</p>
      </div>
      <div className="space-y-3">
        {notifications && notifications.data?.length > 0 ? (
          <>
            {notifications.data?.map((notification, index) => (
              <NotificationCard
                index={index}
                notification={notification}
                setIsOpenNotification={setIsOpenNotification}
              />
            ))}
            {notifications.paginate.pageCount > 1 && (
              <div className="flex justify-end">
                <p
                  className="cursor-pointer"
                  onClick={() => {
                    setIsOpenNotification(false)
                    navigate("/notification")
                  }}>
                  See More
                </p>
              </div>
            )}
          </>
        ) : (
          <p className="font-[25px] text-gray-500 cursor-pointer">
            No Notification
          </p>
        )}
      </div>
    </div>
  )
}

export default Notifications
