import { ENotification } from "~/enums/ENotification"
import { icon } from "~/utils/notification"

const NotificationIcon = ({ type }: { type: string | null }) => {
  const Icon = icon(type)
  switch (type) {
    case ENotification.User:
      return (
        <div className="p-1.5 bg-gray-200 rounded-full">
          <Icon size="30" />
        </div>
      )
    case ENotification.Project:
      return (
        <div className="p-1.5 bg-[#d0f1fb] rounded-full">
          <Icon size="30" stroke="#34c3eb" />
        </div>
      )
    case ENotification.Comment:
      return (
        <div className="p-1.5 bg-blue-100 rounded-full">
          <Icon size="30" stroke="#3486eb" />
        </div>
      )
    case ENotification.Assign:
      return (
        <div className="p-1.5 bg-[#ffcce6]  rounded-full">
          <Icon size="30" stroke="#ff3399" />
        </div>
      )
    case ENotification.Review:
      return (
        <div className="p-1.5 bg-yellow-200 rounded-full">
          <Icon size="30" stroke="#171716" />
        </div>
      )
    case ENotification.ReviewApproved:
      return (
        <div className="p-1.5 bg-green-100 rounded-full">
          <Icon size="30" stroke="green" />
        </div>
      )
    case ENotification.ReviewRejected:
      return (
        <div className="p-1.5 bg-red-100 rounded-full">
          <Icon size="30" stroke="red" />
        </div>
      )
    case ENotification.ProjectApproved:
      return (
        <div className="p-1.5 bg-green-100 rounded-full">
          <Icon size="30" stroke="green" />
        </div>
      )
    case ENotification.ProjectRejected:
      return (
        <div className="p-1.5 bg-red-100 rounded-full">
          <Icon size="30" stroke="red" />
        </div>
      )
    default:
      return (
        <div className="p-1.5 bg-gray-200 rounded-full">
          <Icon size="30" />
        </div>
      )
  }
}

export default NotificationIcon
