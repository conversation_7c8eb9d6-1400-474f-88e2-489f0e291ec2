import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>overTrigger
} from "@heroui/react"
import { Bell } from "lucide-react"
import { useState } from "react"
import Notifications from "."

export interface NotificationItem {
  id: number
  data: number
  isRead: boolean
  link: string | null
  user_id: number | null
  type: string
  created_at: Date
}

export interface Pagination {
  page: number
  pageCount: number
  pageSize: number
  total: number
}

export interface INotification {
  data: NotificationItem[]
  paginate: Pagination
}

const NotificationPopover = ({
  notifications
}: {
  notifications: INotification | null | any
}) => {
  const [isOpenNotification, setIsOpenNotification] = useState(false)

  const unread =
    notifications && notifications?.data?.filter(item => !item?.isRead).length

  return (
    <div>
      <Popover
        placement="bottom"
        isOpen={isOpenNotification}
        onOpenChange={open => setIsOpenNotification(open)}>
        {unread && unread > 0 ? (
          <Badge
            color="danger"
            className="display-none"
            content={unread}
            shape="circle">
            <PopoverTrigger>
              <Button
                bg-green-100
                isIconOnly
                aria-label="more than 99 notifications"
                radius="full"
                variant="light"
                onPress={() => {
                  setIsOpenNotification(true)
                }}>
                <Bell size={24} />
              </Button>
            </PopoverTrigger>
          </Badge>
        ) : (
          <>
            <PopoverTrigger>
              <Button
                bg-green-100
                isIconOnly
                aria-label="more than 99 notifications"
                radius="full"
                variant="light"
                onPress={() => {
                  setIsOpenNotification(true)
                }}>
                <Bell size={24} />
              </Button>
            </PopoverTrigger>
          </>
        )}
        <PopoverContent>
          <Notifications setIsOpenNotification={setIsOpenNotification} />
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default NotificationPopover
