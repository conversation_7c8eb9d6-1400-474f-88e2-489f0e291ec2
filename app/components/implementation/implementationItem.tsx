const ImplementationItem = ({
  label,
  value,
  colorPrimary
}: {
  label: string
  value: string
  colorPrimary?: boolean
}) => {
  return (
    <div className="flex">
      <div className="min-w-[250px]">{label}:</div>
      <div>
        {colorPrimary ? (
          <p className="text-primary uppercase font-bold">{value}</p>
        ) : (
          <p>{value}</p>
        )}
      </div>
    </div>
  )
}

export default ImplementationItem
