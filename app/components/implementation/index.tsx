import { <PERSON>, <PERSON> } from "@heroui/react"
import dayjs from "dayjs"
import {
  getTypeOfCarbonName,
  getUseOfGhgErsName
} from "~/lib/performanceDetail"
import ImplementationItem from "./implementationItem"

const ImplementationDetail = ({
  implementation,
  linkToProject,
  cursor_pointer = false
}: {
  implementation: any
  linkToProject?: string
  cursor_pointer?: boolean
}) => {
  const performanceReportDetail = [
    { name: "Volume of in tCO2eq", value: implementation?.co2 },
    { name: "Share of in tCO2eq", value: implementation?.share_co2 },
    { name: "Carbon Credit", value: implementation?.carbon_credit },
    {
      name: "Duration",
      value: `From ${dayjs(implementation?.start_date).format(
        "DD/MMM/YYYY"
      )} To 
      ${dayjs(implementation?.end_date).format("DD/MMM/YYYY")}`
    },
    {
      name: "Type Of Carbon Mechanism",
      value:
        implementation?.type_of_carbon_mechanism === "other"
          ? implementation?.other_type_of_carbon_mechanism
          : getTypeOfCarbonName(implementation?.type_of_carbon_mechanism)
    },
    {
      name: "Use of GHG ERs",
      value: `${getUseOfGhgErsName(implementation?.user_of_ghg_ers)}`
    },
    {
      name: "Submitted Date",
      value: dayjs(implementation?.created_at).format("DD/MMM/YYYY")
    },
    {
      name: "Performance Report Status",
      value: implementation?.status,
      colorPrimary: true
    }
  ]
  const projectProponentContactDetail = [
    { name: "Email address", value: implementation?.user?.email },
    {
      name: "Phone number",
      value: implementation?.user?.phone ? implementation?.user?.phone : "N/A"
    }
  ]

  return (
    <Card className={`p-4 my-4 ${cursor_pointer ? "cursor-pointer" : ""}`}>
      <div className="space-y-2">
        <div>
          <p className="text-black text-[18px] font-bold">
            Performance Report Detail
          </p>
        </div>
        {implementation?.project?.name && (
          <div className="flex">
            <div className="min-w-[250px]">Project Name:</div>
            <div>
              <Link href={linkToProject} className="font-bold text-[18px]">
                {implementation?.project?.name}
              </Link>
            </div>
          </div>
        )}
        {performanceReportDetail.map((item, index) => {
          return (
            <div key={index}>
              <ImplementationItem
                label={item?.name}
                value={item?.value}
                colorPrimary={item?.colorPrimary}
              />
            </div>
          )
        })}
        <div>
          <p className="text-black text-[18px] font-bold">
            Project proponent contact details
          </p>
        </div>
        {projectProponentContactDetail.map((item, index) => {
          return (
            <div key={index}>
              <ImplementationItem label={item?.name} value={item?.value} />
            </div>
          )
        })}
      </div>
    </Card>
  )
}

export default ImplementationDetail
