import type { ITopImageTitleAndShortDescription } from "~/types"

const TopImageTitleAndShortDescription = ({
  title,
  shortDescription,
  image,
  bg_color = "#0C844266",
  isTop = true,
  textColor = "white"
}: ITopImageTitleAndShortDescription) => {
  return (
    <div className={`${isTop ? "h-[450px]" : "h-[490px]"} relative`}>
      <img className=" w-full h-full object-cover" src={image}></img>
      <div
        style={{ backgroundColor: bg_color }}
        className={`size-full absolute top-0 left-0 text-${textColor}`}>
        <div className="h-full w-full flex justify-center items-center">
          <div className="grid grid-cols-1 gap-4 w-full sm:w-4/5 md:w-9/12 lg:w-1/2 px-2">
            <div className="flex justify-center">
              <p
                className={
                  isTop ? "titleOnBackgroundImage" : "title text-center"
                }>
                {title}
              </p>
            </div>
            <div className="flex justify-center">
              <p
                className={
                  isTop
                    ? "shortDescriptionOnBackgroundImage leading-[40px] lg:leading-[45px]"
                    : "shortDescription"
                }>
                {shortDescription}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TopImageTitleAndShortDescription
