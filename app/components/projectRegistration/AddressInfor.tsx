import { Info } from "lucide-react"

const AddressInfo = () => {
  return (
    <div className="bg-[#EDECEC] rounded-2xl  p-4">
      <div className="flex gap-x-2">
        <div>
          <Info width={22} height={22} />
        </div>
        <div>
          <p>
            <span className="font-bold">Address:&nbsp;</span>
            Specify the exact physical location (e.g., village, commune,
            district). Include landmarks if applicable. Example: "Village X,
            Commune Y"
          </p>
        </div>
      </div>
    </div>
  )
}

export default AddressInfo
