import { Button, Radio, RadioGroup } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ArrowRightIcon } from "lucide-react"
import { useTranslation } from "react-i18next"
import type { IProjectSector } from "~/types"

const SelectProjectSectorStep = ({
  sectors,
  setSelectedSector,
  selectedSector,
  onContinue
}: {
  selectedSector: string | null
  setSelectedSector: (sector: string) => void
  sectors: IProjectSector[]
  onContinue: () => void
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div className="container max-w-3xl mx-auto space-y-6 px-5">
      <div className="flex justify-between">
        <div className="text-primary">
          <p className="text-lg font-bold">Select Project Sector</p>
        </div>
        <div className="text-primary">
          <p className="text-sm">Step 1 of 2</p>
        </div>
      </div>
      <hr />
      <div className="bg-white shadow-md border p-6 rounded-xl max-h-[38rem] overflow-y-auto">
        <div className="grid grid-cols-1 gap-3">
          <div>Choose Project Sector</div>
          <div>
            <RadioGroup
              color="primary"
              name="account_type"
              value={selectedSector}
              onValueChange={setSelectedSector}>
              {sectors.map(sector => (
                <Radio key={sector.id} value={String(sector.id)}>
                  {sector.name}
                </Radio>
              ))}
            </RadioGroup>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Button onPress={() => navigate(-1)} color="primary" variant="light">
          {t("auth.registration.cancel")}
        </Button>
        <Button
          onPress={onContinue}
          isDisabled={!selectedSector}
          color="primary"
          endContent={<ArrowRightIcon className="size-5" />}>
          {t("auth.registration.continue")}
        </Button>
      </div>
    </div>
  )
}

export default SelectProjectSectorStep
