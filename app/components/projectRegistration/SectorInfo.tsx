import { Info } from "lucide-react"

const SectorInfo = () => {
  return (
    <div className="bg-[#EDECEC] rounded-2xl p-4">
      <div className="flex gap-x-2 items-center">
        <div>
          <Info width={22} height={22} />
        </div>
        <div>
          <p className="font-bold text-[20px]">Sectors</p>
        </div>
      </div>
      <div>
        <ul className="list-disc py-2 px-12">
          <li>
            <span className="font-bold">Energy&nbsp;</span>(Renewable energy,
            energy efficiency, clean technology projects)
          </li>
          <li>
            <span className="font-bold">
              Industrial Processing and Product Use (IPPU)&nbsp;
            </span>
            (Manufacturing, chemical processes, refrigerant management)
          </li>
          <li>
            <span className="font-bold">
              Forestry and Other Land Use (FOLU)&nbsp;
            </span>
            (Reforestation, afforestation, sustainable land management)
          </li>
          <li>
            <span className="font-bold">Waste Management&nbsp;</span>
            (Recycling, composting, landfill gas capture, waste-to-energy
            projects)
          </li>
        </ul>
      </div>
    </div>
  )
}

export default SectorInfo
