import { Button, CalendarDate, DateValue } from "@heroui/react"
import { ArrowRightIcon } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { IProjectType } from "~/types/projectType"
import DatePicker from "../ui/DatePicker"
import Input from "../ui/input"
import TextArea from "../ui/textArea"
import FileSelect from "../userRegistration/FileSelect"

const ProjectRegistrationForm = ({
  onBack,
  selectedSector,
  types
}: {
  onBack: () => void
  selectedSector: string | null
  types: IProjectType[]
}) => {
  const { t } = useTranslation()

  const [cover, setCover] = useState<string>()
  const [startDate, setStartDate] = useState<CalendarDate | null>(null)
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)
  const [firstDateDelivery, setFirstDateDelivery] =
    useState<CalendarDate | null>(null)

  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }
  const handleFirstDateDeliveryChange = (date: DateValue | null) => {
    setFirstDateDelivery(date as CalendarDate | null)
  }

  const [coordinates, setCoordinates] = useState<string | null>("")

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const reader = new FileReader()
      reader.onload = () => {
        const kmlText = reader.result as string
        const coordinates = getKMLCoordinates(kmlText)
        setCoordinates(coordinates)
      }
      reader.readAsText(event.target.files[0])
    }
  }
  const getKMLCoordinates = (kmlText: string) => {
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(kmlText, "application/xml")
    const coordinates =
      xmlDoc.getElementsByTagName("coordinates")[0]?.textContent
    return coordinates
  }

  return (
    <div className="container max-w-3xl mx-auto space-y-6 px-5">
      <div className="flex justify-between">
        <div className="text-primary">
          <p className="text-lg font-bold">Project Registration Form</p>
        </div>
        <div className="text-primary">
          <p className="text-sm">Step 2 of 2</p>
        </div>
      </div>
      <hr />
      <div className="bg-white shadow-md border p-6 rounded-xl">
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 gap-3">
            <div>Project Basic Information</div>
            <div className="grid gap-x-4 gap-y-2">
              <input
                type="hidden"
                name="sector_id"
                value={selectedSector ?? ""}
              />
              <div>
                <Input
                  isRequired
                  label={"Project Name"}
                  name="name"
                  placeholder="Enter Project Name"
                />
              </div>
              <div>
                <FileSelect
                  label="Project Location"
                  name="location"></FileSelect>
              </div>

              <div>
                <label className="relative col-span-2 md:col-span-2  border-2 flex items-center justify-center h-40 rounded-2xl cursor-pointer border-black/50 overflow-hidden">
                  <p className="text-black/60">Upload Cover</p>
                  {cover && (
                    <img
                      className="absolute top-0 left-0 w-full h-full object-cover"
                      src={cover}
                    />
                  )}
                  <input
                    id="cover_img"
                    type="file"
                    name="cover_img"
                    hidden
                    accept="image/*"
                    onChange={e => {
                      if (e.target.files?.length) {
                        setCover(URL.createObjectURL(e.target.files[0]))
                      }
                    }}
                  />
                </label>
              </div>
              <div>
                <div className="flex justify-end">
                  <Button
                    type="button"
                    as="label"
                    htmlFor="cover_img"
                    color="primary">
                    {cover ? "Change Cover" : "Upload Cover"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-3">
            <div>Idea Note</div>
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="col-span-2 md:col-span-2">
                <Input
                  label="Greenhouse Gases Targeted"
                  name="con.ghg"
                  placeholder="Greenhouse Gases Targeted"
                  endContent={
                    <p className="text-[15px] text-gray-500 whitespace-nowrap">
                      tCO2eq
                    </p>
                  }
                />
              </div>
              <div className="col-span-2 md:col-span-2">
                <TextArea
                  label="Positive List"
                  name="con.positive"
                  placeholder={
                    "GHG ER Project Alignment with Cambodia's Positive List"
                  }
                />
              </div>
              <div className="col-span-2 md:col-span-2">
                <TextArea
                  label="Sustainable Development Priorities"
                  name="con.dev_prio"
                  placeholder={"Sustainable Development Priorities"}
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Project Start Date"
                  name="con.start_date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  maxValue={endDate}
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Project End Date"
                  name="con.end_date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  minValue={startDate}
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="First Year GHG ER Delivery"
                  name="con.first_date_delivery"
                  value={firstDateDelivery}
                  onChange={handleFirstDateDeliveryChange}
                  minValue={startDate}
                  maxValue={endDate}
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  label={"Technology Transfer"}
                  name="con.technology_transfer"
                  placeholder={"Enter Technology Transfer"}
                />
              </div>
              <div className="col-span-2 md:col-span-2">
                <Input
                  label={"Economic Benefits"}
                  name="con.economic_benefits"
                  placeholder={"Enter Economic Benefits"}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button onPress={onBack} color="primary" variant="light">
          Back
        </Button>
        <Button
          type="submit"
          color="primary"
          endContent={<ArrowRightIcon className="size-5" />}>
          {t("auth.registration.continue")}
        </Button>
      </div>
    </div>
  )
}

export default ProjectRegistrationForm
