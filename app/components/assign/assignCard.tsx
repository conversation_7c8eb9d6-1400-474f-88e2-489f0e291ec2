import { Button, Input } from "@heroui/react"
import { useDialog } from "../dialog"
import AssignItem from "./assignItem"
import { EProjectStatus } from "~/enums/EProjectStatus"

const AssignCard = ({
  assignList,
  data,
  type
}: {
  assignList: any
  data: any
  type?: string
}) => {
  const { openDialog } = useDialog()
  const onChangeApproveReject = ({
    id,
    approve
  }: {
    id: number
    approve: boolean
  }) => {
    const approveRejectLabel = approve ? "Approve" : "Reject"

    openDialog({
      title: approveRejectLabel,
      component: (
        <>
          <input
            type="hidden"
            name={
              type === "project"
                ? "assign_id"
                : type === "pdd"
                ? "pdd_id"
                : "implementation_id"
            }
            value={id}
          />
          <input
            type="hidden"
            name="action"
            value={approve ? "reviewer_approve" : "reviewer_reject"}
          />
          <Input required type="file" name="review_document" />
        </>
      ),
      submitOption: {
        options: {
          method: "post",
          action:
            type === "pdd"
              ? `/admin/pdd-reports/${data?.id}`
              : type === "project"
              ? `/admin/projects/${data?.id}`
              : `/admin/performance-reports/${data?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: approveRejectLabel
      }
    })
  }

  return assignList.map((item: any, index: number) => (
    <div key={index} className="bg-gray-200 p-3 grid gap-3 rounded-md">
      <AssignItem item={item} />
      {item?.status === "pending" &&
        (data?.status === EProjectStatus.PendingLnoRequest ||
          data?.status === EProjectStatus.PendingReviewLnoRequest ||
          data?.project?.status === EProjectStatus.PendingPDDReview ||
          data?.status === "LPE_Request") && (
          <div className="flex justify-end space-x-3">
            <Button
              color="primary"
              onPress={() =>
                onChangeApproveReject({
                  id: item?.id,
                  approve: true
                })
              }>
              Approve
            </Button>
            <Button
              color="danger"
              onPress={() =>
                onChangeApproveReject({
                  id: item?.id,
                  approve: false
                })
              }>
              Reject
            </Button>
          </div>
        )}
    </div>
  ))
}

export default AssignCard
