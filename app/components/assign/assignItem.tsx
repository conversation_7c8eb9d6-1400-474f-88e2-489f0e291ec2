import { Link } from "@heroui/react"
import { getImage } from "~/helpers/r2"

const AssignItem = ({ item }: { item: any }) => {
  return (
    <>
      <div className="grid grid-cols-3">
        <div>Name</div>
        <div>Status</div>
        <div>Document</div>
      </div>
      <div className="grid grid-cols-3">
        <div>
          {item.user?.firstName} {item?.user?.lastName}
        </div>
        <div>{item?.status}</div>
        <div>
          {item?.review_document ? (
            <Link target="_blank" href={getImage(item?.review_document)}>
              Review Document
            </Link>
          ) : (
            "N/A"
          )}
        </div>
      </div>
    </>
  )
}

export default AssignItem
