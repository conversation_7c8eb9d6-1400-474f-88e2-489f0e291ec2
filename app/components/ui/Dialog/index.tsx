import {
  Button,
  ButtonProps,
  Modal,
  ModalBody,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  ModalHeaderProps,
  ModalProps
} from "@heroui/react"
import { useFetcher, useNavigate } from "@remix-run/react"
import { ReactNode, useEffect } from "react"
import { useTranslation } from "react-i18next"

export interface IDialogProps
  extends Omit<ModalProps, "title" | "open" | "onClose"> {
  /**
   * Determines whether the dialog is open or not.
   */
  open: boolean

  /**
   * The title of the dialog.
   */
  title?: ReactNode
  titleIcon?: ReactNode
  subTitle?: ReactNode

  /**
   * The message to display in the dialog.
   */
  message?: ReactNode

  submitOption: {
    formData?: any
    options: any
    fetcherKey?: string
  }

  /**
   * Function to be called when the user clicks the cancel button.
   */
  onSecondaryAction?: VoidFunction

  /**
   * Function to be called when the user clicks the confirm button.
   */
  onPrimaryAction?: VoidFunction

  /**
   * Secondary button text.
   *
   * @default 'Cancel'
   */
  secondaryButtonText?: string

  /**
   * Primary button text.
   *
   * @default 'Confirm'
   */
  primaryButtonText?: string

  /**
   * Determines whether the secondary action button is hidden.
   */
  hideSecondaryAction?: boolean

  /**
   * Determines whether the primary action button is hidden.
   */
  hidePrimaryAction?: boolean

  /**
   * Props to be passed to the DialogTitle component.
   */
  titleProps?: ModalHeaderProps
  primaryButtonProps?: ButtonProps
}

export default function Dialog({
  open,
  title,
  titleIcon,
  subTitle,
  titleProps,
  submitOption,
  children,
  onSecondaryAction,
  primaryButtonText,
  primaryButtonProps,
  secondaryButtonText,
  ...props
}: IDialogProps) {
  const { t } = useTranslation()
  const fetcher = useFetcher({
    key: submitOption?.fetcherKey
  })
  // const fetcher = useFetcher()
  const navigate = useNavigate()
  const primaryButtonLabel = primaryButtonText ?? t("button.save")
  const cancelLabel = secondaryButtonText ?? t("button.cancel")
  const isSubmitting = fetcher.state === "submitting"

  useEffect(() => {
    if (
      onSecondaryAction &&
      fetcher.state === "idle" &&
      fetcher.data?.success
    ) {
      if (fetcher.data?.redirect) {
        navigate(fetcher.data.redirect)
      }
      fetcher.load("")
      onSecondaryAction()
    }
  }, [fetcher.state, fetcher.data])

  return (
    <Modal isOpen={open} onOpenChange={onSecondaryAction} {...props}>
      <ModalContent>
        <fetcher.Form {...submitOption?.options}>
          <ModalHeader className="text-[22px]">{title}</ModalHeader>
          <ModalBody>{children}</ModalBody>
          <ModalFooter>
            <Button onPress={onSecondaryAction} isDisabled={isSubmitting}>
              {cancelLabel}
            </Button>
            <Button
              type="submit"
              className="bg-primary text-white"
              isLoading={isSubmitting}>
              {primaryButtonLabel}
            </Button>
          </ModalFooter>
        </fetcher.Form>
      </ModalContent>
    </Modal>
  )
}
