import { Textarea as TextAreaNextUI, TextAreaProps } from "@heroui/react"

const TextArea = ({ ...props }: TextAreaProps) => {
  return (
    <TextAreaNextUI
      size="lg"
      variant="bordered"
      classNames={{
        ...baseInputClassName
      }}
      {...props}
    />
  )
}

export const baseInputClassName = {
  label: "text-base",
  input: "text-sm",
  inputWrapper: [
    "border-[#A1A1AA]",
    "data-[hover=true]:border-[#6a6a6e]",
    "group-data-[focus=true]:border-green-500"
  ]
}

export default TextArea
