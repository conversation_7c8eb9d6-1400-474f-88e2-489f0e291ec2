import { Button, Input } from "@heroui/react"
import { useRef, useState } from "react"

const FileUpload = ({ name, label }: { name: string; label: string }) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [fileName, setFileName] = useState<string>("")

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFileName(file.name)
    }
  }

  return (
    <div className="flex flex-col gap-1">
      <div className="border-2 border-gray-300 hover:border-gray-400 focus-within:border-green-500 rounded-xl p-2 grid grid-cols-1 gap-y-3">
        <label className="text-sm font-medium text-gray-700">
          {label} <span className="text-red-600">*</span>
        </label>
        <div className="flex gap-x-4 items-center">
          <div>
            <Button onPress={handleButtonClick} className="bg-[#E4E4E7]">
              Choose File
            </Button>
          </div>
          <div>{fileName || "No file chosen"}</div>
        </div>
      </div>
      <Input
        name={name}
        isRequired
        accept="image/*"
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />
    </div>
  )
}

export default FileUpload
