import { But<PERSON>, <PERSON>dal, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, Mo<PERSON>Header } from "@heroui/react"
import { useFetcher, useNavigate } from "@remix-run/react"
import React, { useEffect } from "react"
import { useTranslation } from "react-i18next"
import { IDialogProps } from "~/components/ui/Dialog"

export default function AlertDialog({
  open,
  title,
  titleIcon,
  message,
  titleProps,
  submitOption,
  onPrimaryAction,
  onSecondaryAction,
  primaryButtonText,
  primaryButtonProps,
  secondaryButtonText,
  ...props
}: IDialogProps) {
  const { t } = useTranslation()
  const fetcher = useFetcher()
  const navigate = useNavigate()
  const okLabel = primaryButtonText ?? t("button.ok")
  const cancelLabel = secondaryButtonText ?? t("button.cancel")
  const isSubmitting = fetcher.state === "submitting"

  useEffect(() => {
    if (onSecondaryAction && fetcher.state === "idle" && fetcher.data?.success) {
      if (fetcher.data?.redirect) {
        navigate(fetcher.data.redirect)
      }

      onSecondaryAction()
    }
  }, [fetcher.state, fetcher.data])

  const handleConfirmation = () => {
    fetcher.submit(submitOption.formData ?? null, submitOption.options)
  }

  return (
    <Modal isOpen={open} onOpenChange={onSecondaryAction} {...props}>
      <ModalContent>
        <ModalHeader>{title}</ModalHeader>
        <ModalBody>{message}</ModalBody>
        <ModalFooter>
          <Button onPress={onSecondaryAction} isDisabled={isSubmitting}>
            {cancelLabel}
          </Button>
          <Button
            className="bg-primary text-white"
            onPress={handleConfirmation}
            {...primaryButtonProps}
            isLoading={isSubmitting}
          >
            {okLabel}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
