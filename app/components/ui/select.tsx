import { Select as SelectNextUI, SelectItem, SelectProps } from "@heroui/react"
import { baseInputClassName } from "./input"

interface Option {
  key?: string
  label?: string
  type?: string
  name?: string
  id?: number
}

interface CustomSelectProps extends Omit<SelectProps, "children"> {
  data: Option[]
  children?: React.ReactNode
}

const Select = ({ data, children, value, ...props }: CustomSelectProps) => {
  return (
    <SelectNextUI
      {...props}
      value={value}
      variant="bordered"
      size="lg"
      classNames={{
        label: baseInputClassName.label,
        trigger: [
          ...baseInputClassName.inputWrapper,
          "data-[open=true]:border-green-500"
        ],
        value: baseInputClassName.input
      }}>
      {data.map(item => (
        <SelectItem key={item.key} value={item.key}>
          {item.label}
        </SelectItem>
      ))}
    </SelectNextUI>
  )
}
export default Select
