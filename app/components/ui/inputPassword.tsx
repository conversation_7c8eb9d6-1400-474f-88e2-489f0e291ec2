import { InputProps } from "@heroui/react"
import { Eye, EyeOff } from "lucide-react"
import React from "react"
import Input from "~/components/ui/input"

const InputPassword = ({ ...props }: InputProps) => {
  const [isVisible, setIsVisible] = React.useState(false)

  const toggleVisibility = () => setIsVisible(!isVisible)

  return (
    <Input
      endContent={
        <button
          aria-label="toggle password visibility"
          className="focus:outline-none"
          type="button"
          onClick={toggleVisibility}>
          {isVisible ? <EyeOff /> : <Eye />}
        </button>
      }
      type={isVisible ? "text" : "password"}
      {...props}
    />
  )
}

export default InputPassword
