import { forwardRef } from "react"
import { Input as InputNextUI, InputProps } from "@heroui/react"

export const baseInputClassName = {
  label: "text-base",
  input: "text-sm",
  inputWrapper: [
    "border-[#A1A1AA]",
    "data-[hover=true]:border-[#6a6a6e]",
    "group-data-[focus=true]:border-green-500"
  ]
}

const Input = forwardRef<HTMLInputElement, InputProps>(({ ...props }, ref) => {
  return (
    <InputNextUI
      ref={ref}
      size="lg"
      variant="bordered"
      classNames={baseInputClassName}
      {...props}
    />
  )
})

Input.displayName = "Input"

export default Input
