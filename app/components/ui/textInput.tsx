import { forwardRef, InputHTMLAttributes } from "react"

const TextInput = forwardRef<
  HTMLInputElement,
  InputHTMLAttributes<HTMLInputElement>
>((props, ref) => {
  return (
    <input
      className="border border-gray-400 hover:border-indigo-400 bg-transparent rounded w-full px-2 py-1"
      ref={ref}
      {...props}
    />
  )
})

TextInput.displayName = "TextInput"

export default TextInput
