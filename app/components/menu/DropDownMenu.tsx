import {
  Avatar,
  Dropdown,
  DropdownItem,
  Dropdown<PERSON><PERSON>u,
  DropdownTrigger
} from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ChevronDown, Layout, LogOut, User, UserCircle } from "lucide-react"
import { ERole } from "~/enums/EUserRole"
import { getImage } from "~/helpers/r2"
import NotificationPopover from "../notification/NotificationPopover"

const DropDownMenu = ({
  notifications,
  user
}: {
  notifications: any
  user: any
}) => {
  const navigate = useNavigate()

  return (
    <div className="inline-flex gap-2 items-center">
      <NotificationPopover notifications={notifications} />
      <div
        className="inline-block cursor-pointer"
        onClick={() => {
          user?.role !== ERole.ProjectDeveloper
            ? navigate("/admin/profile")
            : navigate("/profile")
        }}>
        {user?.firstName} {user?.lastName}
      </div>
      <div className="flex items-center">
        {user?.photoUrl ? (
          <Avatar
            onClick={() => {
              user.role !== ERole.ProjectDeveloper
                ? navigate("/admin/profile")
                : navigate("/profile")
            }}
            src={user?.photoUrl ? getImage(user?.photoUrl) : ""}
            alt={`${user?.firstName} ${user?.lastName}`}
            className="w-8 h-8"
          />
        ) : (
          <UserCircle
            size={32}
            className="cursor-pointer"
            onClick={() => {
              user?.role !== ERole.ProjectDeveloper
                ? navigate("/admin/profile")
                : navigate("/profile")
            }}
          />
        )}
        <Dropdown>
          <DropdownTrigger>
            <ChevronDown className="ml-2 cursor-pointer" />
          </DropdownTrigger>
          <DropdownMenu aria-label="Dropdown menu with icons" variant="faded">
            {user?.isAdmin || user?.isSuper ? (
              <DropdownItem
                key="admin"
                href="/admin"
                startContent={<Layout size={16} />}>
                Admin
              </DropdownItem>
            ) : (
              <DropdownItem
                key="dashboard"
                href="/user"
                startContent={<Layout size={16} />}>
                Dashboard
              </DropdownItem>
            )}
            <DropdownItem
              key="profile"
              href={
                user?.role !== ERole.ProjectDeveloper
                  ? "/admin/profile"
                  : "/profile"
              }
              startContent={<User size={16} />}>
              Profile
            </DropdownItem>
            <DropdownItem
              key="logout"
              href="/logout"
              startContent={<LogOut size={16} />}>
              Logout
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </div>
  )
}

export default DropDownMenu
