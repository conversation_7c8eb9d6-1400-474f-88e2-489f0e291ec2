import { replace } from "@remix-run/react"
import { asc, desc, like, or } from "drizzle-orm"
import { SQLiteColumn } from "drizzle-orm/sqlite-core"

export const fieldSort = (
  orderField: string,
  orderBy: "ascending" | "descending"
): string => {
  return `${orderBy === "descending" ? "-" : ""}${orderField}`
}

export const defaultListQuery = {
  page: 1,
  limit: 12,
  sort: fieldSort("created_at", "descending")
}

export type ListQuery<T = Record<string, string | number>> = {
  page: number
  limit: number
  search: string
  sort: string
} & T

export function getListQuery<T>(
  request: Request,
  defaultQuery?: Partial<ListQuery<T>>
): ListQuery<T> {
  const searchParams = new URL(request.url).searchParams

  const query: Record<string, string | number> = {}

  for (const key of searchParams.keys()) {
    query[key] = isNaN(Number(searchParams.get(key)))
      ? String(searchParams.get(key))
      : Number(searchParams.get(key))
  }

  // if (!query.page || !query.limit) {
  //   throw replace(
  //     `?${new URLSearchParams({
  //       ...defaultListQuery,
  //       ...(defaultQuery ? defaultQuery : {})
  //     } as any).toString()}`
  //   )
  // }

  if (defaultQuery) {
    Object.keys(defaultQuery).forEach(key => {
      query[key] = query[key] || (defaultQuery as any)[key]
    })
  }

  query.page = query.page || defaultListQuery.page
  query.limit = query.limit || defaultListQuery.limit
  query.search = query.search || defaultListQuery.search
  query.sort = query.sort || defaultListQuery.sort

  return query as ListQuery<T>
}

export const paginate = (page: number, limit: number) => {
  return {
    offset: (Number(page) - 1) * Number(limit),
    limit: Number(limit)
  }
}

export const filterSearch = (search: string, columns: SQLiteColumn[]) => {
  if (!search) {
    return or()
  }
  return or(...columns.map(column => like(column, `%${search}%`)))
}

export const sortColumn = (sort: string, map: Record<string, SQLiteColumn>) => {
  if (!sort) return []
  const column = sort.replace(/^-/, "")
  const order = sort.startsWith("-") ? "descending" : "ascending"

  if (column in map) {
    if (order === "ascending") {
      return [asc(map[column])]
    } else {
      return [desc(map[column])]
    }
  }

  return []
}
