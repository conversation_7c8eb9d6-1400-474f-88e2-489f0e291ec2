import {
  get<PERSON>ey<PERSON><PERSON><PERSON>,
  SortDescriptor,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableColumnProps,
  TableHeader,
  TableRow
} from "@heroui/table"
import React from "react"

type DataTableProps<T> = {
  rows: DataTableRow<T>[]
  data: T[]
  onSortChange?: (sortDescriptor: SortDescriptor) => void
  sortDescriptor?: SortDescriptor
  onRowClick?: (id: string | number) => void
}

type DataTableRow<T> = {
  headerLabel: string
  key: string
  renderCell?: (row: T) => React.ReactNode
  width?: TableColumnProps<T>["width"]
  allowSorting?: boolean
}

export function createDataTableRow<T extends any[]>(
  rows: DataTableRow<T[0]>[]
) {
  return rows
}

export default function DataTable<T extends { id: string | number }>({
  rows,
  data,
  onSortChange,
  sortDescriptor,
  onRowClick
}: DataTableProps<T>) {
  return (
    <Table
      classNames={{
        wrapper: "p-0"
      }}
      aria-label="table"
      onSortChange={onSortChange}
      sortDescriptor={sortDescriptor}>
      <TableHeader columns={rows}>
        {column => (
          <TableColumn
            className="bg-[#0C8442] text-white text-[14px] text-center"
            allowsSorting={column?.allowSorting}
            width={column?.width}
            key={column.key as string}>
            {column.headerLabel}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody items={data} emptyContent="No project to display">
        {item => (
          <TableRow
            key={item.id}
            className="cursor-pointer hover:bg-[#dcfcec] odd:bg-white even:bg-gray-200"
            onClick={() => {
              if (onRowClick) {
                onRowClick(item?.id)
              }
            }}>
            {columnKey => {
              const cell = rows.find(row => row.key === columnKey)
              let content = null

              if (cell) {
                if (cell.renderCell) {
                  content = cell.renderCell(item)
                } else {
                  content = getKeyValue(item, columnKey)
                }
              }

              if (
                !(React.isValidElement(content) || typeof content === "string")
              ) {
                content = JSON.stringify(content)
              }
              return (
                <TableCell key={columnKey} className="text-[13px]">
                  {content}
                </TableCell>
              )
            }}
          </TableRow>
        )}
      </TableBody>
    </Table>
  )
}
