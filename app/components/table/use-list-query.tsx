import { useSearchParams } from "@remix-run/react";
import { defaultListQuery, fieldSort, ListQuery } from "./query";
import { SortDescriptor } from "@heroui/table";

function useListQuery<T>() {
  const [searchParams, setSearchParams] = useSearchParams();

  const updateQuery = (query: Partial<ListQuery<T>>) => {
    setSearchParams(
      (prev) => {
        const searchParams = new URLSearchParams(
          new URLSearchParams(prev).toString()
        );

        const _query = { ...defaultListQuery, ...query };

        for (const key in _query) {
          searchParams.set(
            key,
            key in _query ? String(_query[key as keyof ListQuery<T>]) : ""
          );
        }

        return searchParams;
      },
      {
        replace: true,
      }
    );
  };

  const removeQuery = (key: keyof ListQuery<T>) => {
    setSearchParams(
      (prev) => {
        const searchParams = new URLSearchParams(
          new URLSearchParams(prev).toString()
        );

        searchParams.delete(key as string);

        return searchParams;
      },
      {
        replace: true,
      }
    );
  };

  const query: Record<string, string | number> = {};

  for (const key of searchParams.keys()) {
    query[key] = isNaN(Number(searchParams.get(key)))
      ? String(searchParams.get(key))
      : Number(searchParams.get(key));
  }

  query.page = query.page || defaultListQuery.page;
  query.limit = query.limit || defaultListQuery.limit;
  query.search = query.search || defaultListQuery.search;

  let sortDescriptor: SortDescriptor | undefined;
  if (query.sort) {
    sortDescriptor = {
      column: String(query.sort).replace(/^-/, ""),
      direction: String(query.sort).includes("-") ? "descending" : "ascending",
    };
  }

  const handleSortChange = (sort: SortDescriptor) => {
    const sortString = fieldSort(String(sort.column), sort.direction);

    updateQuery({
      sort: sortString,
    } as Partial<ListQuery<T>>);
  };

  return {
    query,
    sortDescriptor,
    updateQuery,
    removeQuery,
    handleSortChange,
  };
}

export default useListQuery;
