import React, { SVGProps } from "react"
import {
  Table as TableNextUI,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  DropdownTrigger,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Chip,
  Pagination,
  Selection,
  ChipProps,
  SortDescriptor
} from "@heroui/react"
import { ListFilter, LucideIcon, Pencil, Plus, Trash2 } from "lucide-react"
import { useTranslation } from "react-i18next"
import { AcceptIcon } from "../icons/AcceptIcon"
import { RejectIcon } from "../icons/RejectIcon"
import { ReviewIcon } from "../icons/ReviewIcon"
import { useNavigate } from "@remix-run/react"
import {
  getTypeOfCarbonName,
  getUseOfGhgErsName
} from "~/lib/performanceDetail"

export type IconSvgProps = SVGProps<SVGSVGElement> & {
  size?: number
}

export function capitalize(s: string) {
  return s ? s.charAt(0).toUpperCase() + s.slice(1).toLowerCase() : ""
}

const statusColorMap: Record<string, ChipProps["color"]> = {
  accept: "success",
  reject: "danger",
  review: "warning"
}

type TOption = { name: string; uid: string }
type TColumn = {
  name: string
  uid: string
  sortable?: boolean
}
type TProps = {
  title?: string
  titleIcon?: LucideIcon
  filterOptions?: TOption[]
  onAdd?: () => void
  columns: TColumn[]
  data: any[]
  onEdit?: () => void
  onSort?: (sortDescriptor: SortDescriptor) => void
}

export default function Table({
  title,
  titleIcon: TitleIcon,
  filterOptions,
  onAdd,
  columns,
  data,
  onEdit
}: TProps) {
  type User = (typeof data)[0]
  const [filterValue, setFilterValue] = React.useState("")
  const [selectedKeys, setSelectedKeys] = React.useState<Selection>(new Set([]))
  const [statusFilter, setStatusFilter] = React.useState<Selection>("all")
  const [rowsPerPage, setRowsPerPage] = React.useState(100)

  const [page, setPage] = React.useState(1)

  const hasSearchFilter = Boolean(filterValue)

  const filteredItems = React.useMemo(() => {
    let filteredUsers = [...data]

    if (hasSearchFilter) {
      filteredUsers = filteredUsers.filter(user =>
        user.name.toLowerCase().includes(filterValue.toLowerCase())
      )
    }
    if (
      statusFilter !== "all" &&
      Array.from(statusFilter).length !== filterOptions?.length
    ) {
      filteredUsers = filteredUsers.filter(user =>
        Array.from(statusFilter).includes(user.status)
      )
    }

    return filteredUsers
  }, [data, filterValue, statusFilter])

  const pages = Math.ceil(filteredItems.length / rowsPerPage)

  const items = React.useMemo(() => {
    const start = (page - 1) * rowsPerPage
    const end = start + rowsPerPage

    return filteredItems.slice(start, end)
  }, [page, filteredItems, rowsPerPage])

  const renderCell = React.useCallback((user: User, columnKey: React.Key) => {
    const cellValue = user[columnKey as keyof User]

    switch (columnKey) {
      case "name":
        return (
          <p>
            {user.firstName} {user.lastName}
          </p>
        )
      case "project_name":
        return <p>{user.project?.name}</p>
      case "type_of_carbon_mechanism":
        return (
          <p>
            {getTypeOfCarbonName(user.type_of_carbon_mechanism)
              ? getTypeOfCarbonName(user.type_of_carbon_mechanism)
              : user?.other_type_of_carbon_mechanism}
          </p>
        )
      case "user_of_ghg_ers":
        return <p>{getUseOfGhgErsName(user.user_of_ghg_ers)}</p>
      case "role":
        return (
          <p>
            {cellValue
              ? t(`user.role.${cellValue}`)
              : t(`user.role.project_developer`)}
          </p>
        )
      case "status":
        return (
          <Chip
            className="capitalize"
            startContent={
              statusColorMap[user.status] === "danger" ? (
                <RejectIcon />
              ) : statusColorMap[user.status] === "warning" ? (
                <ReviewIcon />
              ) : (
                <AcceptIcon />
              )
            }
            color={statusColorMap[user.status]}
            variant="bordered">
            {cellValue}
          </Chip>
        )
      case "actions":
        return (
          <div className="relative flex justify-end items-center gap-2">
            {onEdit && (
              <Button
                isIconOnly
                aria-label="Edit"
                size="sm"
                color="success"
                onPress={onEdit}>
                <Pencil width={15} height={15} />
              </Button>
            )}

            <Button isIconOnly aria-label="Delete" color="danger" size="sm">
              <Trash2 width={15} height={15} />
            </Button>
          </div>
        )
      default:
        return cellValue ? cellValue : "N/A"
    }
  }, [])

  const onRowsPerPageChange = React.useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      setRowsPerPage(Number(e.target.value))
      setPage(1)
    },
    []
  )

  const onSearchChange = React.useCallback((value?: string) => {
    if (value) {
      setFilterValue(value)
      setPage(1)
    } else {
      setFilterValue("")
    }
  }, [])

  const onClear = React.useCallback(() => {
    setFilterValue("")
    setPage(1)
  }, [])
  const { t } = useTranslation()

  const topContent = React.useMemo(() => {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center gap-2">
          <div>
            <div className="flex space-x-2">
              {TitleIcon && <TitleIcon />}
              {title && <div>{title}</div>}
            </div>
          </div>
          <div>
            <div className="flex space-x-2">
              {filterOptions && (
                <div>
                  <Dropdown>
                    <DropdownTrigger className="hidden sm:flex">
                      <Button variant="bordered" startContent={<ListFilter />}>
                        {t("common.filter")}
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      disallowEmptySelection
                      aria-label="Table Columns"
                      closeOnSelect={false}
                      selectedKeys={statusFilter}
                      selectionMode="multiple"
                      onSelectionChange={setStatusFilter}>
                      {filterOptions?.map(status => (
                        <DropdownItem key={status.uid} className="capitalize">
                          {capitalize(status.name)}
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                </div>
              )}
              {onAdd && (
                <div>
                  <Button
                    onPress={onAdd}
                    color="primary"
                    startContent={<Plus />}>
                    {t("common.add")}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }, [
    filterValue,
    statusFilter,
    onSearchChange,
    onRowsPerPageChange,
    data.length,
    hasSearchFilter
  ])

  const bottomContent = React.useMemo(() => {
    return (
      <div className="py-2 px-2 flex justify-between items-center">
        <span className="w-[30%] text-small text-default-400">
          page {page} of {pages}
        </span>
        <Pagination
          isCompact
          showControls
          showShadow
          color="primary"
          page={page}
          total={pages}
          onChange={setPage}
        />
        <div className="hidden sm:flex w-[30%] justify-end gap-2">
          <label className="flex items-center text-default-400 text-small">
            Rows per page:
            <select
              className="bg-transparent outline-none text-default-400 text-small"
              onChange={onRowsPerPageChange}>
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="15">15</option>
            </select>
          </label>
        </div>
      </div>
    )
  }, [selectedKeys, items.length, page, pages, hasSearchFilter])
  const navigate = useNavigate()
  return (
    <TableNextUI
      isHeaderSticky
      aria-label="Example table with custom cells, pagination and sorting"
      bottomContent={items.length > 0 ? bottomContent : null}
      bottomContentPlacement="outside"
      selectedKeys={selectedKeys}
      selectionMode="single"
      topContent={topContent}
      topContentPlacement="outside"
      onSelectionChange={setSelectedKeys}>
      <TableHeader columns={columns}>
        {column => (
          <TableColumn
            key={column.uid}
            align={column.uid === "actions" ? "center" : "start"}>
            {column.name}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody emptyContent={"No users found"} items={items}>
        {item => (
          <TableRow
            className="cursor-pointer"
            key={item.id}
            onClick={() => {
              item?.environmental_integrity_detail
                ? navigate(`/admin/pdd-reports/${item.id}`)
                : item?.project_id
                ? title
                  ? navigate(`/admin/performance-reports/${item.id}`)
                  : navigate(`/performance-report/${item?.id}`)
                : navigate("/admin/users/" + item.id)
            }}>
            {columnKey => <TableCell>{renderCell(item, columnKey)}</TableCell>}
          </TableRow>
        )}
      </TableBody>
    </TableNextUI>
  )
}
