import {
  Autocomplete,
  AutocompleteItem,
  Button,
  Radio,
  RadioGroup
} from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ArrowRightIcon } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { ERole } from "~/enums/EUserRole"
import { fieldError } from "~/helpers/form"
import Input, { baseInputClassName } from "../ui/input"
import InputPassword from "../ui/inputPassword"
import FileSelect from "./FileSelect"

type ErrorType = {
  address?: string[]
  email?: string[]
  password?: string[]
  confirmPassword?: string[]
  firstName?: string[]
  lastName?: string[]
  nationalID?: string[]
  [key: string]: string[] | undefined // To allow additional properties
}

const ApplicationFormStep = ({
  error,
  organizations
}: {
  error?: ErrorType | null
  organizations: any[] | null
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const accountType = [
    {
      label: "project_developers",
      value: ERole.ProjectDeveloper,
      description: "For entities overseeing individual  projects."
    },
    {
      label: "third-party_verifiers",
      value: ERole.IndependentAuditor,
      description: "For organizations validating and verifying carbon credits."
    }
  ]

  const supportDocumentFields = [
    {
      name: "national_id_passport_document",
      label: "National ID or Passport"
    },
    {
      name: "business_registration_document",
      label: "Business Registration Document"
    },
    {
      name: "organization_authorization_letter_document",
      label: "Organization Authorization Letter"
    }
  ]

  const [selectOrg, setSelectOrg] = useState<any>()

  return (
    <div className="container max-w-3xl mx-auto space-y-6 px-5">
      <div className="flex justify-between">
        <div className="text-primary">
          <p className="text-lg font-bold">{t("auth.registration.form")}</p>
        </div>
        <div className="text-primary">
          <p className="text-sm">Step 3 of 3</p>
        </div>
      </div>
      <hr />
      <p className="text-center">
        Complete the application form below with accurate about yourself or your
        organization. Provide personal or organizational details as required,
        upload necessary documents like identification or authorization letters,
        and double-check all entries for accuracy before submitting the form to
        avoid delays.
      </p>
      <div className="bg-white shadow-md border p-6 rounded-xl ">
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="font-bold text-[20px]">
                {t("auth.registration.account_type")}
              </p>
            </div>
            <div>
              <RadioGroup color="primary" name="role" isRequired>
                {accountType?.map((account, index) => {
                  return (
                    <Radio
                      key={index}
                      description={account.description}
                      value={account.value}>
                      {t(`auth.registration.${account.label}`)}
                    </Radio>
                  )
                })}
              </RadioGroup>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="font-bold text-[20px]">Organization Information</p>
            </div>

            <div className="grid grid-cols-2 gap-x-4 gap-y-2 bg-gray-100 rounded-xl p-5">
              <div className="col-span-2">
                <Autocomplete
                  isRequired
                  name="org.name"
                  maxListboxHeight={200}
                  allowsCustomValue
                  defaultItems={organizations ? organizations : []}
                  size="lg"
                  placeholder="Organization Name"
                  inputProps={{
                    classNames: baseInputClassName
                  }}
                  label="Organization Name"
                  variant="bordered"
                  onSelectionChange={(selectedItems: any) => {
                    const filter = organizations?.filter(
                      org => org.id === Number(selectedItems)
                    )
                    setSelectOrg(filter ? filter[0] : null)
                  }}>
                  {item => (
                    <AutocompleteItem key={item.id}>
                      {item.name}
                    </AutocompleteItem>
                  )}
                </Autocomplete>
                {error && error["org.name"] && fieldError(error["org.name"][0])}
                <input type="hidden" name="org.id" value={selectOrg?.id} />
              </div>
              <div
                key={selectOrg?.id ?? -1}
                className="grid col-span-2 gap-x-4 gap-y-2">
                <div className="col-span-2 ">
                  <Input
                    isDisabled={Boolean(selectOrg?.id)}
                    value={selectOrg?.address}
                    isRequired
                    label="Address"
                    name="org.address"
                    placeholder="Organization Address"
                  />
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.province}
                    isRequired
                    label={t("auth.registration.province")}
                    name="org.province"
                    placeholder={t("auth.registration.enter_province")}
                  />
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.district}
                    isRequired
                    label={t("auth.registration.district")}
                    name="org.district"
                    placeholder={t("auth.registration.enter_province")}
                  />
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.commune}
                    name="org.commune"
                    label={t("auth.registration.commune")}
                    placeholder={t("auth.registration.choose_your_commune")}
                  />
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.postal_code}
                    label={t("auth.registration.postal_code")}
                    name="org.postal_code"
                    placeholder={t("auth.registration.enter_postal_code")}
                  />
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    isRequired
                    value={selectOrg?.email}
                    type="email"
                    label="Organization Email"
                    name="org.email"
                    placeholder="Enter Organization Email"
                  />
                  {error &&
                    error["org.email"] &&
                    fieldError(error["org.email"][0])}
                </div>
                <div className="col-span-2 md:col-span-1">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.phone}
                    isRequired
                    label="Organization Phone"
                    name="org.phone"
                    placeholder="Enter your organization number"
                  />
                </div>
                <div className="col-span-2 ">
                  <Input
                    isDisabled={selectOrg?.id ? true : false}
                    value={selectOrg?.website}
                    isRequired
                    label="Organization Website"
                    name="org.website"
                    placeholder="Organization Website"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="font-bold text-[20px]">Account Manager (holder)</p>
            </div>
            <div className="grid grid-cols-2 gap-x-4 gap-y-2 bg-gray-100 rounded-xl p-5">
              <div className="col-span-1 md:col-span-2">
                <Input
                  isRequired
                  label="Job Title"
                  name="job_title"
                  placeholder="Enter your job title"
                />
              </div>
              <div className="col-span-1 md:col-span-2">
                <Input
                  isRequired
                  label="Nationality"
                  name="nationality"
                  placeholder="Enter nationality"
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  isRequired
                  label={t("auth.registration.first_name")}
                  name="firstName"
                  placeholder={t("auth.registration.enter_first_name")}
                />
                {error &&
                  error["firstName"] &&
                  fieldError(error["firstName"][0])}
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  isRequired
                  label={t("auth.registration.last_name")}
                  name="lastName"
                  placeholder={t("auth.registration.enter_last_name")}
                />
                {error && error["lastName"] && fieldError(error["lastName"][0])}
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  isRequired
                  label="Passport or National ID"
                  name="passport_national_id"
                  placeholder="Enter your Passport number or National ID"
                />
              </div>
              <div className="col-span-2 md:col-span-1">
                <Input
                  isRequired
                  label={t("auth.registration.phone_number")}
                  name="phone"
                  placeholder={t("auth.registration.enter_phone_number")}
                />
              </div>
              <div>
                <p>Login Information</p>
              </div>
              <div className="col-span-2">
                <Input
                  isRequired
                  type="email"
                  label={t("auth.registration.email")}
                  name="email"
                  placeholder={t("auth.registration.enter_email")}
                />
                {error && error.email && fieldError(error.email[0])}
              </div>
              <div className="col-span-2 md:col-span-1">
                <InputPassword
                  label={t("auth.registration.password")}
                  name="password"
                  isRequired
                  placeholder={t("auth.registration.enter_password")}
                />
                {error && error.password && fieldError(error.password[0])}
              </div>
              <div className="col-span-2 md:col-span-1">
                <InputPassword
                  label={t("auth.registration.confirm_password")}
                  name="confirm_password"
                  isRequired
                  placeholder={t("auth.registration.enter_confirm_password")}
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="font-bold text-[20px]">Support Document</p>
            </div>
            {supportDocumentFields.map((item, index) => (
              <FileSelect
                key={item.name}
                name={item.name}
                order={index + 1}
                label={item.label}
              />
            ))}
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Button onPress={() => navigate(-1)} color="primary" variant="light">
          {t("auth.registration.cancel")}
        </Button>
        <Button
          type={"submit"}
          color="primary"
          endContent={<ArrowRightIcon className="size-5" />}>
          {t("common.submit")}
        </Button>
      </div>
    </div>
  )
}

export default ApplicationFormStep
