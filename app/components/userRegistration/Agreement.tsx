import { But<PERSON>, Checkbox } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ArrowRightIcon } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"

const AgreementsStep = ({ onContinue }: { onContinue: () => void }) => {
  const { t } = useTranslation()
  const [isRead, setIsRead] = useState(false)
  const navigate = useNavigate()
  return (
    <div className="container max-w-3xl mx-auto space-y-6 px-5">
      <div className="flex justify-between">
        <div className="text-primary">
          <p className="text-lg font-bold">
            {t("auth.registration.agreement")}
          </p>
        </div>
        <div className="text-primary">
          <p className="text-sm">Step 2 of 3</p>
        </div>
      </div>
      <hr />
      <p className="text-center">
        Carefully read and accept the terms and conditions for using the
        Cambodia Carbon Registry. This step ensures you understand your
        responsibilities and the rules of the system. Take the time to{" "}
        <b>download</b> or save a copy of the agreements for your records if
        needed, and confirm your acceptance by checking the provided box before
        moving forward.
      </p>
      <div className="bg-white shadow-md border p-6 rounded-xl max-h-[34rem] overflow-y-auto">
        <>
          <p>
            <b>General Terms of Use</b>
            <br />
            <p className={"mt-6"}>
              The User acknowledges and agrees that when using the Cambodia
              Carbon Registry, the User will be subject to, and must comply
              with, these Terms of Use as modified from time to time in
              accordance with the terms hereof. Where there is any inconsistency
              between these Terms of Use and the Program Rules and Requirements
              of a the Registry Program, the Program Rules and Requirements of
              the relevant the Registry Program will prevail over these Terms of
              Use. In addition, the User agrees to comply with any and all
              applicable Scheme Regulations imposed and updated from time to
              time by a third-party Scheme Regulator. If the User does not agree
              to these Terms of Use, the User may not access or otherwise use
              the Registry. Specified business activities The User may not
              conduct any of the following business activities in relation to
              its use of the Cambodia Carbon Registry without providing advance
              written notice to Cambodia Carbon Registry: <br />
              (a) purchasing, holding, transferring, retiring or cancelling
              Instruments on behalf of third parties; or
              <br /> (b) acting as Agent on behalf of a Principal who owns or
              intends to own Instruments. Where the User conducts the business
              activities referred to in Clause 1.5, the User represents and
              warrants that: VERRA REGISTRY - TERMS OF USE – OCTOBER 2024 4 (a)
              in carrying out such business activities it holds all necessary
              securities and/or financial services licenses and approvals
              required to undertake these activities in each of the
              jurisdictions that it is performing such business activities in;
              and (b) where applicable, that Know-Your-Client checks have been
              conducted on each of the customers it is undertaking the business
              activities on behalf of; and
              <br /> (c) if acting as Agent, it has full, valid and current
              authority to represent and act on behalf of the Principal (who
              shall be fully liable for the acts and omission of the Agent) and
              that such authority has not been revoked. If the User conducts any
              of the business activities referred to in Clause 1.5, it shall
              provide such evidence as is required by Verra to confirm its
              authority to undertake each such activity. The User shall
              immediately notify Verra if any authority or permission required
              to undertake the relevant business activity is amended or revoked.
              Verra reserves the right to grant, limit, suspend, or remove the
              User's access to certain functionality in the Verra Registry that
              supports the business activities identified in Clause 1.5 at any
              time. Related instruments The User may not conduct any of the
              following business activities without the express written consent
              of Verra, which is to be granted in Verra’s sole and absolute
              discretion: <br />
              (a) creating Related Instruments; <br />
              (b) marketing Related Instruments; <br />
              (c) transacting in Related Instruments in any form whatsoever. If
              the User conducts any of the business activities referred to in
              Clause 1.9, it shall provide such evidence as is required by Verra
              to confirm its ability (including its technical competence and
              financial standing) and authority (including its legal capacity
              and regulatory permissions and authorities) to undertake each such
              activity. The User shall immediately notify Verra if there is any
              material change in such ability or authority, including whether
              any authority or permission required to undertake the relevant
              business activity is amended or revoked. Verra reserves the right
              to grant, limit, suspend or remove the User's access to certain
              functionality in the Verra Registry that supports the business
              activities identified in Clause 1.9 at any time.
            </p>
          </p>
        </>
      </div>
      <div className="grid justify-center">
        <Checkbox
          isSelected={isRead}
          color="primary"
          onChange={() => {
            setIsRead(prev => !prev)
          }}>
          {t("auth.login.remember_me")}&nbsp;
          {t("auth.registration.read_term_uses")}
        </Checkbox>
      </div>
      <div className="flex justify-end gap-2">
        <Button onPress={() => navigate(-1)} color="primary" variant="light">
          {t("auth.registration.cancel")}
        </Button>
        <Button
          onPress={onContinue}
          isDisabled={!isRead}
          color="primary"
          endContent={<ArrowRightIcon className="size-5" />}>
          {t("auth.registration.continue")}
        </Button>
      </div>
    </div>
  )
}

export default AgreementsStep
