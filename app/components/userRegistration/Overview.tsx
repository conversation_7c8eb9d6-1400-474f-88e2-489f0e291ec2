import { But<PERSON> } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ArrowRightIcon } from "lucide-react"
import { useTranslation } from "react-i18next"

const OverviewStep = ({ onContinue }: { onContinue: () => void }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  return (
    <div className="container max-w-3xl mx-auto space-y-6 px-5">
      <div className="flex justify-between">
        <div className="text-primary">
          <p className="text-lg font-bold">{t("auth.registration.overview")}</p>
        </div>
        <div className="text-primary">
          <p className="text-sm">Step 1 of 3</p>
        </div>
      </div>
      <hr />
      <p className="text-center">
        Please review the following account registration process. This step
        introduces you to the Cambodia Carbon Registry, its purpose, and how you
        can benefit from being a registered user. Make sure you understand the
        process and prepare the required documents in advance.
      </p>
      <div className="bg-white shadow-md border p-6 rounded-xl max-h-[38rem] overflow-y-auto">
        <>
          <p>
            <b>1. Understand the Cambodia Carbon Registry System Familiarize</b>
            <br />
            Familiarize yourself with the purpose and functionality of the
            Cambodia Carbon Registry, including its role in managing and tracking
            carbon credits and Cambodia Carbon Registry program information.
          </p>
          <br />
          <p>
            <b>2. Gather Required Information and Documents</b>
            <br />
            Prepare the following information and documents in advance: Personal
            Identification: Valid government-issued ID or passport for the
            account owner. Organizational Details (if applicable): Legal name,
            registration number, and proof of authority for representing the
            organization. Contact Information: Email address and phone number
            for the primary account manager. Supporting Documents: Any specific
            forms or agreements required by the Cambodia Carbon Registry.
          </p>
          <br />
          <p>
            <b>3. Review Terms and Conditions</b>
            <br />
            Before applying, carefully read the terms and conditions of using
            the Cambodia Carbon Registry. Ensure that you understand your
            responsibilities and the system's data privacy policies.
          </p>
          <br />
          <p>
            <b>4. Submit an Application</b>
            <br />
            Visit the Cambodia Carbon Registry system website. Navigate to the
            "Account Registration" or "New Account" section. Fill in the
            required fields with accurate information, ensuring no errors.
            Upload the necessary supporting documents as per the instructions on
            the website.
          </p>
          <br />
          <p>
            <b>5. Application Review</b>
            <br />
            Once submitted, your application will undergo a review by the
            Registry Administrator. Be prepared to respond to any follow-up
            questions or provide additional documentation if requested.
          </p>
          <br />
          <p>
            <b>6. Approval Notification</b>
            <br />
            If your application meets all the requirements, you will receive an
            approval notification via email. This will include: Account login
            credentials. Instructions for accessing and using the Cambodia Carbon Registry.
          </p>
          <br />
          <p>
            <b>7. First-Time Login and Setup</b>
            <br />
            Log in using the provided credentials and: Verify your information.
            Set up a secure password. Familiarize yourself with the dashboard
            and features of the registry
          </p>
          <br />
          <p>
            <b>8. Begin Using the System</b>
            <br />
            You can now manage your Cambodia Carbon Registry projects, submit reports, and monitor
            transactions in compliance with national and international
            standards.
          </p>
        </>
      </div>
      <div className="flex justify-end gap-2">
        <Button onPress={() => navigate(-1)} color="primary" variant="light">
          {t("auth.registration.cancel")}
        </Button>
        <Button
          onPress={onContinue}
          color="primary"
          endContent={<ArrowRightIcon className="size-5" />}>
          {t("auth.registration.continue")}
        </Button>
      </div>
    </div>
  )
}
export default OverviewStep
