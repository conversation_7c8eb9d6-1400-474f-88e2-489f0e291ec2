import { Button } from "@heroui/react"
import { useState } from "react"

type FileSelectProps = {
  order?: number
  label: string
  name: string
  fileNameSelected?: string | null
  acceptFileTypes?: string
}
const FileSelect = ({
  order,
  label,
  name,
  fileNameSelected,
  acceptFileTypes
}: FileSelectProps) => {
  const [fileName, setFileName] = useState(
    fileNameSelected ? fileNameSelected : ""
  )
  const [error, setError] = useState(false)
  return (
    <div>
      <div
        className={
          error
            ? "flex  border-[#fc0a37] border-[2px] rounded-2xl space-x-2 p-3"
            : "flex  border-[#A1A1AA] border-[2px] rounded-2xl space-x-2 p-3"
        }>
        <div>
          <p className={error ? "text-red-600" : "text-gray-700"}>{order}</p>
        </div>
        <div className="flex-1 space-y-1">
          <div>
            <p className={error ? "text-red-600" : "text-gray-700 text-sm"}>
              {label}
              <span className="text-red-600">*</span>
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button as="label">
              <p className="text-[14px]">Choose File</p>
              <input
                required={!fileName} // Only required if there is no file
                onInvalid={() => {
                  if (!fileName) setError(true)
                }}
                accept={acceptFileTypes}
                type="file"
                onChange={event => {
                  const file = event.target.files?.[0]
                  if (file) {
                    setFileName(file.name)
                  }
                  setError(false)
                }}
                className="hidden"
                name={name}
              />
            </Button>
            <p>{fileName || "No File Chosen"}</p>
          </div>
        </div>
      </div>
      {error && (
        <p className="text-red-600 text-xs ml-2 mt-1">Need to select File</p>
      )}
    </div>
  )
}

export default FileSelect
