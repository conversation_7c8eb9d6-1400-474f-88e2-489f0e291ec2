import { useFetcher } from "@remix-run/react"
import { useEffect } from "react"
import { useTranslation } from "react-i18next"
import { action } from "~/routes/change-language"

interface Props {
  className?: string
}

export default function LanguageSwitcher({ className }: Readonly<Props>) {
  const { i18n } = useTranslation()
  const fetcher = useFetcher<typeof action>()
  const lang = i18n.language
  const setLang = i18n.changeLanguage
  const data = fetcher.data
  const isSubmitting = fetcher.state !== "idle"
  const text = lang === "en" ? "ភាសាខ្មែរ" : "English"
  const toLang = lang === "en" ? "km" : "en"

  useEffect(() => {
    if (data && data.lang) {
      i18n.changeLanguage(data.lang)
      if (setLang) setLang(data.lang)
    }
  }, [data, setLang, i18n])

  const spinner = <span className="loading loading-sm loading-ring" />

  return (
    <fetcher.Form method="POST" action="/change-language" className={className}>
      <input type="hidden" name="lang" value={toLang} />
      <button
        disabled={isSubmitting}
        className="btn btn-ghost btn-sm capitalize">
        {isSubmitting ? spinner : <span>{text}</span>}
      </button>
    </fetcher.Form>
  )
}
