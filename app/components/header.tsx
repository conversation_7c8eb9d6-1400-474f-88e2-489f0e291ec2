import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer<PERSON>eader,
  useDisclosure
} from "@heroui/react"
import { Link, useLoaderData, useLocation, useNavigate } from "@remix-run/react"
import {
  Archive,
  Factory,
  FileText,
  FolderGit2,
  House,
  Layout,
  LogOut,
  <PERSON>u as MenuIcon,
  User,
  UserCircle,
  X
} from "lucide-react"
import { useTranslation } from "react-i18next"
import { ERole } from "~/enums/EUserRole"
import type { loader } from "~/routes/_page"
import type { AuthUser } from "~/types"
import Logo from "./logo"
import DropDownMenu from "./menu/DropDownMenu"

interface Props {
  user: AuthUser | null
}

export const ListboxWrapper = ({ children }: { children: any }) => (
  <div className="w-full max-w-[260px] border-small px-1 py-2 rounded-small border-default-200 dark:border-default-100">
    {children}
  </div>
)

export default function Header({ user }: Props) {
  const { t } = useTranslation()
  const { isOpen, onO<PERSON>, onOpenChange } = useDisclosure()
  const menus = [
    { label: "Home", link: "/", exactMatch: true, icon: House },
    {
      label: "Project Database",
      link: "/projects",
      icon: Factory
    },
    { label: "Resources", link: "/resources", exactMatch: true, icon: Archive },
    {
      label: "How it Works",
      link: "/how-it-works",
      exactMatch: true,
      icon: FolderGit2
    },
    { label: "About", link: "/about", exactMatch: true, icon: FileText }
  ]
  const { notifications } = useLoaderData<typeof loader>()

  const navigate = useNavigate()
  const location = useLocation()

  return (
    <div
      className={
        "lg:container mx-auto py-4 px-4 lg:px-0 flex relative items-center h-20"
      }>
      <div>
        <Logo href="/" />
      </div>
      <div className="flex-1 flex lg:hidden justify-end">
        {user ? (
          <Button isIconOnly onPress={onOpen}>
            <MenuIcon />
          </Button>
        ) : (
          <Link to="/login">Login</Link>
        )}
      </div>
      <div className="absolute left-1/2 -translate-x-1/2 hidden lg:flex">
        {menus.map((item, index) => {
          const isActive = (
            item?.exactMatch
              ? location.pathname === item?.link
              : location.pathname.startsWith(item?.link)
          )
            ? true
            : false

          return (
            <Link
              to={item?.link}
              key={index}
              className={`px-4 py-2 rounded text-center ${
                isActive ? "bg-[#0C8442] text-white rounded-xl" : "text-black"
              }`}>
              {item?.label}
            </Link>
          )
        })}
      </div>

      <Drawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        closeButton={<X width="40px" height="40px" />}>
        <DrawerContent>
          {onClose => (
            <>
              <DrawerHeader className="flex flex-col gap-1">
                <div className="flex space-x-4 items-center">
                  <div>
                    {user?.photoUrl ? (
                      <Avatar
                        onClick={() => {
                          user.isAdmin || user.isSuper
                            ? navigate("/admin/profile")
                            : navigate("/profile")
                        }}
                        src={user.photoUrl}
                        alt={`${user.firstName} ${user.lastName}`}
                        className="w-8 h-8"
                      />
                    ) : (
                      <UserCircle
                        size={32}
                        className="cursor-pointer"
                        onClick={() => {
                          user?.isAdmin || user?.isSuper
                            ? navigate("/admin/profile")
                            : navigate("/profile")
                        }}
                      />
                    )}
                  </div>
                  <div>
                    <p>
                      {user?.firstName}&nbsp;
                      {user?.lastName}
                    </p>
                  </div>
                </div>
              </DrawerHeader>
              <DrawerBody className="border-t-2 m-0 p-0">
                <div className="mt-3">
                  {menus.map((item, index) => {
                    const isActive = (
                      item?.exactMatch
                        ? location.pathname === item?.link
                        : location.pathname.startsWith(item?.link)
                    )
                      ? true
                      : false

                    const Icon = item.icon
                    return (
                      <div
                        key={index}
                        className={`${
                          isActive ? "bg-[#0C8442]" : "hover:bg-gray-300"
                        } `}>
                        <Link
                          to={item?.link}
                          title={item?.label}
                          className={`px-4 py-2 rounded ${
                            isActive ? " text-white" : "text-black"
                          }`}
                          onClick={onClose}>
                          <div className="flex space-x-2 px-3">
                            <div>
                              <Icon />
                            </div>
                            <div>{item?.label}</div>
                          </div>
                        </Link>
                      </div>
                    )
                  })}

                  <div className={"hover:bg-gray-300"}>
                    <Link
                      to={
                        user?.role === ERole.ProjectDeveloper
                          ? "/user"
                          : "admin"
                      }
                      title={"Logout"}
                      onClick={onClose}
                      className={`px-4 py-2 rounded`}>
                      <div className="flex space-x-2 px-3">
                        <div>
                          <Layout />
                        </div>
                        <div>Dashboard</div>
                      </div>
                    </Link>
                  </div>

                  <div className={"hover:bg-gray-300"}>
                    <Link
                      to={
                        user?.role === ERole.ProjectDeveloper
                          ? "/profile"
                          : "/admin/profile"
                      }
                      title={"profile"}
                      onClick={onClose}
                      className={`px-4 py-2 rounded`}>
                      <div className="flex space-x-2 px-3">
                        <div>
                          <User />
                        </div>
                        <div>Profile</div>
                      </div>
                    </Link>
                  </div>

                  <div className={"hover:bg-gray-300"}>
                    <Link
                      to={"/logout"}
                      title={"Logout"}
                      onClick={onClose}
                      className={`px-4 py-2 rounded`}>
                      <div className="flex space-x-2 px-3">
                        <div>
                          <LogOut />
                        </div>
                        <div>Logout</div>
                      </div>
                    </Link>
                  </div>
                </div>
              </DrawerBody>
            </>
          )}
        </DrawerContent>
      </Drawer>

      {!user ? (
        <div className="ml-auto hidden lg:flex gap-5">
          <div className="items-center flex">
            <Link to="/login">Login</Link>
          </div>
          <div>
            <Button
              color="primary"
              onPress={() => {
                navigate("/registration")
              }}>
              {t("common.open_account")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="ml-auto hidden lg:flex gap-5">
          {user && (
            <div className="flex items-center lg:gap-4 gap-2">
              <DropDownMenu notifications={notifications} user={user} />
            </div>
          )}
        </div>
      )}
    </div>
  )
}
