import { Link } from "@remix-run/react"
import { useTranslation } from "react-i18next"
import Logo from "./logo"
import { Mail, Phone } from "lucide-react"

const Footer = () => {
  const { t } = useTranslation()
  const menus = [
    { label: "Home", link: "/" },
    { label: "Project Database", link: "/projects" },
    { label: "Resources", link: "/resources" },
    { label: "About", link: "/about" }
  ]
  return (
    <footer className="border-t border-t-base-200 mt-12 px-4 md:px-0">
      <div className="container mx-auto py-5 space-y-4">
        <div className="grid md:flex w-full ">
          <div className="flex">
            <Logo />
          </div>
          <div className="flex-1 flex flex-wrap gap-2 justify-start md:justify-end items-center pt-5 md:py-0">
            {menus.map((item, index) => {
              return (
                <Link to={item?.link} key={index}>
                  {item?.label}
                  {index < menus.length - 1 ? "   |" : ""}
                </Link>
              )
            })}
          </div>
        </div>
        <div className="max-w-64">
          <p className="text-sm my-4">
            REDD Secretariat and
            Carbon Crediting Secretariat
            Ministry of Environment
            Lot 503, Morodok Techo Building, Phnom Penh, Cambodia
          </p>
        </div>
        <div className="flex space-x-4">
          <div>
            <div className="flex space-x-2">
              <div>
                <Phone className="size-5" />
              </div>
              <div>
                <a href="tel:+85523213908" className="text-sm hover:underline">
                  +855 23 213 908
                </a>
              </div>
            </div>
          </div>
          <div>
            <div className="flex space-x-2">
              <div>
                <Mail className="size-5" />
              </div>
              <div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm  hover:underline">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="border-base-200 border-t py-6 text-sm">
        <div className="container mx-auto ">
          <div className="flex flex-col-reverse items-center gap-2 md:flex-row md:justify-between w-full opacity-60">
            <div>
              © Copyright {new Date().getFullYear()} Cambodia Carbon Registry
            </div>
            <div className="flex">
              <a href="/privacy" title="Terms and Conditions">
                Privacy Policy
              </a>
              <span className="divider divider-horizontal mx-1"></span>
              <a href="/terms-of-use" title="Terms and Conditions">
                Terms Of Use
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
