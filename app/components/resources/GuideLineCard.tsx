import { Card } from "@heroui/react"

interface GuideLineCardProps {
  title: string
  itemList: string[]
}
const GuideLineCard = ({ title, itemList }: GuideLineCardProps) => {
  return (
    <div className="">
      <Card className="p-5 pl-10 h-64 bg-[#F4F4F5]">
        <div className="space-y-3">
          <p className=" text-primary subTitle">{title}</p>
          <div>
            <ul className="list-disc space-y-2">
              {itemList.map((item, index) => {
                return <li key={index}>{item}</li>
              })}
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default GuideLineCard
