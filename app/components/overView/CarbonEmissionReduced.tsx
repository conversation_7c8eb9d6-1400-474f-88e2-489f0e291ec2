import { <PERSON>, CardBody, CardHeader } from "@heroui/react"
import { Trees } from "lucide-react"

const CarbonEmissionReduced = ({
  carbonEmissionReduced
}: {
  carbonEmissionReduced: number
}) => {
  return (
    <Card className="p-4">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <h2 className="text-tiny uppercase">CARBON EMISSIONS REDUCED</h2>
        </div>
        <Trees size={18} />
      </CardHeader>
      <CardBody className="pt-0">
        <div className="my-4 font-bold text-6xl">{carbonEmissionReduced}</div>
        <div className="flex justify-between">
          <div>TONS</div>
          <div>Co2</div>
        </div>
      </CardBody>
    </Card>
  )
}

export default CarbonEmissionReduced
