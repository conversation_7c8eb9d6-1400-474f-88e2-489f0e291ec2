import { <PERSON>, Card<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react"
import {
  ArrowRight,
  BadgeX,
  CircleCheckBig,
  Clock,
  Factory,
  FilePenLine
} from "lucide-react"
import { useTranslation } from "react-i18next"

type IProjectOverview = {
  totalProject: number
  totalDraftProject: number
  totalSubmittedProject: number
  totalUnderReviewProject: number
  totalRejectProject: number
  totalApprovedProject: number
}

const ProjectOverView = ({
  projectsOverview
}: {
  projectsOverview: IProjectOverview
}) => {
  const { t } = useTranslation()

  return (
    <Card className="p-4">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <Factory className="mr-2" />
          <h2 className="text-tiny uppercase">{t("common.projects")}</h2>
        </div>
        <Link showAnchorIcon anchorIcon={<ArrowRight />} href="/user/projects">
          {t("common.view")}
        </Link>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="my-4 font-bold text-6xl">
          {projectsOverview?.totalProject}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
            <div className="flex items-center">
              <FilePenLine className="mr-2" />
              <h2 className="text-tiny uppercase">Draft</h2>
            </div>
            <span className="font-bold">
              {projectsOverview?.totalDraftProject}
            </span>
          </div>
          <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
            <div className="flex items-center">
              <BadgeX className="mr-2" />
              <h2 className="text-tiny uppercase">REJECTED</h2>
            </div>
            <span className="font-bold">
              {projectsOverview?.totalRejectProject}
            </span>
          </div>
        </div>
        <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
          <div className="flex items-center">
            <CircleCheckBig className="mr-2" />
            <h2 className="text-tiny uppercase">APPROVED</h2>
          </div>
          <span className="font-bold">
            {projectsOverview?.totalApprovedProject}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
            <div className="flex items-center">
              <FilePenLine className="mr-2" />
              <h2 className="text-tiny uppercase">Submiited</h2>
            </div>
            <span className="font-bold">
              {projectsOverview?.totalSubmittedProject}
            </span>
          </div>
          <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
            <div className="flex items-center">
              <Clock className="mr-2" />
              <h2 className="text-tiny uppercase">Under Review</h2>
            </div>
            <span className="font-bold">
              {projectsOverview?.totalUnderReviewProject}
            </span>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}

export default ProjectOverView
