import { Card, CardBody, CardHeader } from "@heroui/react"
import { Percent } from "lucide-react"

const ApprovalRate = () => {
  return (
    <Card className="p-4">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <h2 className="text-tiny uppercase">APPROVAL RATE</h2>
        </div>
        <Percent size={18} />
      </CardHeader>
      <CardBody className="pt-0">
        <div className="my-4 font-bold text-6xl">85%</div>
        <div className="flex justify-between">
          <div>Average Review Time</div>
          <div>14 days</div>
        </div>
      </CardBody>
    </Card>
  )
}

export default ApprovalRate
