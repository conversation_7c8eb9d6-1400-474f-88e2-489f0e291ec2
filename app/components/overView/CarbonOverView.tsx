import { <PERSON>, CardBody, CardH<PERSON>er, Link } from "@heroui/react"
import {
  ArrowRight,
  BadgeX,
  CircleCheckBig,
  CircleDollarSign,
  ShieldCheck
} from "lucide-react"
import { useTranslation } from "react-i18next"

const CarbonOverView = ({
  carbonCreditOverview,
  totalExpectCarbon
}: {
  totalExpectCarbon: any
  carbonCreditOverview: any
}) => {
  const { t } = useTranslation()

  return (
    <Card className="p-4">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <CircleDollarSign className="mr-2" />
          <h2 className="text-tiny uppercase">CREDITS</h2>
        </div>
        <Link showAnchorIcon anchorIcon={<ArrowRight />} href="/user/projects">
          {t("common.view")}
        </Link>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="my-4 font-bold text-6xl">
          {carbonCreditOverview?.totalIssuedCarbon}
        </div>
        <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
          <div className="flex items-center">
            <ShieldCheck className="mr-2" />
            <h2 className="text-tiny uppercase">ISSUED</h2>
          </div>
          <span className="font-bold">
            {carbonCreditOverview?.totalIssuedCarbon}
          </span>
        </div>
        <div className="p-4 mb-2 rounded-md bg-gray-100 flex justify-between">
          <div className="flex items-center">
            <CircleCheckBig className="mr-2" />
            <h2 className="text-tiny uppercase">AVAILABLE</h2>
          </div>
          <span className="font-bold">
            {Number(totalExpectCarbon) -
              Number(carbonCreditOverview?.totalIssuedCarbon)}
          </span>
        </div>
        <div className="p-4 rounded-md bg-gray-100 flex justify-between">
          <div className="flex items-center">
            <BadgeX className="mr-2" />
            <h2 className="text-tiny uppercase">RETIRED</h2>
          </div>
          <span className="font-bold">
            {carbonCreditOverview.totalRetiredCarbon}
          </span>
        </div>
      </CardBody>
    </Card>
  )
}

export default CarbonOverView
