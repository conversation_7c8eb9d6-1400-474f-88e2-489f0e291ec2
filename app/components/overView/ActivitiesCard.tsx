import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "@heroui/react"
import { ArrowR<PERSON>, Logs } from "lucide-react"

const ActivitiesCard = () => {
  return (
    <Card className="p-4 col-span-2">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <Logs className="mr-2" />
          <h2 className="text-tiny uppercase">ACTIVITIES</h2>
        </div>
        <Link showAnchorIcon anchorIcon={<ArrowRight />} href="/user/projects">
          More
        </Link>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="grid gap-2 pt-4">
          <div className="flex space-x-2 border-b-2 pb-2 mb-3">
            <div>
              <Chip size="sm" radius={"sm"}>
                Jan 5, 2025
              </Chip>
            </div>
            <div>Project D moved to “Approved”</div>
          </div>
          <div className="flex space-x-2 border-b-2 pb-2 mb-3">
            <div>
              <Chip size="sm" radius={"sm"}>
                Jan 5, 2025
              </Chip>
            </div>
            <div>5,000 Credits retired for Project E</div>
          </div>
          <div className="flex space-x-2 border-b-2 pb-2 mb-3">
            <div>
              <Chip size="sm" radius={"sm"}>
                Jan 20, 2025
              </Chip>
            </div>
            <div>Reviewer feedback added to Project F</div>
          </div>
          <div className="flex space-x-2 border-b-2 pb-2 mb-3">
            <div>
              <Chip size="sm" radius={"sm"}>
                Jan 5, 2025
              </Chip>
            </div>
            <div>5,000 Credits retired for Project E</div>
          </div>
          <div className="flex space-x-2">
            <div>
              <Chip size="sm" radius={"sm"}>
                Jan 12, 2025
              </Chip>
            </div>
            <div>5,000 Credits retired for Project E</div>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}

export default ActivitiesCard
