import { Card, CardBody, CardHeader, Checkbox, Chip, Link } from "@heroui/react"
import { <PERSON><PERSON><PERSON>, Square<PERSON>heckBig } from "lucide-react"

const TaskCard = () => {
  return (
    <Card className="p-4 col-span-2">
      <CardHeader className="flex justify-between pb-0">
        <div className="flex items-center">
          <SquareCheckBig className="mr-2" />
          <h2 className="text-tiny uppercase">TASKS</h2>
        </div>
        <Link showAnchorIcon anchorIcon={<ArrowRight />} href="/user/projects">
          More
        </Link>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="grid gap-2 pt-4">
          <div className="flex justify-between border-b-2 pb-2 mb-3">
            <div>
              <Checkbox>
                <div className="flex space-x-2">
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      HIGH
                    </Chip>
                  </div>
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      IN PROGRESS
                    </Chip>
                  </div>
                  <div> Upload supporting documents for Project A</div>
                </div>
              </Checkbox>
            </div>
            <div>Jan 30, 2025</div>
          </div>
          <div className="flex justify-between border-b-2 pb-2 mb-3">
            <div>
              <Checkbox>
                <div className="flex space-x-2">
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      HIGH
                    </Chip>
                  </div>
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      IN PROGRESS
                    </Chip>
                  </div>
                  <div> Upload supporting documents for Project A</div>
                </div>
              </Checkbox>
            </div>
            <div>Jan 30, 2025</div>
          </div>
          <div className="flex justify-between ">
            <div>
              <Checkbox>
                <div className="flex space-x-2">
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      HIGH
                    </Chip>
                  </div>
                  <div>
                    <Chip size="sm" radius={"sm"}>
                      IN PROGRESS
                    </Chip>
                  </div>
                  <div> Upload supporting documents for Project A</div>
                </div>
              </Checkbox>
            </div>
            <div>Jan 30, 2025</div>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}

export default TaskCard
