import { But<PERSON> } from "@heroui/react"
import { Form, useNavigate } from "@remix-run/react"
import { ERole } from "~/enums/EUserRole"
import { EStatus } from "~/enums/EStatus"
import PDDDetail from "./PddDetail"

const PDD = ({
  pdd,
  user,
  cursor_pointer = false
}: {
  pdd: any
  user: any
  cursor_pointer?: boolean
}) => {
  const navigate = useNavigate()
  return (
    <div
      onClick={() => {
        user?.role === ERole.ProjectDeveloper
          ? navigate(`/pdds/${pdd?.id}`)
          : navigate(`/admin/pdd-reports/${pdd?.id}`)
      }}>
      <PDDDetail pdd={pdd} cursor_pointer={cursor_pointer} />
      {user?.role === ERole.ProjectDeveloper && (
        <div>
          {pdd?.status === EStatus.Draft && (
            <div className="flex justify-end space-x-2">
              <Button
                color="primary"
                onPress={() => {
                  navigate(pdd?.step)
                }}>
                Edit
              </Button>
              <Form method="POST">
                <input type="hidden" name="action" value="submit-pdd" />
                <input type="hidden" name="pdd_id" value={pdd?.id} />
                <Button className="bg-primary" type="submit">
                  <p className="text-white">Submit</p>
                </Button>
              </Form>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default PDD
