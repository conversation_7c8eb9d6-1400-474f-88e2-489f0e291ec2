import { Card } from "@heroui/react"
import ImplementationItem from "../implementation/implementationItem"

const PDDDetail = ({
  pdd,
  cursor_pointer = false
}: {
  pdd: any
  cursor_pointer?: boolean
}) => {
  const pddReportDetail = [
    { name: "Address", value: pdd?.address },
    { name: "Methodology Name", value: pdd?.methodology_name },
    {
      name: "Baseline Scenario",
      value: pdd?.baseline_scenario
    },
    {
      name: "Project Scenario",
      value: pdd?.project_scenario
    },
    { name: "Staus", value: pdd?.status }
  ]

  return (
    <Card className={`p-4 my-4 ${cursor_pointer ? "cursor-pointer" : ""}`}>
      <div className="space-y-2">
        <div>
          <p className="text-black text-[18px] font-bold">PDD Report Detail</p>
        </div>

        {pddReportDetail.map((item, index) => {
          return (
            <div key={index}>
              <ImplementationItem label={item?.name} value={item?.value} />
            </div>
          )
        })}
      </div>
    </Card>
  )
}

export default PDDDetail
