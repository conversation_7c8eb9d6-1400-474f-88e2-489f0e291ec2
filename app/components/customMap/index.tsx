import { useState } from "react"
import { Map, Marker } from "@vis.gl/react-google-maps"

const CustomMap = ({
  lat,
  lng
}: {
  lat: number | string | null
  lng: number | string | null
}) => {
  const [markerLocation, setMarkerLocation] = useState({
    lat: Number(lat),
    lng: Number(lng)
  })

  return (
    <div className="h-full w-full">
      <Map
        style={{ borderRadius: "20px" }}
        defaultZoom={12}
        defaultCenter={markerLocation}
        gestureHandling={"greedy"}
        disableDefaultUI>
        <Marker position={markerLocation} />
      </Map>
    </div>
  )
}

export default CustomMap
