import { useState } from "react"
import { Accordion, AccordionItem } from "@heroui/react"
import { MinusIcon, PlusIcon } from "lucide-react"

const KeySectors = () => {
  const keySectors = [
    {
      title: "Energy",
      content:
        "The Energy sector focuses on transitioning to renewable energy, energy efficiency, and clean technologies to cut greenhouse gas (GHG) emissions. Projects in this sector include solar, wind, hydro, and energy-saving initiatives.",
      image: "key-sector-engery.webp",
      value: "1"
    },
    {
      title: "Industrial Processing and Product Use (IPPU)",
      content:
        "IPPU projects aim to reduce emissions from industrial activities, including cement production, chemical manufacturing, and refrigerant use. These projects enhance efficiency and promote low-carbon industrial practices.",
      image: "key-sector-ippu.webp",
      value: "2"
    },
    {
      title: "Agriculture",
      content:
        "The Agriculture sector focuses on reducing methane and nitrous oxide emissions from farming. This includes climate-smart agricultural practices, organic farming, and improved livestock management to enhance sustainability.",
      image: "key-sector-agriculture.webp",
      value: "3"
    },
    {
      title: "Forestry and Other Land Use (FOLU)",
      content: "Protecting and Restoring Ecosystems.",
      image: "key-sector-folu.webp",
      value: "4"
    },
    {
      title: "Waste Management",
      content: "Reducing Waste & Methane Emissions.",
      image: "key-sector-waste.webp",
      value: "5"
    }
  ]

  const [activeKey, setActiveKey] = useState("0")

  const handleAccordionChange = (key: string) => {
    setActiveKey(prevKey => (prevKey === key ? prevKey : key))
  }

  const [activeImage, setActiveImage] = useState(keySectors[0].image)

  return (
    <div className="py-5 md:py-10 px-5 xl:px-0 container mx-auto flex flex-col justify-center">
      <div className="space-y-1 md:space-y-14">
        <div className="space-y-1 p-4 sm:p-0">
          <p className="text-2xl md:text-3xl">EXPLORE</p>
          <p className="text-4xl md:text-5xl font-bold text-primary">
            Key Sectors
          </p>
          <p className="text-2xl md:text-3xl">for Climate Impact</p>
        </div>
        <div className="grid md:flex flex-row-reverse gap-0 md:gap-6  ">
          <div className="flex-[3] p-4 sm:p-0">
            <Accordion
              dividerProps={{ hidden: true }}
              selectedKeys={[activeKey]}>
              {keySectors.map((item, index) => {
                const key = String(index)
                const isActive = activeKey === key
                return (
                  <AccordionItem
                    key={index}
                    value={index}
                    title={item?.title}
                    classNames={{
                      indicator: "data-[open=true]:-rotate-0 ",
                      title: `text-xl data-[open=true]:font-bold ${
                        isActive ? "cursor-default" : "cursor-pointer"
                      }`,
                      content: `py-0 pb-4`
                    }}
                    indicator={({ isOpen }) =>
                      isOpen ? <MinusIcon /> : <PlusIcon />
                    }
                    onPress={() => {
                      handleAccordionChange(index.toString())
                      setActiveImage(item.image) // Update image
                    }}>
                    <div className="flex flex-col gap-4">
                      <p>{item?.content}</p>
                      <a
                        href={`/projects?page=1&limit=12&sector=${item?.value}`}
                        className="text-primary">
                        View Projects
                      </a>
                    </div>
                  </AccordionItem>
                )
              })}
            </Accordion>
          </div>
          <div className="flex-1 md:flex-[2] p-4 md:p-0">
            <img
              className="w-full h-[500px] rounded-2xl object-cover transition-all duration-500"
              src={activeImage}
              alt="Key Sector"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default KeySectors
