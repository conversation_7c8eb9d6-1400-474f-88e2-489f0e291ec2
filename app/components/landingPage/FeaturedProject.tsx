import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { ArrowRight } from "lucide-react"

const FeaturedProject = () => {
  return (
    <div className="pt-0 pb-5 md:pb-14 container px-5 xl:px-0 mx-auto flex flex-col justify-center">
      <div className="space-y-1 md:space-y-14">
        <div className="space-y-1 p-4 sm:p-0 grid grid-cols-1 md:grid-cols-2">
          <div>
            <p className="text-2xl md:text-2xl lg:text-3xl">EXPLORE</p>
            <p className="text-3xl md:text-3xl lg:text-4xl font-bold text-primary">
              Featured Project
            </p>
            <p className="text-xl md:text-2xl lg:text-3xl mt-4">
              Discover Verified Projects Making a Difference
            </p>
          </div>
          <div className="flex justify-end items-center">
            <Button color="primary" variant="light" endContent={<ArrowRight />}>
              <p className="text-[18px] md:text-[20px] lg:text-[22px]">More</p>
            </Button>
          </div>
        </div>
        <div className="h-[550px] relative">
          <img
            className="w-full h-full object-cover"
            src="/explor-project.png"></img>
          <div
            className="size-full absolute top-0 left-0 text-white"
            style={{
              background:
                "linear-gradient(0deg, rgba(2,0,36,0.7595413165266106) 0%, rgba(254,253,253,0.12648809523809523) 100%)"
            }}>
            <div className="h-full container mx-auto">
              <div className="p-3 md:p-6 h-full w-full sm:3/4 md:2/3 lg:w-1/2 items-end flex">
                <div className="gird grid-cols-1 space-y-2">
                  <p className="text-[22px] md:text-2xl lg:text-3xl">
                    Community Forest Protection Project
                  </p>
                  <p className="text-[18px] md:text-[22px] lg:text-[24px]">
                    <b>Sector:</b> Forestry and Other Land Use (FOLU)
                    <br />
                    <b>Impact:</b> Prevented 1.2M tons of CO₂ emissions and
                    supported 300+ community jobs.
                  </p>
                  <Link className="text-white p-0 cursor-pointer">
                    <p className="text-[16px] flex mt-5">
                      View Detail <ArrowRight className="ml-3" />
                    </p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FeaturedProject
