import { Button } from "@heroui/react"
import { useNavigate } from "@remix-run/react"

const HeroSection = () => {
  const navigate = useNavigate()
  return (
    <div className="h-[calc(100vh-5rem)] w-full relative">
      <img className="w-full h-full object-cover" src="/hero.webp"></img>
      <div className="size-full bg-white/15 absolute top-0 left-0 text-white">
        <div className="h-full container mx-auto p-5 xl:p-0">
          <div className="h-full w-full sm:3/4 md:2/3 lg:w-1/2 flex flex-col justify-center">
            <div className="space-y-6 ">
              <h1 className="text-[26px] sm:text-3xl lg:text-5xl w-4/5 font-bold">
                Transforming Climate Goals into Reality
              </h1>
              <p className="text-[16px] sm:text-[18px] lg:text-xl">
                The Cambodia Carbon Registry is your gateway to transparency,
                trust, and global recognition for carbon projects. Explore
                verified projects, track carbon credits, and join us in
                advancing sustainable development for Cambodia's future.
              </p>
              <div className="flex items-center gap-10 text-md">
                <Button
                  onPress={() => {
                    navigate("/user")
                  }}
                  color="primary"
                  size="lg"
                  className="w-40 font-semibold ">
                  Get Started
                </Button>
                <Button
                  onPress={() => {
                    navigate("/#how-it-words")
                  }}
                  size="lg"
                  className="w-40 font-semibold bg-transparent text-white">
                  Learn How It Works
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HeroSection
