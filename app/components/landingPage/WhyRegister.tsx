import {
  CircleDollarSign,
  Eye,
  Globe,
  LifeBuoy,
  LucideIcon
} from "lucide-react"

const WhyRegister = () => {
  const reasons = [
    {
      Icon: Globe,
      title: "Global Recognition",
      description:
        "Gain visibility and credibility through alignment with global standards like ART-TREES, Verra, and CDM."
    },
    {
      Icon: CircleDollarSign,
      title: "Access to Climate Finance",
      description:
        "Attract climate finance opportunities from donors and investors."
    },
    {
      Icon: Eye,
      title: "Transparency & Accountability",
      description:
        "Showcase project impact through transparent monitoring and reporting."
    },
    {
      Icon: LifeBuoy,
      title: "Technical Support",
      description:
        "eceive expert guidance and support from the Ministry of Environment, UNDP, and development partners."
    }
  ]
  return (
    <div className="px-4 md:px-10 pt-5 md:pt-16">
      <div className="space-y-5 p-4 sm:p-0">
        <div className="space-y-1 justify-center items-end ">
          <p className="text-1xl md:text-2xl text-center">WHY</p>
          <p className="text-3xl md:text-4xl font-bold text-primary text-center px-0 md:px-0 lg:px-[300px] xl:px-[550px]">
            Why Register Your Carbon Project With Us?
          </p>
        </div>
        <div>
          <p className="text-center text-[20px] px-0 lg:px-80">
            Take part in Cambodia’s journey toward sustainable development and
            international climate impact. By joining the Cambodia Carbon
            Registry Registry, you unlock a range of benefits
          </p>
        </div>
      </div>
      <div className="container mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 my-12">
        {reasons.map(
          (
            item: { Icon: LucideIcon; title: string; description: string },
            index: number
          ) => {
            return (
              <div
                className="bg-white shadow-md border rounded-lg p-4 grid grid-cols-1 h-96 "
                key={index}>
                <div className="justify-center flex items-center">
                  <item.Icon className="size-20" />
                </div>
                <div className="justify-center text-center px-10 ">
                  <p className="font-bold text-[18px] my-4">{item.title}</p>
                  <p>{item?.description}</p>
                </div>
              </div>
            )
          }
        )}
      </div>
    </div>
  )
}

export default WhyRegister
