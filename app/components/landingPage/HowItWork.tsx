import { Button } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import {
  ArrowRight,
  HandCoins,
  ListChecks,
  LucideIcon,
  MessageSquareText,
  UserPlus
} from "lucide-react"

const HowItWork = () => {
  const howItworkProcess = [
    {
      Icon: UserPlus,
      title: "Register & Submit Your Project",
      description:
        "Sign up and submit project details with supporting documents."
    },
    {
      Icon: MessageSquareText,
      title: "Review & Approval",
      description:
        "Our reviewers assess project eligibility and provide feedback."
    },
    {
      Icon: ListChecks,
      title: "Verification & Listing",
      description:
        "Projects that meet the criteria are verified and listed in the public registry."
    },
    {
      Icon: HandCoins,
      title: "Track Your Carbon Credits",
      description: "Manage credit issuance, transfer, and retirement."
    }
  ]

  const navigate = useNavigate()

  return (
    <div className="bg-primary-light p-5 md:p-10" id="how-it-words">
      <div className="space-y-5 p-4 sm:p-0">
        <div className="space-y-1">
          <p className="text-1xl md:text-2xl text-center">LEARN</p>
          <p className="text-3xl md:text-4xl font-bold text-primary text-center">
            HOW IT WORKS
          </p>
        </div>
        <div>
          <p className="text-center text-[20px]">
            How Does the Cambodia Carbon Registry Work?
          </p>
        </div>
      </div>
      <div className="container mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 my-12">
        {howItworkProcess.map(
          (
            item: { Icon: LucideIcon; title: string; description: string },
            index: number
          ) => {
            return (
              <div
                className="bg-white shadow-md border rounded-lg p-4 grid grid-cols-1 h-96 "
                key={index}>
                <div className="justify-center flex items-center">
                  <item.Icon className="size-20" />
                </div>
                <div className="justify-center text-center px-10 ">
                  <p className="font-bold text-[18px] my-4">{item?.title}</p>
                  <p>{item?.description}</p>
                </div>
              </div>
            )
          }
        )}
      </div>
      <div className="flex justify-center">
        <Button
          color="primary"
          variant="bordered"
          endContent={<ArrowRight />}
          onPress={() => {
            navigate("/how-it-works")
          }}>
          Learn more
        </Button>
      </div>
    </div>
  )
}

export default HowItWork
