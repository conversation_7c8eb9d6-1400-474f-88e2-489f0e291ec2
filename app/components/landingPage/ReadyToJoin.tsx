import { Button } from "@heroui/react"
import { useNavigate } from "@remix-run/react"

const ReadyToJoin = () => {
  const navigate = useNavigate()
  return (
    <div>
      <div className="justify-center items-center py-7 md:py-8 ">
        <div className="w-full bg-[#F4F4F5] relative border-b-1 border-t-1 border-gray-300">
          <div className="relative w-full min-h-[300px]">
            <img
              className="absolute inset-0 w-full h-full object-cover"
              src="/ready-to-join.webp"
              alt="FAQ Background"
            />

            <div className="relative w-full p-4 sm:p-6 md:p-8 lg:p-12 space-y-6">
              <div>
                <p className="text-center readyToJoinTitle">
                  Ready to Get Started?
                </p>
              </div>
              <div className="flex justify-center">
                <p className="text-center flex justify-center description max-w-4xl sm:max-w-3xl md:max-w-2xl  lg:max-w-xl">
                  Join a trusted system that ensures your carbon project is
                  recognized, verified, and impactful.
                </p>
              </div>
              <div className="grid grid-cols-1 sm:flex sm:justify-center sm:space-x-4 gap-2 sm:gap-0">
                <Button
                  onPress={() => {
                    navigate("/user")
                  }}
                  variant="bordered"
                  className="bg-primary text-white p-2 md:p-7 text-[16px] md:text-[20px] border-primary">
                  Submit Your Project
                </Button>
                <Button
                  onPress={() => {
                    navigate("/projects")
                  }}
                  variant="bordered"
                  className="text-primary p-2 md:p-7 text-[16px] md:text-[20px] border-primary">
                  Explore Verified Projects
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReadyToJoin
