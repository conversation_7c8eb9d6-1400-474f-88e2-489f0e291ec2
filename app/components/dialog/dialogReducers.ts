import type { ReactElement, ReactNode } from "react"
import { IDialogProps } from "~/components/ui/Dialog"
import type { OpenDialogOptions } from "./DialogContext"

export interface DialogState {
  /**
   * Title of the dialog.
   */
  title?: ReactNode;
  titleIcon?: ReactNode;
  subTitle?: ReactNode;
  message?: ReactNode;
  /**
   * Tracks if a dialog is open.
   */
  open: boolean;
  /**
   * Component to render inside the dialog skeleton.
   */
  activeDialog?: ReactElement;
  /**
   * Props passed to the currently active dialog.
   */
  dialogProps?: Partial<IDialogProps>;

  submitOption: {
    formData?: any,
    options: any
  };
  /**
   * Custom payload to be passed to the currently active dialog. This is the
   * data needed by the active dialog.
   */
  payload?: any;
}

export type DialogAction =
  | { type: "OPEN_DIALOG"; payload: OpenDialogOptions }
  | { type: "HIDE_DIALOG" };

/**
 * Reducer for the main dialog.
 *
 * @param state - Current state of the main dialog.
 * @param action - Action to be performed on the main dialog.
 * @returns New state of the main dialog.
 */
export function dialogReducer(
  state: DialogState[],
  action: DialogAction
): DialogState[] {
  switch (action.type) {
    case "OPEN_DIALOG":
      return [
        ...state,
        {
          open: true,
          title: action.payload.title,
          titleIcon: action.payload.titleIcon,
          subTitle: action.payload.subTitle,
          activeDialog: action.payload.component,
          dialogProps: action.payload.props,
          submitOption: action.payload.submitOption
        }
      ]
    case "HIDE_DIALOG":
      return [...state.slice(0, -1)]
    default:
      return state
  }
}


export type AlertDialogAction =
  | { type: "OPEN_ALERT"; payload: OpenDialogOptions }
  | { type: "HIDE_ALERT" };

/**
 * Reducer for the alert dialog.
 *
 * @param state - Current state of the alert dialog.
 * @param action - Action to be performed on the alert dialog.
 * @returns New state of the alert dialog.
 */
export function alertDialogReducer(
  state: Pick<DialogState, "open" | "title" | "titleIcon" | "message" | "dialogProps" | "submitOption">,
  action: AlertDialogAction
): DialogState {
  switch (action.type) {
    case "OPEN_ALERT":
      return {
        ...state,
        open: true,
        title: action.payload.title,
        titleIcon: action.payload.titleIcon,
        message: action.payload.message,
        dialogProps: action.payload.props,
        submitOption: action.payload.submitOption
      }
    case "HIDE_ALERT":
      return { ...state, open: false }
    default:
      return state
  }
}
