import { PropsWithChildren, use<PERSON><PERSON>back, useMemo, useReducer } from "react"
import AlertDialog from "~/components/ui/AlertDialog"
import Dialog from "~/components/ui/Dialog"
import type { OpenDialogOptions } from "./DialogContext"
import DialogContext from "./DialogContext"
import { alertDialogReducer, dialogReducer } from "./dialogReducers"

function DialogProvider({ children }: PropsWithChildren<unknown>) {
  const [dialogs, dialogDispatch] = useReducer(dialogReducer, [])

  const [
    {
      open: alertDialogOpen,
      dialogProps: alertDialogProps,
      title: alertDialogTitle,
      titleIcon: alertDialogTitleIcon,
      message: alertDialogMessage,
      submitOption: alertDialogSubmitOption
    },
    alertDialogDispatch
  ] = useReducer(alertDialogReducer, {
    open: false
  })

  const openDialog = useCallback((options: OpenDialogOptions) => {
    dialogDispatch({ type: "OPEN_DIALOG", payload: options })
  }, [])

  const closeDialog = useCallback(() => {
    dialogDispatch({ type: "HIDE_DIALOG" })
  }, [])

  const openAlertDialog = useCallback((options: OpenDialogOptions) => {
    alertDialogDispatch({ type: "OPEN_ALERT", payload: options })
  }, [])

  const closeAlertDialog = useCallback(() => {
    alertDialogDispatch({ type: "HIDE_ALERT" })
  }, [])

  const contextValue = useMemo(
    () => ({
      openDialog,
      closeDialog,
      openAlertDialog,
      closeAlertDialog
    }),
    [
      closeDialog,
      openDialog,
      openAlertDialog,
      closeAlertDialog
    ]
  )

  return (
    <DialogContext.Provider value={contextValue}>
      {dialogs.map((d, index) => (
        <Dialog
          key={index}
          {...d.dialogProps}
          title={d.title}
          titleIcon={d.titleIcon}
          subTitle={d.subTitle}
          open={d.open}
          onSecondaryAction={closeDialog}
          submitOption={d.submitOption}
        >
          {d.activeDialog}
        </Dialog>
      ))}

      <AlertDialog
        {...alertDialogProps}
        title={alertDialogTitle}
        titleIcon={alertDialogTitleIcon}
        message={alertDialogMessage}
        open={alertDialogOpen}
        onSecondaryAction={closeAlertDialog}
        submitOption={alertDialogSubmitOption}
      />

      {children}
    </DialogContext.Provider>
  )
}

export default DialogProvider
