import type { ReactElement, ReactNode } from "react"
import { createContext } from "react"
import { IDialogProps } from "~/components/ui/Dialog"

export interface OpenDialogOptions {
  /**
   * Title of the dialog.
   */
  title?: ReactNode
  titleIcon?: ReactNode
  subTitle?: ReactNode
  message?: ReactNode
  /**
   * Component to render inside the dialog skeleton.
   */
  component?: ReactElement

  submitOption: {
    formData?: any
    options: any
    fetcherKey?: string
  }
  /**
   * Props to pass to the root dialog component.
   */
  props?: Partial<IDialogProps>
}

interface DialogContextProps {
  /**
   * Call this function to open a dialog. It will automatically apply the
   * necessary functionality to the dialog.
   */
  openDialog: (options: OpenDialogOptions) => void
  /**
   * Call this function to close the active dialog.
   */
  closeDialog: VoidFunction
  /**
   * Call this function to open an alert dialog.
   */
  openAlertDialog: (options: OpenDialogOptions) => void
  /**
   * Call this function to close the active alert dialog.
   */
  closeAlertDialog: VoidFunction
}

export default createContext<DialogContextProps>({
  openDialog: () => {},
  closeDialog: () => {},
  openAlertDialog: () => {},
  closeAlertDialog: () => {}
})
