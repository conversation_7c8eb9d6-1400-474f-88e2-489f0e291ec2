import { Card, CardBody } from "@heroui/react"
import dayjs from "dayjs"
import { FileDown, FileText } from "lucide-react"
import { getImage } from "~/helpers/r2"

const DocumentTab = ({
  implementationList,
  project,
  pdd
}: {
  implementationList?: any
  project?: any
  pdd?: any
}) => {
  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2">
        <FileText />
        <p className="font-bold text-[18px]">Project Document</p>
      </div>
      {project?.review_project_document && (
        <Card>
          <CardBody>
            <div className="space-y-3">
              {implementationList?.length > 0 &&
                implementationList?.map((item: any, index: number) => {
                  if (item?.review_implementation_document) {
                    return (
                      <div key={index}>
                        <div className="flex items-center">
                          <div className="min-w-[350px]">
                            <p className="text-[17px]">
                              Implementation Document
                            </p>
                          </div>
                          <div>
                            <a
                              href={getImage(
                                item?.review_implementation_document
                              )}
                              target="_blank"
                              download>
                              <FileDown className="w-5 h-5" />
                            </a>
                          </div>
                        </div>
                        <div>
                          <p className="text-gray-500 text-[13px]">
                            {dayjs(new Date(item?.updated_at)).format(
                              "DD MMMM YYYY @ hh:mm"
                            )}
                          </p>
                        </div>
                      </div>
                    )
                  }
                })}

              {pdd[0]?.review_pdd_document && (
                <div>
                  <div className="flex items-center">
                    <div className="min-w-[350px]">
                      <p className="text-[17px]">
                        Signed Letter Authorization Document
                      </p>
                    </div>
                    <div>
                      <a
                        href={getImage(pdd[0]?.review_pdd_document)}
                        target="_blank"
                        download>
                        <FileDown className="w-5 h-5" />
                      </a>
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-500 text-[13px]">
                      {dayjs(new Date(project?.updated_at)).format(
                        "DD MMMM YYYY @ hh:mm"
                      )}
                    </p>
                  </div>
                </div>
              )}

              <div>
                <div className="flex items-center">
                  <div className="min-w-[350px]">
                    <p className="text-[17px]">
                      Signed Letter No Objection Document
                    </p>
                  </div>
                  <div>
                    <a
                      href={getImage(project?.review_project_document)}
                      target="_blank"
                      download>
                      <FileDown className="w-5 h-5" />
                    </a>
                  </div>
                </div>
                <div>
                  <p className="text-gray-500 text-[13px]">
                    {dayjs(new Date(project?.updated_at)).format(
                      "DD MMMM YYYY @ hh:mm"
                    )}
                  </p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  )
}

export default DocumentTab
