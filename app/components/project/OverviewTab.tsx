import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  CardHeader,
  Checkbox,
  Link
} from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { APIProvider, Map, useMap } from "@vis.gl/react-google-maps"
import { ArrowRight, Earth, MapPin, SquareCheckBig } from "lucide-react"
import { useEffect } from "react"
import { getImage } from "~/helpers/r2"
import { useDialog } from "../dialog"

const OverviewTab = ({
  project,
  isSubmit,
  isEdit,
  isDelete
}: {
  project: any
  isSubmit?: boolean
  isEdit?: boolean
  isDelete?: boolean
}) => {
  const steps = [
    { label: "Submitted", status: "completed" },
    { label: "Reviewed", status: "completed" },
    { label: "Registered", status: "current" },
    { label: "Verified", status: "upcoming" },
    { label: "Credits Issued", status: "upcoming" },
    { label: "Completed", status: "upcoming" }
  ]
  const navigate = useNavigate()
  const { openAlertDialog } = useDialog()

  const currentStepIndex = steps.findIndex(step => step.status === "current")
  const progressPercent = (currentStepIndex / (steps.length - 1)) * 100

  const handleDelete = () => {
    openAlertDialog({
      title: "Delete Project",
      message: "Are you sure you want to delete this project?",
      submitOption: {
        formData: { action: "delete" },
        options: {
          method: "post",
          action: `/user/projects/${project?.id}`
        }
      }
    })
  }

  const handleSubmit = () => {
    openAlertDialog({
      title: "Submit Project",
      message: "Are you sure you want to Submit this project?",
      submitOption: {
        formData: { action: "submit" },
        options: {
          method: "post",
          action: `/user/projects/${project?.id}`
        }
      }
    })
  }

  return (
    <div className="space-y-6">
      {project?.reject_reason && (
        <Card className="p-2 rounded-lg bg-danger">
          <div className="text-center text-white font-bold">
            {project.reject_reason}
          </div>
        </Card>
      )}
      <Card className="p-4 rounded-lg ">
        <CardBody className="space-y-2">
          <div>PROGRESS</div>
          <div className="w-full flex flex-col items-center">
            <div className="w-full flex justify-between my-2">
              {steps.map((step, index) => (
                <span
                  key={index}
                  className={`text-md ${
                    step.status === "current"
                      ? "font-bold text-green-700"
                      : "text-gray-500"
                  }`}>
                  {step.label}
                </span>
              ))}
            </div>
            <div className="relative w-full h-3 bg-gray-200 rounded-full ">
              <div
                className="absolute h-3 bg-green-700 rounded-full transition-all"
                style={{ width: `${progressPercent}%` }}
              />
              {/* Step Circles */}
              <div className="absolute top-[-5px] w-full flex justify-between">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      step.status === "completed"
                        ? "bg-green-700 border-green-700"
                        : step.status === "current"
                          ? "bg-yellow-400 border-green-700"
                          : "bg-white border-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      <Card className="p-4 col-span-2 rounded-lg">
        <CardHeader className="flex justify-between pb-0">
          <div className="flex items-center">
            <SquareCheckBig className="mr-2" />
            <h2 className="text-tiny uppercase">PENDING TASKS</h2>
          </div>
          <Link
            showAnchorIcon
            anchorIcon={<ArrowRight />}
            href="/user/projects">
            More
          </Link>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="grid gap-2 pt-4">
            <div className="flex justify-between border-b-2 pb-2 mb-3">
              <div>
                <Checkbox>
                  <div>Upload supporting documents for Project A</div>
                </Checkbox>
              </div>
              <div>Jan 30, 2025</div>
            </div>
            <div className="flex justify-between border-b-2 pb-2 mb-3">
              <div>
                <Checkbox>
                  <div>Upload supporting documents for Project A</div>
                </Checkbox>
              </div>
              <div>Jan 30, 2025</div>
            </div>
            <div className="flex justify-between ">
              <div>
                <Checkbox>
                  <div>Upload supporting documents for Project A</div>
                </Checkbox>
              </div>
              <div>Jan 30, 2025</div>
            </div>
          </div>
        </CardBody>
      </Card>

      <Card className="p-4 rounded-lg">
        <CardHeader className="flex justify-between pb-0">
          <div className="flex items-center">
            <Earth className="mr-2" />
            <h2 className="text-tiny uppercase">METRICS</h2>
          </div>
          <div>All time</div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-4 gap-6">
            <div>
              <Card className="rounded-md bg-base-200 shadow-none p-5">
                <div>EMISSION REDUCTED</div>
                <div className="flex my-4 space-x-3">
                  <div className=" font-bold text-6xl">1.2M</div>
                  <div className="flex items-end">TONS</div>
                </div>
              </Card>
            </div>
            <div>
              <Card className="rounded-md bg-base-200 shadow-none p-5 h-full">
                <div>EMISSION REDUCTED</div>

                <div className=" my-4 font-bold text-6xl">1.25M</div>
              </Card>
            </div>
            <div>
              <Card className="rounded-md bg-base-200 shadow-none p-5 h-full">
                <div>CREDITS RETIRED</div>
                <div className=" my-4 font-bold text-6xl">950K</div>
              </Card>
            </div>
            <div>
              <Card className="rounded-md bg-base-200 shadow-none p-5 h-full">
                <div>BUFFER AVAILABLE</div>
                <div className=" my-4 font-bold text-6xl">100k</div>
              </Card>
            </div>
          </div>
        </CardBody>
      </Card>

      <Card className="p-4 rounded-lg">
        <CardHeader className="flex justify-between pb-0">
          <div className="flex items-center">
            <MapPin className="mr-2" />
            <h2 className="text-tiny uppercase">LOCATION</h2>
          </div>
          <div>{project?.province}</div>
        </CardHeader>
        <CardBody>
          <div className="pt-4 w-full h-96">
            <APIProvider apiKey={"AIzaSyCulMnvWBSpzXP0A851RACOVDVXGc5zDic"}>
              <Map
                restriction={{
                  latLngBounds: {
                    north: 85,
                    south: -85,
                    west: -180,
                    east: 180
                  }
                }}
                minZoom={3}
                defaultZoom={8}>
                <KmlLayerComponent location={project?.kml_file} />
              </Map>
            </APIProvider>
          </div>
        </CardBody>
      </Card>

      <div className="w-full flex justify-end space-x-2">
        {isSubmit && (
          <Button className="bg-primary" onPress={handleSubmit}>
            <p className="text-white">Submit</p>
          </Button>
        )}
        {isEdit && (
          <Button
            className="bg-primary"
            onPress={() =>
              navigate(`/user/projects/${project?.id}/${project?.project_step}`)
            }>
            <p className="text-white">Edit</p>
          </Button>
        )}
        {isDelete && (
          <Button className="bg-danger" onPress={handleDelete}>
            <p className="text-white">Delete</p>
          </Button>
        )}
      </div>
    </div>
  )
}

export default OverviewTab

const KmlLayerComponent = ({ location }: { location: string }) => {
  const map = useMap()
  useEffect(() => {
    if (!map) return

    const kmlLayer = new google.maps.KmlLayer({
      url: getImage(location),
      map: map
    })

    return () => kmlLayer.setMap(null)
  }, [map, location])

  return null
}
