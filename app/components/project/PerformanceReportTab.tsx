import { useNavigate } from "@remix-run/react"
import ImplementationDetail from "../implementation"
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { ERole } from "~/enums/EUserRole"

const PerformanceReportTab = ({
  isAddImplementation,
  project,
  implementationList,
  user
}: {
  isAddImplementation?: boolean
  project?: any
  implementationList?: any
  user?: any
}) => {
  const navigate = useNavigate()
  return (
    <>
      {isAddImplementation && (
        <div className="flex justify-end">
          <Button
            as={Link}
            href={`/${project?.id}/performance-report/new`}
            color="primary">
            Add Performance Reports
          </Button>
        </div>
      )}
      {implementationList?.map((item: any, index: number) => {
        return (
          <div
            key={index}
            onClick={() =>
              user?.role === ERole.ProjectDeveloper
                ? navigate(`/performance-report/${item?.id}`)
                : navigate(`/admin/performance-reports/${item?.id}`)
            }>
            <ImplementationDetail implementation={item} cursor_pointer={true} />
          </div>
        )
      })}
    </>
  )
}

export default PerformanceReportTab
