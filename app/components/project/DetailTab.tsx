import { <PERSON>, CardBody, CardHeader } from "@heroui/react"

const DetailTab = ({ project }: { project: any }) => {
  return (
    <div className="space-y-6">
      <Card className="p-4 rounded-lg">
        <CardHeader>
          <div className="flex justify-between w-full">
            <div>SUMMARY</div>
            <div>{project.ideaNote?.status}</div>
          </div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1">
            <Card className="rounded-md bg-base-200 shadow-none p-5 grid grid-cols-1 gap-2">
              <div className="flex">
                <div className="min-w-[35%] mr-2">ID</div>
                <div className="flex items-center">{project?.id}</div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Location</div>
                <div className="flex items-center">
                  {project?.province ? project?.province : "N/A"}
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%]  mr-2">Sector</div>
                <div className="flex items-center">
                  {project?.sector ? project?.sector?.name : "N/A"}
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%]  mr-2">Project Type</div>
                <div className="flex items-center">
                  {project?.type?.name ? project?.type?.name : "N/A"}
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Acres/Hectares</div>
                <div className="flex items-center">
                  {project?.total_project_area
                    ? project?.total_project_area
                    : "N/A"}
                </div>
              </div>

              <div className="flex">
                <div className="min-w-[35%] mr-2">Started Date</div>
                <div className="flex items-center">
                  {project?.start_date ? project?.start_date : "N/A"}
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Expected Completion</div>
                <div className="flex items-center">
                  {project?.end_date ? project?.end_date : "N/A"}
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Methodology</div>
                <div className="flex items-center">AR-ACM0003</div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Status</div>
                <div className="flex items-center">{project?.status}</div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Project Validator</div>
                <div className="flex items-center">
                  AENOR International S.A.U.
                </div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] max-w-[30%] mr-2">
                  Estimated Annual Emission Reductions
                </div>
                <div className="flex items-center">6357</div>
              </div>
              <div className="flex">
                <div className="min-w-[35%] mr-2">Crediting Period Term</div>
                <div className="flex items-center">
                  1st, 17/06/2016 - 16/06/2046
                </div>
              </div>
            </Card>
          </div>
        </CardBody>
      </Card>

      <Card className="p-4 rounded-lg">
        <CardHeader className="flex justify-between pb-0">
          <div>DESCRIPTION</div>
        </CardHeader>
        <CardBody>
          <div className="pt-4 ">
            This project focuses on conserving Prey Lang Forest, a critical
            biodiversity hotspot, while integrating sustainable agroforestry
            practices to support local communities. The project aims to reduce
            deforestation and enhance carbon sequestration through
            afforestation, reforestation, and agroforestry practices. Local
            farmers are provided with training, resources, and incentives to
            cultivate high-value crops alongside reforestation initiatives.
          </div>
        </CardBody>
      </Card>
    </div>
  )
}

export default DetailTab
