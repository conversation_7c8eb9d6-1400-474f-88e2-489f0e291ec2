import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>B<PERSON>, <PERSON>, Tab, Tabs } from "@heroui/react"
import { useFetcher } from "@remix-run/react"
import dayjs from "dayjs"
import React, { useState } from "react"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EStatus } from "~/enums/EStatus"
import { EModules, ERole } from "~/enums/EUserRole"
import { getImage } from "~/helpers/r2"
import { hasPermissions } from "~/utils/permission"
import PDD from "../pdd"
import Input from "../ui/input"
import DetailTab from "./DetailTab"
import DocumentTab from "./DocumentTab"
import OverviewTab from "./OverviewTab"
import PerformanceReportTab from "./PerformanceReportTab"
import ReviewProjectTab from "./ReviewProjectTab"

const ProjectTab = ({
  user,
  pdd,
  assingList,
  project,
  assignInternal,
  isAddImplementation,
  assingExternal,
  implementationList,
  internalReviewerList,
  externalAuditorList,
  coordinatorList,
  isApproveReject,
  isReview,
  isFeedback,
  commentList,
  isSubmit,
  isEdit,
  isDelete
}: {
  user?: any
  pdd?: any
  assingList?: any
  project?: any
  assignInternal?: any
  isAddImplementation?: any
  assingExternal?: any
  implementationList?: any
  internalReviewerList?: any
  externalAuditorList?: any
  coordinatorList?: any
  isApproveReject?: any
  isReview?: any
  isFeedback?: any
  commentList?: any
  isSubmit?: any
  isEdit?: any
  isDelete?: any
}) => {
  const assignReviewProjectByUser = assingList?.filter(
    (item: any) => item?.user_id === user?.id
  )

  const fetcher = useFetcher()

  const [comment, setComment] = useState("")

  React.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success) {
      setComment("") // Clear input only on success
    }
  }, [fetcher.state, fetcher.data])

  return (
    <Tabs
      aria-label="Tabs variants"
      classNames={{
        tab: "",
        tabContent: "group-data-[selected=true]:font-bold text-lg"
      }}
      color="primary"
      variant="underlined">
      <Tab key="overview" title="Overview">
        <OverviewTab
          project={project}
          isSubmit={isSubmit}
          isEdit={isEdit}
          isDelete={isDelete}
        />
      </Tab>
      <Tab key="detail" title="Details">
        <DetailTab project={project} />
      </Tab>
      {project?.ideaNote?.status === EStatus.Approve && (
        <Tab key="pdd" title="PDD">
          {pdd?.length > 0 ? (
            <PDD pdd={pdd[0]} user={user} cursor_pointer={true} />
          ) : (
            <>
              {user?.role === ERole.ProjectDeveloper && (
                <div className="flex justify-end">
                  <Button
                    as={Link}
                    href={`/user/projects/${project?.id}/pdds/new/project-information/1`}
                    color="primary">
                    Add PDD
                  </Button>
                </div>
              )}
            </>
          )}
        </Tab>
      )}
      {project?.status === EProjectStatus.SignedLOA && (
        <Tab key="performance-reports" title="Performance Reports">
          <PerformanceReportTab
            isAddImplementation={isAddImplementation}
            project={project}
            user={user}
            implementationList={implementationList}
          />
        </Tab>
      )}

      <Tab key="document" title="Documents">
        <DocumentTab
          project={project}
          implementationList={implementationList}
          pdd={pdd}
        />
      </Tab>

      <Tab key="comment" title="Comments">
        <div className="space-y-3">
          {commentList?.map((item: any, index: number) => {
            return (
              <div key={index}>
                <div className="flex gap-3 ">
                  <div>
                    <Avatar
                      size="md"
                      src={
                        item?.user?.photoUrl
                          ? getImage(item?.user?.photoUrl)
                          : "https://i.pravatar.cc/150?u=a042581f4e29026704d"
                      }
                    />
                  </div>
                  <Card className="flex-1">
                    <CardBody>
                      <p className="font-bold text-[18px]">
                        {item?.user?.firstName} {item?.user?.lastName}
                      </p>
                      <p>{item?.comment}</p>
                    </CardBody>
                  </Card>
                </div>
                <div className="flex justify-end  mt-2 pr-4">
                  <p>
                    {dayjs(new Date(item?.created_at)).format(
                      "DD/MM/YYYY HH:mm"
                    )}
                  </p>
                </div>
              </div>
            )
          })}
          <div>
            <fetcher.Form method="post" action="/comment">
              <div className="flex gap-3 ">
                <div>
                  <Avatar
                    size="md"
                    src={
                      user?.photoUrl
                        ? getImage(user?.photoUrl)
                        : "https://i.pravatar.cc/150?u=a042581f4e29026704d"
                    }
                  />
                </div>
                <Card className="flex-1">
                  <CardBody>
                    <Input
                      name="comment"
                      placeholder="Write your comment here"
                      value={comment}
                      onChange={e => setComment(e.target.value)}
                    />
                  </CardBody>
                </Card>
              </div>
              <input type="hidden" name="project_id" value={project?.id} />
              <input type="hidden" name="user_id" value={user?.id} />
              <div className="flex justify-end  mt-2">
                <Button type="submit" color="primary">
                  Add Comment
                </Button>
              </div>
            </fetcher.Form>
          </div>
        </div>
      </Tab>
      {hasPermissions(user?.role, [EModules.ReviewProject]) && (
        <>
          <Tab key="review" title="Review">
            <ReviewProjectTab
              project={project}
              user={user}
              assignReviewProjectByUser={assignReviewProjectByUser}
              assignInternal={assignInternal}
              assingExternal={assingExternal}
              isApproveReject={isApproveReject}
              isReview={isReview}
              isFeedback={isFeedback}
              internalReviewerList={internalReviewerList}
              externalAuditorList={externalAuditorList}
              coordinatorList={coordinatorList}
            />
          </Tab>
        </>
      )}
    </Tabs>
  )
}

export default ProjectTab
