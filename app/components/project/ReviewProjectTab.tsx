import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Select,
  SelectItem,
  SharedSelection
} from "@heroui/react"
import { Form } from "@remix-run/react"
import React from "react"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EModules, ERole } from "~/enums/EUserRole"
import { getImage } from "~/helpers/r2"
import { hasPermissions } from "~/utils/permission"
import AssignCard from "../assign/assignCard"
import DownloadButton from "../buttonDownload"
import { useDialog } from "../dialog"
import Input from "../ui/input"

const ReviewProjectTab = ({
  project,
  user,
  assignReviewProjectByUser,
  assignInternal,
  assingExternal,
  isApproveReject,
  isReview,
  isFeedback,
  internalReviewerList,
  externalAuditorList,
  coordinatorList
}: {
  project?: any
  user?: any
  assignReviewProjectByUser?: any
  assignInternal?: any
  assingExternal?: any
  isApproveReject?: any
  isReview?: any
  isFeedback?: any
  internalReviewerList?: any
  externalAuditorList?: any
  coordinatorList?: any
}) => {
  const { openDialog } = useDialog()
  const AssignComponent = ({
    isInternalReviewer
  }: {
    isInternalReviewer: any
  }) => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={
            isInternalReviewer ? internalReviewerList : externalAuditorList
          }
          label="Assigned to"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>
        <Input required type="file" name="support_document" />
        <input type="hidden" name="id" value={project?.id} />
        <input
          type="hidden"
          name="isInternalReviewer"
          value={isInternalReviewer}
        />
        <input type="hidden" name="action" value="assign" />
        {Array.from(values).map((value, index) => (
          <input name="assign" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }
  const RequestLNOComponent = () => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={coordinatorList}
          label="Request LNO from"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>

        <input type="hidden" name="id" value={project?.id} />
        <input type="hidden" name="action" value="request_lno" />
        {Array.from(values).map((value, index) => (
          <input name="request_from" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }
  const handleAssign = ({
    isInternalReviewer
  }: {
    isInternalReviewer?: any
  }) => {
    openDialog({
      title: "Assign",
      component: <AssignComponent isInternalReviewer={isInternalReviewer} />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/projects/${project?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: "Assign"
      }
    })
  }
  const onApproveRejectProject = ({ approve }: { approve: boolean }) => {
    const approveRejectLabel = approve ? "Approve" : "Reject"
    openDialog({
      title: approveRejectLabel,
      component: (
        <>
          {approve ? (
            <>
              <DownloadButton
                projectID={Number(project?.id)}
                projectName={project?.name ?? ""}
                type="no-objection-letter"
              />
              <Input required type="file" name="review_project_document" />
            </>
          ) : (
            <>
              <Input name="reason" label="Reason" placeholder="Enter reason" />
            </>
          )}

          <input type="hidden" name="id" value={project?.id}></input>
          <input
            type="hidden"
            name="action"
            value={approve ? "approve" : "reject"}></input>
        </>
      ),
      submitOption: {
        options: {
          method: "post",
          action: `/admin/projects/${project?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: approveRejectLabel
      }
    })
  }

  const onRequestLON = () => {
    openDialog({
      title: "Request LON",
      component: <RequestLNOComponent />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/projects/${project?.id}`,
          encType: "multipart/form-data"
        }
      }
    })
  }

  const onInvalidRequestLON = () => {
    openDialog({
      title: "Invalid Request LON",
      component: (
        <div>
          <input type="hidden" name="id" value={project?.id}></input>
          <input type="hidden" name="action" value="invalid" />
          <Input name="reason" label="Reason" placeholder="Enter reason" />
        </div>
      ),
      submitOption: {
        options: {
          method: "post",
          action: `/admin/projects/${project?.id}`,
          encType: "multipart/form-data"
        }
      }
    })
  }

  return (
    <>
      {project?.status !== EProjectStatus.PendingLnoRequest && (
        <>
          {(user.role === ERole.InternalReviewer ||
            user.role === ERole.IndependentAuditor) &&
            assignReviewProjectByUser.length > 0 && (
              <div>
                <Card className="p-4 my-4">
                  <div className=" flex justify-between items-center">
                    <div className="mb-2">
                      {user.role === ERole.InternalReviewer
                        ? "Internal Reviewer"
                        : "External Auditor"}
                    </div>
                  </div>
                  <AssignCard
                    assignList={assignReviewProjectByUser}
                    data={project}
                    type="project"
                  />
                </Card>
                {assignReviewProjectByUser[0]?.support_doucment && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      Doucment: {assignReviewProjectByUser[0].support_doucment}
                    </div>
                  </>
                )}
              </div>
            )}
          {(user?.role === ERole.Administrator ||
            user?.role === ERole.Coordinator ||
            user?.role === ERole.Secretariat) && (
            <>
              <div>
                <Card className="p-4 my-4">
                  <div className=" flex justify-between items-center">
                    <div>Internal Reviewer</div>
                  </div>
                  {assignInternal?.length > 0 ? (
                    assignInternal?.map((item: any, index: number) => {
                      return (
                        <div key={index}>
                          <div className="bg-gray-200 p-3 grid gap-3 rounded-md my-2">
                            <div className="grid grid-cols-3">
                              <div>Name</div>
                              <div>Status</div>
                              <div>Document</div>
                            </div>
                            <div className="grid grid-cols-3">
                              <div>
                                {item.user?.firstName} {item?.user?.lastName}
                              </div>
                              <div>{item?.status}</div>
                              <div>
                                {item?.review_document ? (
                                  <Link
                                    target="_blank"
                                    href={getImage(item?.review_document)}>
                                    Review Document
                                  </Link>
                                ) : (
                                  "N/A"
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    <>
                      <div className="flex justify-center">
                        {project?.status ===
                        EProjectStatus.PendingReviewLnoRequest
                          ? "There is no Internal Reviewer Assign. Please, Click button assign below"
                          : "There is no Internal Reviewer Assign"}
                      </div>
                      {project?.status ===
                        EProjectStatus.PendingReviewLnoRequest && (
                        <div className="flex justify-center mt-3">
                          <Button
                            color="primary"
                            type="submit"
                            className="bg-primary text-white"
                            onPress={() =>
                              handleAssign({
                                isInternalReviewer: true
                              })
                            }>
                            Assign
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </Card>
              </div>
              <div>
                {assignInternal[0]?.support_document && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      Internal Reviewer Doucment:{" "}
                      <Link
                        target="_blank"
                        href={getImage(assignInternal[0].support_document)}
                        className="text-black">
                        Support Doucment
                      </Link>
                    </div>
                  </>
                )}
              </div>
              <div>
                {assingExternal[0]?.support_document && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      External Auditor Doucment:{" "}
                      <Link
                        target="_blank"
                        href={getImage(assingExternal[0].support_document)}
                        className="text-black">
                        Support Doucment
                      </Link>
                    </div>
                  </>
                )}
              </div>
            </>
          )}
          {isApproveReject &&
            hasPermissions(user?.role, [
              EModules.ApproveProject,
              EModules.RejectProject
            ]) && (
              <div className="flex">
                <DownloadButton
                  projectID={project?.id}
                  projectName={project?.name}
                  type="no-objection-letter"
                />
              </div>
            )}
        </>
      )}
      {isReview && hasPermissions(user.role, [EModules.ReviewProject]) && (
        <>
          <div className="flex justify-center space-x-2">
            <div>
              <Button
                color="danger"
                onPress={() => {
                  onInvalidRequestLON()
                }}>
                Invalid
              </Button>
            </div>
            <div>
              <Form method="POST">
                <input type="hidden" name="action" value="review" />
                <input type="hidden" name="id" value={project?.id} />
                <Button className="bg-primary" type="submit">
                  <p className="text-white">Start To Review</p>
                </Button>
              </Form>
            </div>
          </div>
        </>
      )}
      <div className="flex justify-end space-x-2">
        {isFeedback &&
          hasPermissions(user.role, [EModules.FeedbackProject]) && (
            <Form method="POST">
              <input type="hidden" name="action" value="feedback" />
              <input type="hidden" name="id" value={project?.id} />
              <Button className="bg-primary" type="submit">
                <p className="text-white">Feedback</p>
              </Button>
            </Form>
          )}
        {project?.status === EProjectStatus.PendingReviewLnoRequest &&
          hasPermissions(user.role, [EModules.RequestLNO]) && (
            <>
              <Button
                color="primary"
                onPress={() => {
                  onRequestLON()
                }}>
                Request Issuing LNO
              </Button>
            </>
          )}
        {isApproveReject &&
          hasPermissions(user.role, [
            EModules.ApproveProject,
            EModules.RejectProject
          ]) && (
            <>
              <Button
                color="primary"
                onPress={() => {
                  onApproveRejectProject({
                    approve: true
                  })
                }}>
                Approve
              </Button>
              <Button
                color="danger"
                onPress={() => {
                  onApproveRejectProject({
                    approve: false
                  })
                }}>
                Reject
              </Button>
            </>
          )}
      </div>
    </>
  )
}

export default ReviewProjectTab
