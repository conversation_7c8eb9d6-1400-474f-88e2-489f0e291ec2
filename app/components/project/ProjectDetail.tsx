import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { ChevronLeft } from "lucide-react"
import { getImage } from "~/helpers/r2"
import ProjectTab from "./projectTab"

const ProjectDetail = ({
  implementationList,
  locationFile,
  pdd,
  project,
  isEdit = false,
  isDelete = false,
  isReview = false,
  isApproveReject = false,
  isSubmit = false,
  isFeedback = false,
  isAddImplementation = false,
  user,
  internalReviewerList,
  externalAuditorList,
  coordinatorList,
  assingList,
  commentList
}: {
  implementationList?: any
  locationFile?: any
  pdd?: any
  project: any
  isEdit?: boolean
  isDelete?: boolean
  isReview?: boolean
  isApproveReject?: boolean
  isSubmit?: boolean
  isFeedback?: boolean
  isAddImplementation?: boolean
  user?: any
  internalReviewerList?: any
  externalAuditorList?: any
  coordinatorList?: any
  assingList?: any
  commentList?: any
}) => {
  const navigate = useNavigate()
  const assignInternal = assingList?.filter(
    (item: any) => item?.isInternalReviewer === true
  )
  const assingExternal = assingList?.filter(
    (item: any) => item?.isInternalReviewer === false
  )

  return (
    <>
      <div className="flex justify-between items-center mb-2">
        <div>
          <Button
            onPress={() => {
              navigate(-1)
            }}
            startContent={<ChevronLeft />}
            variant="light"
            className="p-0">
            Back
          </Button>
        </div>
        <div>
          <p className="font-light text-gray-500">{project?.id}</p>
        </div>
      </div>
      <div>
        <div className="w-full h-80 relative">
          <img
            className="w-full h-full object-cover rounded-[20px]"
            src={
              project?.cover_img
                ? getImage(project.cover_img)
                : "/project1.jpeg"
            }
          />
          <div className="absolute bottom-0 p-6 w-full">
            <div className="flex justify-between">
              <div className="max-w-[30%]">
                <p className="text-[23px] font-bold text-white">
                  {project?.name}
                </p>
              </div>
              <div className="flex items-end">
                <Chip size="lg" radius={"sm"}>
                  {project?.status}
                </Chip>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <div className="grid flex-wrap gap-2">
            <ProjectTab
              user={user}
              pdd={pdd}
              assingList={assingList}
              project={project}
              assignInternal={assignInternal}
              implementationList={implementationList}
              assingExternal={assingExternal}
              isAddImplementation={isAddImplementation}
              internalReviewerList={internalReviewerList}
              externalAuditorList={externalAuditorList}
              coordinatorList={coordinatorList}
              isApproveReject={isApproveReject}
              isReview={isReview}
              isFeedback={isFeedback}
              commentList={commentList}
              isEdit={isEdit}
              isDelete={isDelete}
              isSubmit={isSubmit}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export default ProjectDetail
