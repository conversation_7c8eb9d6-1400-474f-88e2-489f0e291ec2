export function generateSalt(length: number = 10): string {
  const salt = new Uint8Array(length)
  crypto.getRandomValues(salt)
  return [...salt].map(b => b.toString(16).padStart(2, "0")).join("")
}

export async function hash(rawPwd: string): Promise<string> {
  const encoder = new TextEncoder()
  const salt = generateSalt(10)
  const password = rawPwd + `.` + salt
  const data = encoder.encode(password)
  const hashBuffer = await crypto.subtle.digest("SHA-256", data)
  const hash = bufferToHex(hashBuffer)
  return `${hash}.${salt}`
}

function bufferToHex(buffer: ArrayBuffer): string {
  return [...new Uint8Array(buffer)]
    .map(b => b.toString(16).padStart(2, "0"))
    .join("")
}

export async function verify(
  rawPwd: string,
  storedPwd: string
): Promise<boolean> {
  const encoder = new TextEncoder()
  const [_, salt] = storedPwd.split(".")
  const saltedPwd = rawPwd + `.` + salt
  const data = encoder.encode(saltedPwd)
  const hashBuffer = await crypto.subtle.digest("SHA-256", data)
  const hash = bufferToHex(hashBuffer)
  const pwd = `${hash}.${salt}`
  return pwd === storedPwd
}

export function strongPwdRegex(): RegExp {
  return /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9]).{8,}$/
}
