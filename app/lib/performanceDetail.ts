export const useOfGhgErs = [
  { id: 1, name: "Use towards the achievement of an NDC" },
  { id: 2, name: "Other international mitigation purposes" },
  { id: 3, name: "Any use" }
]

export const typeOfCarbon = [
  { id: 1, name: "Bilateral mechanism" },
  { id: 2, name: "Independent mechanism" },
  { id: 3, name: "Article 6.4 mechanism" },
  { id: "other", name: "other" }
]

export const getTypeOfCarbonName = (id: number | string) => {
  const typeOfCarbonByID = typeOfCarbon.find(item => item.id === Number(id))
  return typeOfCarbonByID ? typeOfCarbonByID.name : null
}

export const getUseOfGhgErsName = (id: number | string) => {
  const useOfGhgErsByID = useOfGhgErs.find(item => item.id === Number(id))
  return useOfGhgErsByID ? useOfGhgErsByID.name : null
}
