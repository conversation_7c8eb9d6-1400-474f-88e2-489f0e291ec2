export const provinces = [
  { key: "01", label: "Banteay Meanchey" },
  { key: "02", label: "Battambang" },
  { key: "03", label: "Kampong Cham" },
  { key: "04", label: "Kampong Chhnang" },
  { key: "05", label: "Kampong Speu" },
  { key: "06", label: "Kampong Thom" },
  { key: "07", label: "Kampot" },
  { key: "08", label: "Kandal" },
  { key: "09", label: "Koh Kong" },
  { key: "10", label: "<PERSON><PERSON><PERSON>" },
  { key: "11", label: "<PERSON><PERSON><PERSON> Kiri" },
  { key: "12", label: "Phnom Penh" },
  { key: "13", label: "Preah Vihear" },
  { key: "14", label: "Prey Veng" },
  { key: "15", label: "Pursat" },
  { key: "16", label: "Ratanak Kiri" },
  { key: "17", label: "Siemreap" },
  { key: "18", label: "Preah Sihanouk" },
  { key: "19", label: "Stung Treng " },
  { key: "20", label: "Svay Rieng" },
  { key: "21", label: "Takeo" },
  { key: "22", label: "Oddar Meanchey" },
  { key: "23", label: "Kep" },
  { key: "24", label: "Pailin" },
  { key: "25", label: "Tboung Khmum" }
]

export const getProvinceName = (key: number | string | null) => {
  const provinceByKey = provinces.find(item => item.key === key)
  return provinceByKey ? provinceByKey.label : "N/A"
}
