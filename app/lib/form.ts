export function getFormDataFromObject(o: object) {
  const formData = new FormData()
  for (const k in o) formData.append(k, o[k as keyof typeof o])
  return formData
}

export function getUpdatedFormData(a: FormData, b: FormData) {
  const formData = new FormData()
  for (const [key, value] of a.entries()) {
    if (b.get(key) !== value && (typeof value !== "object" || value.size > 0)) {
      formData.append(key, value)
    }
  }
  return formData
}
