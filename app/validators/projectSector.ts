import { z } from "zod"
import { BaseValidator } from "."

export class ProjectSectorValidator extends BaseValidator {
  private readonly schema

  constructor() {
    super()
    this.schema = z.object({
      name: z.string().min(1, "Name is required"),
      abbr: z.string().min(1, "Abbreviation is required"),
      parentId: z.coerce.number().optional(),
      description: z.string().optional()
    })
  }

  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }

  async validateEdit(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.partial().safeParseAsync(raw)
    return this.flatten(res)
  }

  async validateDelete(data: FormData) {
    const raw = this.getRaw(data)
    const res = await z.object({ id: z.coerce.number() }).safeParseAsync(raw)
    return this.flatten(res)
  }
}
