import { z } from "zod"
import { BaseValidator } from "."
import OrganizationService from "~/services/db/organization"

export class OrganizationValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema
  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z
      .object({
        "org.name": this.required(),
        "org.email": this.uniqueEmail(this.validEmail())
      })
      .passthrough()
  }
  async validateCreate(data: FormData) {
    const res = await this.schema.safeParseAsync(this.getRaw(data))
    return this.flatten(res)
  }
  private uniqueEmail(val: z.ZodString) {
    return val.refine(
      async email => {
        const check = await new OrganizationService(this.db).checkEmail(email)
        return !check
      },
      { message: "Email already exists" }
    )
  }
  private validEmail() {
    return z.string().min(1, "Email is required").email("Email is not valid")
  }
}
