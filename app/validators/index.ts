import { z } from "zod"

interface ValidNameParam {
  space?: boolean
}

export class BaseValidator {
  protected required() {
    return z.string().min(1, "This field is required")
  }

  protected validId() {
    return z.coerce.number().min(0, "This field is required")
  }

  protected validName(param?: ValidNameParam) {
    const allowSpace = param?.space ? " " : ""
    const re = new RegExp(`^[a-zA-Z0-9${allowSpace}]+$`, "gi") // Dynamically allow space
    return z
      .string()
      .min(1, "Name is required")
      .max(50, "Name must not exceed 50 characters")
      .regex(re, "Special characters are not allowed.")
  }

  protected validPhoneNumber() {
    const re = new RegExp(String.raw`^\+?[0-9]{7,15}$`, "g") // Allows optional '+' and 7 to 15 digits
    return z
      .string()
      .min(7, "Phone number must have at least 7 digits")
      .max(15, "Phone number must not exceed 15 digits")
      .regex(re, "Invalid phone number format")
  }

  private validateFileSize = (file: File | null) => {
    if (!file) return false
    const MAX_FILE_SIZE = 5000000 // 5MB
    return file.size <= MAX_FILE_SIZE
  }

  private validateFileType = (file: File | null) => {
    if (!file) return false
    const allowedTypes = ["image/jpeg", "image/png", "image/gif"]
    return allowedTypes.includes(file.type)
  }

  protected validImageFile() {
    return z
      .custom<File>()
      .refine(file => this.validateFileSize(file), "File is too large. Max 5MB")
      .refine(
        file => this.validateFileType(file),
        "Only jpg, png, gif are supported"
      )
  }

  protected getRaw(data: FormData) {
    const raw: { [key: string]: any } = {}
    for (const key of data.keys()) {
      const val = data.get(key) || undefined
      if (
        val &&
        (!(val instanceof File) || (val instanceof File && val.size > 0))
      )
        raw[key] = val
    }
    return raw
  }

  protected flatten<Input, Output>(res: z.SafeParseReturnType<Input, Output>) {
    return {
      data: res.data,
      error: res.success ? null : res.error.flatten().fieldErrors
    }
  }

  protected flattenV2<Input, Output>(
    res: z.SafeParseReturnType<Input, Output>
  ) {
    let error: Record<string, string> | null = null
    if (!res.success) {
      const _error = res.error.flatten().fieldErrors
      error = {}
      Object.entries(_error).forEach(([key, value]) => {
        if (error) {
          error[key] = Array.isArray(value) ? value[0] : value
        }
      })
    }
    return {
      data: res.data,
      error
    }
  }
}
