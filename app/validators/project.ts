import { z } from "zod"
import { BaseValidator } from "."

export class ProjectValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema

  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z
      .object({
        sector_id: this.validId(),
        name: this.required(),
        status: z.string().optional(),
        cover_img: z.instanceof(File).optional(),
        country: z.string().optional(),
        province: z.string().optional(),
        city: z.string().optional(),
        total_project_area: z.string().optional(),
        lat: z.string().optional(),
        lng: z.string().optional(),
        estimate_emission_reduction: z.string().optional()
      })
      .passthrough()
  }

  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }

  async validateEdit(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.partial().safeParseAsync(raw)
    return this.flatten(res)
  }
}
