import { z } from "zod"
import { BaseValidator } from "."

export class AssignValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema

  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z.object({
      user_id: this.validId(),
      project_id: this.validId(),
      support_document: z.instanceof(File)
    })
  }
  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }
}
