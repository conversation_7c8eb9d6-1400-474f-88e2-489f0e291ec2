import { z } from "zod"
import { ERole } from "~/enums/EUserRole"
import { strongPwdRegex } from "~/lib/password"
import UserService from "~/services/db/user"
import { BaseValidator } from "."

export class UserValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema

  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z
      .object({
        firstName: this.validName(),
        lastName: this.validName(),
        email: this.uniqueEmail(this.validEmail()),
        password: this.strongPassword(),
        confirmPassword: z.string().min(1, "Confirm Password is required"),
        role: z.enum([
          ERole.Administrator,
          ERole.IndependentAuditor,
          ERole.InternalReviewer,
          ERole.ProjectDeveloper,
          ERole.Secretariat,
          ERole.Coordinator
        ])
      })
      .passthrough()
  }

  private strongPassword() {
    return z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .refine(pwd => pwd === "" || strongPwdRegex().test(pwd), {
        message:
          "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character."
      })
  }

  private uniqueEmail(val: z.ZodString) {
    return val.refine(
      async email => {
        const check = await new UserService(this.db).checkEmail(email)
        return !check
      },
      { message: "Email already exists" }
    )
  }

  private validEmail() {
    return z.string().min(1, "Email is required").email("Email is not valid")
  }

  async validateRegisteredEmail(data: FormData) {
    const schema = z.object({
      email: this.validEmail().refine(
        async email => {
          const exist = await new UserService(this.db).checkEmail(email)
          return exist === true
        },
        { message: "Email not found" }
      )
    })
    const res = await schema.safeParseAsync(this.getRaw(data))
    return this.flatten(res)
  }

  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }

  async validateEdit(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.partial().safeParseAsync(raw)
    return this.flatten(res)
  }

  async validateResetPassword(data: FormData) {
    const schema = z
      .object({
        user_id: z.string(),
        password: this.strongPassword(),
        confirmPassword: z.string().min(1, "Confirm Password is required")
      })
      .refine(data => data.password === data.confirmPassword, {
        message: "Confirm Password must match Password",
        path: ["confirmPassword"]
      })

    const res = await schema.safeParseAsync(this.getRaw(data))
    const result = this.flattenV2(res)
    return result
  }
  async validateChangePassword(data: FormData) {
    const schema = z
      .object({
        user_id: z.string(),
        user_email: this.validEmail(),
        password: this.strongPassword(),
        confirmPassword: z.string().min(1, "Confirm Password is required")
      })
      .refine(data => data.password === data.confirmPassword, {
        message: "Confirm Password must match Password",
        path: ["confirmPassword"]
      })

    const res = await schema.safeParseAsync(this.getRaw(data))
    const result = this.flattenV2(res)
    return result
  }

  async validateCreateSuper(data: FormData) {
    const raw = Object.fromEntries(data.entries())
    return this.schema
      .pick({
        firstName: true,
        lastName: true,
        email: true,
        password: true
      })
      .safeParseAsync(raw)
      .then(res => this.flatten(res))
  }
}
