import { z } from "zod"
import { strongPassword, uniqueEmail } from "~/utils/validationUtils"
import { BaseValidator } from "."

export class RegistrationValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema

  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z
      .object({
        firstName: this.validName({ space: true }),
        lastName: this.validName({ space: true }),
        email: uniqueEmail(this.db),
        password: strongPassword(),
        national_id_passport_document: z.instanceof(File),
        business_registration_document: z.instanceof(File),
        organization_authorization_letter_document: z.instanceof(File)
      })
      .passthrough()
  }

  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }
}
