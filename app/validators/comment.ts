import { z } from "zod"
import { BaseValidator } from "."

export class CommentValidator extends BaseValidator {
  private readonly db: D1Database
  private readonly schema
  constructor(db: D1Database) {
    super()
    this.db = db
    this.schema = z.object({
      project_id: this.validId(),
      user_id: this.validId(),
      comment: z.string().min(1, "Comment is required")
    })
  }

  async validateCreate(data: FormData) {
    const raw = this.getRaw(data)
    const res = await this.schema.safeParseAsync(raw)
    return this.flatten(res)
  }
}
