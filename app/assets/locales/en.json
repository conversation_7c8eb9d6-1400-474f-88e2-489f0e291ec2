{"common.app_name": "Cambodia Carbon Credit Registry", "common.welcome": "Welcome to Carbon Project Registry", "common.language": "Language", "common.select_language": "Select Language", "common.english": "English", "common.khmer": "ភាសាខ្មែរ", "common.register": "Register", "common.registration": "Registration", "common.login": "<PERSON><PERSON>", "common.submit": "Submit", "common.role": "Role", "common.welcome_user": "Welcome back, <b>{{username}}</b>. Let's drive sustainability together!", "common.view": "View", "common.projects": "Projects", "common.close": "Close", "button.ok": "Ok", "button.save": "Save", "button.cancel": "Cancel", "page.home": "Home", "project.create_new_project": "Create new project", "auth.registration.title": "User Registration", "auth.registration.form": "Application Form", "auth.registration.account_holder": "Account Holder", "auth.registration.first_name": "First Name", "auth.registration.last_name": "Last Name", "auth.registration.enter_first_name": "Enter first name", "auth.registration.enter_last_name": "Enter last name", "auth.registration.email": "Email", "auth.registration.enter_email": "Enter your email", "auth.registration.password": "Password", "auth.registration.enter_password": "Enter password", "auth.registration.confirm_password": "Confirm Password", "auth.registration.enter_confirm_password": "Enter confirm password", "auth.registration.phone_number": "Phone Number", "auth.registration.enter_phone_number": "Enter your phone number", "auth.registration.nationality": "Nationality", "auth.registration.enter_nationality": "Choose your nationality", "auth.registration.national_id_passport": "National ID", "auth.registration.enter_national_id_password": "Enter your national ID", "auth.registration.address": "Address", "auth.registration.enter_address": "Enter your address", "auth.registration.province": "Province", "auth.registration.enter_province": "Choose your province", "auth.registration.district": "District", "auth.registration.enter_district": "choose your district", "auth.registration.sub_district": "Sub-District", "auth.registration.enter_sub_district": "Enter your sub-district", "auth.registration.postal_code": "Postal Code", "auth.registration.enter_postal_code": "Enter your postal code", "auth.registration.support_document": "Support Documents", "auth.registration.add_file": "Add file", "auth.registration.add_another_file": "Add another file", "auth.registration.continue": "Continue", "auth.registration.cancel": "Cancel", "auth.registration.government_entity": "Government entity", "auth.registration.project_developers": "Project developers", "auth.registration.third-party_verifiers": "Third-party verifiers", "user.role.administrator": "Administrator", "user.role.secretariat": "Secretariat", "user.role.internal_reviewer": "Internal Reviewer", "user.role.independent_auditor": "Independent Auditor", "user.role.project_developer": "Project Developer", "user.role.coordinator": "Coordinator", "common.project_developer_list": "Project Developer List", "common.add": "Add", "common.filter": "Filter", "auth.login.forgot_password": "Forgot Password?", "common.submitting": "Submitting", "auth.login.remember_me": "Remember me", "auth.registration.read_term_uses": "I have read the Terms of Use and have the authority to bind my organization.", "auth.registration.commune": "<PERSON><PERSON><PERSON>", "auth.registration.choose_your_commune": "Choose your commune", "auth.registration.overview": "Overview", "auth.registration.agreement": "Agreements", "auth.registration.account_type": "Account Type", "common.open_account": "Open an Account", "project_step.project-information": "Project Information", "project_step.alignments": "Alignments", "project_step.lno-request": "LNO Request"}