import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react"
import type {
  LinksFunction,
  LoaderFunctionArgs,
  MetaFunction
} from "@remix-run/cloudflare"
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useNavigate,
  useRouteError
} from "@remix-run/react"
import { useTranslation } from "react-i18next"
import { useChangeLanguage } from "remix-i18next/react"
import { DialogProvider } from "~/components/dialog"
import { i18next } from "./i18n.server"
import tailwindHref from "./tailwind.css?url"

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: tailwindHref },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous"
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
  }
]

export const meta: MetaFunction = () => {
  return [{ title: "Cambodia Carbon Registry" }]
}

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request)
  return { locale }
}

function Layout({
  lang,
  dir,
  children
}: Readonly<{ lang: string; dir: string; children: React.ReactNode }>) {
  return (
    <html lang={lang} dir={dir}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  const navigate = useNavigate()
  const { locale } = useLoaderData<typeof loader>() ?? {}
  const { i18n } = useTranslation()
  useChangeLanguage(locale)

  return (
    <Layout lang={locale} dir={i18n.dir()}>
      <HeroUIProvider navigate={navigate}>
        <DialogProvider>
          <Outlet />
        </DialogProvider>
      </HeroUIProvider>
    </Layout>
  )
}

export function ErrorBoundary() {
  const error = useRouteError()
  const { t } = useTranslation()
  const isResponseError = isRouteErrorResponse(error)

  const style = {
    subTitle: "text-2xl font-bold text-red-500",
    title: "text-5xl font-bold",
    desc: "text-lg text-gray-600"
  }

  const pageNotFound = (
    <div className="space-y-5">
      <header>
        <span className={style.subTitle}>Error 404</span>
        <h1 className={style.title}>{t("Page not found")}</h1>
      </header>
      <p className={style.desc}>
        {t(
          "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."
        )}
      </p>
      <Button as="a" href="/" color="primary">
        {t("Go back home")}
      </Button>
    </div>
  )

  const serverError = (
    <div className="space-y-5">
      <header>
        <span className={style.subTitle}>Internal Server Error</span>
        <h1 className={style.title}>{t("Something went wrong")}</h1>
      </header>
      <p className={style.desc}>
        {t("An unexpected error occurred. Please try again later.")}
      </p>
      <Button as="a" href="/" color="primary">
        {t("Go back home")}
      </Button>
      <Outlet />
    </div>
  )

  return (
    <Layout lang="en" dir="ltr">
      <div className="bg-gray-100 min-h-screen flex items-center justify-center">
        <div className="max-w-xl w-full p-10 bg-white rounded-lg shadow-lg text-center">
          {isResponseError && error.status === 404 ? pageNotFound : serverError}
        </div>
      </div>
    </Layout>
  )
}
