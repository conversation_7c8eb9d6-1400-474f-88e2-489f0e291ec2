import { nanoid } from "nanoid"

export const getKey = (file: File, folderName?: string) => {
  const ext = file.name.split(".").pop()
  const name = folderName ? `${folderName}/${nanoid()}` : nanoid()
  return `${name}.${ext}`
}

export const getImage = (keyname: string | null) => {
  if (!keyname) return null
  if (process.env.NODE_ENV === "production") {
    return `/media/${keyname}`
  }
  return `http://localhost:8788/media/${keyname}`
}
