export function fieldError(msg: string) {
  return (
    <div className="label">
      <span className="label-text-alt text-error">{msg}</span>
    </div>
  )
}

export function getErrorField(name: string, error: any) {
  let errorField: { isInvalid?: boolean; errorMessage?: string } = {}

  if (error && error[name]) {
    errorField.isInvalid = true
    errorField.errorMessage = Array.isArray(error[name])
      ? error[name][0]
      : error[name]
  }

  return errorField
}

export function ErrorField({
  error,
  name
}: {
  error: Record<string, string>
  name: string
}) {
  const errorField = getErrorField(name, error)
  if (!errorField) return null
  return (
    <div className="label">
      <span className="label-text-alt text-error">
        {errorField.errorMessage}
      </span>
    </div>
  )
}
