export enum EProjectStatus {
  Draft = "draft",
  PendingLnoRequest = "pending_lno_request",
  PendingReviewLnoRequest = "pending_review_lno_request",
  LnoIssuingRequest = "lno_issuing_request",
  SignedLNO = "signed_lno",
  Submitted = "submitted",
  UnderReview = "under_review",
  Approved = "approved",
  Rejected = "rejected",
  Verified = "verified",
  PendingLOARequest = "pending_loa_request",
  InvalidLOARequest = "invalid_loa_request",
  PendingPDDReview = "pending_pdd_review",
  ReviewedPDD = "reviewed_pdd",
  PendingIssuingLOARequest = "pending_issuing_loa_request",
  SignedLOA = "signed_loa",
  RejectedLOARequestModification = "rejected_loa_request_modification"
}
