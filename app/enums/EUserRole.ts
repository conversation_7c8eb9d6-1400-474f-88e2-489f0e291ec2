export enum ERole {
  Administrator = "administrator",
  Secretariat = "secretariat",
  InternalReviewer = "internal_reviewer",
  IndependentAuditor = "independent_auditor",
  Coordinator = "coordinator",
  ProjectDeveloper = "project_developer"
}

export enum EModules {
  ViewProject = "view-project",
  CreateProject = "create-project",
  ReviewProject = "review-project",
  FeedbackProject = "feedback-project",
  ApproveProject = "approve-project",
  RejectProject = "reject-project",
  ViewUserList = "view-user-list",
  RequestLNO = "request-lno",
  RequestIssuingLOA = "request-issuing-loa",
  ApproveUser = "approve-user",
  RejectUser = "reject-user",
  ImplementationMenu = "implementation-menu",
  ImplementationTab = "implementation-tab",
  AddImplementation = "add-implementation",
  AddPDD = "add-pdd",
  ReviewPDD = "review-pdd",
  PDDMenu = "pdd-menu",
  ReviewPerformanceReport = "review-performance-report",
  LPERequest = "lpe-request",
  ApproveAndRejectReviewformanceReport = "approve-and-reject-reviewformance-report",
  SettingPage = "setting-page"
}
