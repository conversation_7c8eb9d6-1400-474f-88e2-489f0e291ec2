export enum ENotification {
  User = "user",
  Project = "project",
  Comment = "comment",
  Review = "review",
  Assign = "assign",
  ReviewApproved = "reviewApproved",
  ReviewRejected = "reviewRejected",
  ProjectApproved = "projectApproved",
  ProjectRejected = "projectRejected"
}

export enum ENotificationText {
  User = "New user request to create new account",
  Project = "New project has been submitted",
  Comment = "New comment on the project",
  Review = "Your project has been reviewed by administrator",
  Assign = "You has been assigned to this project",
  ReviewApproved = "Internal Reviewer Approved the project",
  ReviewRejected = "Internal Reviewer Rejected the project",
  ProjectApproved = "Your project has been Approved",
  ProjectRejected = "Your project has been Rejected"
}
