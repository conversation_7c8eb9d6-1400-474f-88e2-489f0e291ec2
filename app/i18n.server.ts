import { createInstance } from "i18next"
import i18n from "./i18n.config"
import { createCookie } from "@remix-run/cloudflare"
import { RemixI18Next } from "remix-i18next/server"
import en from "~/assets/locales/en.json"
import km from "~/assets/locales/km.json"

export const langCookie = createCookie("language", {
  maxAge: 604_800 // one week
})

export const i18nextInstance = createInstance()

export const i18next = new RemixI18Next({
  detection: {
    cookie: langCookie,
    supportedLanguages: i18n.supportedLngs,
    fallbackLanguage: i18n.fallbackLng
  },
  i18next: {
    ...i18n,
    resources: {
      en: {
        common: en
      },
      km: {
        common: km
      }
    }
  }
})
