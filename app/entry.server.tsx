import type { AppLoadContext, EntryContext } from "@remix-run/cloudflare"
import { RemixServer } from "@remix-run/react"
import { isbot } from "isbot"
import { renderToReadableStream } from "react-dom/server"
import { I18nextProvider, initReactI18next } from "react-i18next"
import i18n from "./i18n.config"
import { i18next, i18nextInstance } from "./i18n.server"

const ABORT_DELAY = 5000

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  // This is ignored so we can keep it in the template for visibility.  Feel
  // free to delete this parameter in your app if you're not using it!
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadContext: AppLoadContext
) {
  // Import translations dynamically
  const translations = {
    en: (await import("./assets/locales/en.json")).default,
    km: (await import("./assets/locales/km.json")).default
  }
  const lng = await i18next.getLocale(request)
  const ns = i18next.getRouteNamespaces(remixContext)
  await i18nextInstance.use(initReactI18next).init({
    ...i18n,
    lng,
    ns,
    resources: {
      en: { common: translations.en },
      km: { common: translations.km }
    }
  })
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), ABORT_DELAY)

  const body = await renderToReadableStream(
    <I18nextProvider i18n={i18nextInstance}>
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={ABORT_DELAY}
      />
    </I18nextProvider>,
    {
      signal: controller.signal,
      onError(error: unknown) {
        if (!controller.signal.aborted) {
          // Log streaming rendering errors from inside the shell
          console.error(error)
        }
        responseStatusCode = 500
      }
    }
  )

  body.allReady.then(() => clearTimeout(timeoutId))

  if (isbot(request.headers.get("user-agent") || "")) {
    await body.allReady
  }

  responseHeaders.set("Content-Type", "text/html")
  return new Response(body, {
    headers: responseHeaders,
    status: responseStatusCode
  })
}
