import { EModules, ERole } from "~/enums/EUserRole"

const userRoles: Record<Exclude<ERole, ERole.Administrator>, EModules[]> = {
  [ERole.IndependentAuditor]: [
    EModules.ViewProject,
    EModules.ImplementationMenu,
    EModules.ReviewProject,
    EModules.PDDMenu
  ],
  [ERole.InternalReviewer]: [
    EModules.ViewProject,
    EModules.ReviewProject,
    EModules.ImplementationMenu,
    EModules.AddPDD,
    EModules.PDDMenu
  ],
  [ERole.ProjectDeveloper]: [
    EModules.CreateProject,
    EModules.AddImplementation
  ],
  [ERole.Secretariat]: [
    EModules.ViewProject,
    EModules.ReviewProject,
    EModules.RequestLNO,
    EModules.ImplementationMenu,
    EModules.RequestIssuingLOA,
    EModules.ReviewPDD,
    EModules.PDDMenu,
    EModules.ReviewPerformanceReport,
    EModules.LPERequest
  ],
  [ERole.Coordinator]: [
    EModules.ViewProject,
    EModules.ReviewProject,
    EModules.RejectProject,
    EModules.ApproveProject,
    EModules.PDDMenu,
    EModules.ImplementationMenu,
    EModules.ApproveAndRejectReviewformanceReport
  ]
}

export const hasPermissions = (role: ERole, permissions: EModules[]) => {
  if (!role) {
    return false
  }

  if (role === ERole.Administrator) {
    return true
  }

  return permissions.every(permission => userRoles[role]?.includes(permission))
}
