import {
  Check,
  ClipboardPlus,
  Eye,
  Factory,
  MessageSquareMore,
  UserPlus,
  X
} from "lucide-react"
import { ENotification, ENotificationText } from "~/enums/ENotification"

export const notificationDescription = (type: string | null) => {
  switch (type) {
    case ENotification.User:
      return ENotificationText.User
    case ENotification.Project:
      return ENotificationText.Project
    case ENotification.Comment:
      return ENotificationText.Comment
    case ENotification.Review:
      return ENotificationText.Review
    case ENotification.Assign:
      return ENotificationText.Assign
    case ENotification.ReviewApproved:
      return ENotificationText.ReviewApproved
    case ENotification.ReviewRejected:
      return ENotificationText.ReviewRejected
    case ENotification.ProjectApproved:
      return ENotificationText.ProjectApproved
    case ENotification.ProjectRejected:
      return ENotificationText.ProjectRejected
    default:
      return ""
  }
}

export const icon = (type: string | null) => {
  switch (type) {
    case ENotification.User:
      return UserPlus
    case ENotification.Project:
      return Factory
    case ENotification.Comment:
      return MessageSquareMore
    case ENotification.Review:
      return Eye
    case ENotification.Assign:
      return ClipboardPlus
    case ENotification.ReviewApproved:
      return Check
    case ENotification.ReviewRejected:
      return X
    case ENotification.ProjectApproved:
      return Check
    case ENotification.ProjectRejected:
      return X
    default:
      return UserPlus
  }
}
