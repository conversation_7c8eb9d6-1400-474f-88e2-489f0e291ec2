import { z } from "zod";
import { strongPwdRegex } from "~/lib/password";
import UserService from "~/services/db/user";

export const strongPassword = () =>
  z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .refine(pwd => pwd === "" || strongPwdRegex().test(pwd), {
      message:
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.",
    });

export const uniqueEmail = (db: D1Database) =>
  z.string().refine(
    async email => {
      const check = await new UserService(db).checkEmail(email);
      return !check;
    },
    { message: "Email already exists" }
  );

export const validEmail = () =>
  z.string().min(1, "Email is required").email("Email is not valid");
