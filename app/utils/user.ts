import type { AppLoadContext } from "@remix-run/cloudflare"
import { getAuthenticator } from "~/services/auth"
import type { CloudflareENV } from "~/types"

export const getAuthUser = async (
  context: AppLoadContext,
  request: Request
) => {
  const { authenticator } = getAuthenticator(
    context.cloudflare.env as CloudflareENV
  )
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: "/login"
  })

  return user
}
