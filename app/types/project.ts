import type { EProjectStatus } from "~/enums/EProjectStatus"
import type { Project_STEPS } from "~/enums/EProjectStep"
import type { IGetMany } from "."

export interface IProject {
  status: EProjectStatus
  reject_reason?: string | undefined | null
  project_step?: Project_STEPS
}

export type Project = Pick<IProject, "status"> & {
  id: number
  created_at: Date
  updated_at: Date | null
}

export type IGetManyProject = Pick<IProject, "status"> & {
  fromDate?: Date
  toDate?: Date
} & IGetMany
