import type { D1 } from "./base"

export interface IProjectSector {
  name: string
  abbr: string
  parentId?: number
  description?: string
}

export interface ICreateSector extends D1 {
  values: IProjectSector
}

export interface IUpdateSector extends D1 {
  id: number
  values: Partial<IProjectSector>
}

export type ProjectSectorFieldErrors = {
  name?: string[]
  abbr?: string[]
  parentId?: string[]
  description?: string[]
}

export type ProjectSector = {
  id: number
  name: string
  abbr: string
  parentId: number | null
  description: string | null
  created_at: Date
  updated_at: Date | null
}
