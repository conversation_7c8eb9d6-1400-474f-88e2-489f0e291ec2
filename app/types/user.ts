import type { EAccountType } from "~/enums/EAccountType"
import type { EStatus } from "~/enums/EStatus"
import type { ERole } from "~/enums/EUserRole"
import type { IGetMany } from "."

type UserGender = "M" | "F"

export interface IUser {
  accountType?: EAccountType
  job_title: string
  firstName: string
  lastName: string
  email: string
  password: string
  confirm_password: string
  nationality: string
  passport_national_id: string
  phone: string
  photoUrl?: string
  national_id_passport_document?: string
  business_registration_document?: string
  organization_authorization_letter_document?: string
  dob?: Date
  gender?: UserGender
  status?: EStatus
  isAdmin?: boolean
  isSuper?: boolean
  active?: boolean
  resetTokenKey?: string | null
}

export interface ICreateUser {
  email: string
  firstName: string
  lastName: string
  password: string
  status: EStatus.Approve | EStatus.Reject | EStatus.Review
  role?: ERole
  org_id?: number
  accountType?: EAccountType
  job_title?: string
  nationality?: string
  passport_national_id?: string
  phone?: string
  photoUrl?: string
  national_id_passport_document?: string
  business_registration_document?: string
  organization_authorization_letter_document?: string
  active?: boolean
  isSuper?: boolean
  isAdmin?: boolean
  resetTokenKey?: string
}

export interface ISignUp extends IUser {
  confirmPassword: string
}

export type SignupData = {
  next: string
  email: string
  data?: ISignUp
}

export type User = Pick<IUser, "email" | "firstName" | "lastName"> & {
  id: number
  org_id: number
  dob: Date | null
  gender: UserGender | null
  nationality: string | null
  job_title: string | null
  passport_id: string | null
  status: EStatus
  isAdmin: boolean
  isSuper: boolean
  created_at: Date
  photoUrl: string | null
  updated_at: Date | null
  role: ERole | null
}

export type AuthUser = Omit<User, "password" | "created_at" | "updated_at">

export type IGetManyUser = Pick<IUser, "isAdmin" | "status"> & {
  fromDate?: Date
  toDate?: Date
} & IGetMany
