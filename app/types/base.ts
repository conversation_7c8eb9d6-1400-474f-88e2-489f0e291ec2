import type { DrizzleD1Database } from "drizzle-orm/d1"

export type CloudflareENV = {
  DB: D1Database
  R2: R2Bucket
  SECRET: string
  RESEND_API_KEY: string
  RESET_PASSWORD_SECRET: string
  RESEND_FROM_EMAIL: string
  RESEND_CONTACT_TO_EMAIL: string
  WORKER_ENV: "local" | "preview" | "production"
  SITE_URL: string
}

export interface D1 {
  D1: D1Database
}

export type DrizzleD1<TSchema extends Record<string, unknown>> =
  DrizzleD1Database<TSchema>

export interface IGetMany {
  page?: number
  pageSize?: number
  sort?: "ASC" | "DESC"
  fromDate?: Date
  toDate?: Date
}
