import { type ActionFunctionArgs } from "@remix-run/cloudflare"
import { validateId } from "~/helpers/id"
import { ProjectSectorService } from "~/services/db"
import type {
  ActionResult,
  CloudflareENV,
  ProjectSectorFieldErrors
} from "~/types"
import { updateSector } from "./query.server"
import { validateEdit } from "./validate.server"

type SectorActionResult = ActionResult<ProjectSectorFieldErrors>

async function validateSectorId(db: D1Database, args: ActionFunctionArgs) {
  const { params } = args
  const id = validateId(params.id)
  const sector = await new ProjectSectorService(db).getOne(id)
  if (!sector) throw { ok: false }
  return id
}

async function update(args: ActionFunctionArgs): Promise<SectorActionResult> {
  try {
    const env = args.context.cloudflare.env as CloudflareENV
    const id = await validateSectorId(env.DB, args)
    const payload = await args.request.formData()
    const { data, error } = await validateEdit(payload)
    if (!data) throw { ok: false, fieldErrors: error }
    const sector = await updateSector({ db: env.DB, id, values: data })
    if (!sector) throw { ok: false }
    return { ok: true }
  } catch (error) {
    return error as SectorActionResult
  }
}

export async function action(
  args: ActionFunctionArgs
): Promise<SectorActionResult> {
  // TODO: check permission -- only for superadmin
  const method = args.request.method
  if (method === "PUT") return await update(args)
  // if (method === "DELETE") return await handleDelete(args)
  throw { ok: false, message: "Method not allowed" }
}
