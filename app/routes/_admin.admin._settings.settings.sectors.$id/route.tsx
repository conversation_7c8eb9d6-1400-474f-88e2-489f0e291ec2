import { data, type ActionFunctionArgs } from "@remix-run/cloudflare"
import { ProjectSectorService } from "~/services/db"
import type { CloudflareENV } from "~/types"
import { ProjectSectorValidator } from "~/validators/projectSector"
import { deleteSector, updateSector } from "./server/query"

async function validateAndGetSector(args: ActionFunctionArgs) {
  const { params, context } = args
  const env = context.cloudflare.env as CloudflareENV
  const id = Number(params.id)
  if (!id || Number.isNaN(id) || id === 1) {
    throw data({ ok: false, message: "Not Found" }, { status: 404 })
  }
  const sector = await new ProjectSectorService(env.DB).getOne(id)
  if (!sector) {
    throw data({ ok: false, message: "Not Found" }, { status: 404 })
  }
  return { id, env }
}

async function validateFormData(request: Request) {
  const payload = await request.formData()
  const validator = new ProjectSectorValidator()
  return await validator.validateEdit(payload)
}

async function handleEdit(args: ActionFunctionArgs): Promise<{
  ok: boolean
  message: string
  error: any
  sector: any
}> {
  try {
    const { id, env } = await validateAndGetSector(args)
    const payload = await args.request.formData()
    const validator = new ProjectSectorValidator()
    const v = await validator.validateEdit(payload)
    if (!v.data)
      throw data(
        { ok: false, message: "Bad Request", error: v.error },
        { status: 400 }
      )
    const sector = await updateSector({ D1: env.DB, id, values: v.data })
    return data({ ok: true, message: "Good", error: null, sector })
  } catch (error) {
    return error
  }
}

async function handleDelete(args: ActionFunctionArgs) {
  const validation = await validateAndGetSector(args)

  if (validation.error) {
    return data(
      { error: { id: [validation.error] }, sector: null },
      { status: 400 }
    )
  }

  const { id, env } = validation
  const { data: formData, error } = await validateFormData(args.request)

  if (!formData) {
    return data({ error, sector: null }, { status: 400 })
  }

  const deletedSector = await deleteSector({ D1: env.DB, id, values: formData })
  return data({ error: null, sector: deletedSector })
}

export async function action(args: ActionFunctionArgs) {
  // TODO: check permission -- only for superadmin
  const method = args.request.method
  if (method === "PUT") return await handleEdit(args)
  if (method === "DELETE") return await handleDelete(args)
  return data({ message: "Method not allowed" }, { status: 405 })
}
