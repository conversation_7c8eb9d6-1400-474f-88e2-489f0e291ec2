import { data, type ActionFunctionArgs } from "@remix-run/cloudflare"
import { error404 } from "~/helpers/errors"
import { ProjectSectorService } from "~/services/db"
import type { CloudflareENV, ProjectSector } from "~/types"
import { ProjectSectorValidator } from "~/validators/projectSector"
import { deleteSector, updateSector } from "./server/query"

interface SectorActionResult {
  error: Record<string, string[]> | null
  sector: ProjectSector[] | null
}

async function validateAndGetSector(args: ActionFunctionArgs) {
  const { params, context } = args
  const env = context.cloudflare.env as CloudflareENV
  const id = Number(params.id)

  if (!id || Number.isNaN(id) || id === 1)
    return data(
      { error: { id: ["Invalid sector id"] }, sector: null },
      { status: 400 }
    )

  const sector = await new ProjectSectorService(env.DB).getOne(id)
  if (!sector) throw error404()

  return { id, env, sector }
}

async function validateFormData(request: Request) {
  const payload = await request.formData()
  const validator = new ProjectSectorValidator()
  return await validator.validateEdit(payload)
}

async function handleEdit(
  args: ActionFunctionArgs
): Promise<SectorActionResult> {
  const { id, env } = await validateAndGetSector(args)
  const { data, error } = await validateFormData(args.request)

  if (!data) return { error, sector: null }

  const updatedSector = await updateSector({ D1: env.DB, id, values: data })
  return { error, sector: updatedSector }
}

async function handleDelete(
  args: ActionFunctionArgs
): Promise<SectorActionResult> {
  const { id, env } = await validateAndGetSector(args)
  const { data, error } = await validateFormData(args.request)

  if (!data) return { error, sector: null }

  const deletedSector = await deleteSector({ D1: env.DB, id, values: data })
  return { error, sector: deletedSector }
}

export async function action(args: ActionFunctionArgs) {
  // TODO: check permission -- only for superadmin
  const method = args.request.method
  if (method === "PUT") return handleEdit(args)
  if (method === "DELETE") return handleDelete(args)
  return null // do nothing
}
