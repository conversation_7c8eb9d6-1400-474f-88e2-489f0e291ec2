import type { ActionFunctionArgs } from "@remix-run/cloudflare"
import { error404 } from "~/helpers/errors"
import { ProjectSectorService } from "~/services/db"
import type { CloudflareENV } from "~/types"
import { ProjectSectorValidator } from "~/validators/projectSector"
import { updateSector } from "./server/query"

async function handleEdit(args: ActionFunctionArgs) {
  const { params, request, context } = args
  const env = context.cloudflare.env as CloudflareENV
  const id = Number(params.id)
  if (!id || Number.isNaN(id)) throw error404()
  const sector = await new ProjectSectorService(env.DB).getOne(id)
  if (!sector) throw error404()
  const payload = await request.formData()
  const v = new ProjectSectorValidator()
  const { data, error } = await v.validateEdit(payload)
  if (!data) return { error, sector: null }
  const updatedSector = await updateSector({ D1: env.DB, id, values: data })
  return { error, sector: updatedSector }
}

function handleDelete(args: ActionFunctionArgs) {
  const { params, request, context } = args
  const env = context.cloudflare.env as CloudflareENV
  const id = Number(params.id)
  if (!id || Number.isNaN(id)) throw error404()
  console.log("method", request.method)
  return { error: null, sector: null }
}

export async function action(args: ActionFunctionArgs) {
  // TODO: check permission -- only for superadmin
  const method = args.request.method
  if (method === "PUT") return handleEdit(args)
  if (method === "DELETE") return handleDelete(args)
  return null // do nothing
}
