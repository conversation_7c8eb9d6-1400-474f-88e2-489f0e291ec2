import { eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/d1"
import * as schema from "~/db/schema"
import type { ICreateSector, IUpdateSector } from "./interface"

export async function createSector(param: ICreateSector) {
  const { D1, values } = param
  const db = drizzle(D1, { schema })
  const sector = schema.projectSectors
  return await db.insert(sector).values(values).returning()
}

export async function updateSector(param: IUpdateSector) {
  const { D1, id, values } = param
  const db = drizzle(D1, { schema })
  const sector = schema.projectSectors
  return await db
    .update(sector)
    .set(values)
    .where(eq(sector.id, id))
    .returning()
}

export async function deleteSector(param: IUpdateSector) {
  const { D1, id, values } = param
  const db = drizzle(D1, { schema })
  const sector = schema.projectSectors
  return await db
    .update(sector)
    .set(values)
    .where(eq(sector.id, id))
    .returning()
}
