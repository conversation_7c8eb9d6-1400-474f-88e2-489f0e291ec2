import { z } from "zod"
import { flatten } from "~/utils/zod"

const schema = z.object({
  name: z.string().min(1, "Name is required"),
  abbr: z.string().min(1, "Abbreviation is required"),
  parentId: z.coerce.number().optional(),
  description: z.string().optional()
})

export async function validateCreate(data: FormData) {
  const raw = Object.fromEntries(data.entries())
  return await schema.safeParseAsync(raw).then(res => flatten(res))
}

export async function validateEdit(data: FormData) {
  const raw = Object.fromEntries(data.entries())
  return await schema
    .partial()
    .safeParseAsync(raw)
    .then(res => flatten(res))
}
