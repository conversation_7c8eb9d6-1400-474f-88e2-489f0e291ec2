import { eq } from "drizzle-orm"
import { getDB } from "~/db"
import * as schema from "~/db/schema"
import type { ICreateSector, IUpdateSector } from "~/types"

// Type-safe function to get schema tables with validation
function getTable(name: "projectSectors"): typeof schema.projectSectors
function getTable(name: "projects"): typeof schema.projects
function getTable(name: "users"): typeof schema.users
function getTable(name: string): any {
  const validTables = ["projectSectors", "projects", "users"] as const

  if (!validTables.includes(name as any)) {
    throw new Error(
      `Invalid table name: ${name}. Valid tables: ${validTables.join(", ")}`
    )
  }

  const table = (schema as any)[name]
  if (!table) {
    throw new Error(`Table '${name}' not found in schema`)
  }
  return table
}

function getDbConnection(database: D1Database) {
  return {
    db: getDB(database),
    sector: getTable("projectSectors")
  }
}

export async function checkSectorId(
  d1: D1Database,
  id: number
): Promise<boolean> {
  const { db } = getDbConnection(d1)
  return await db.query.projectSectors
    .findFirst({
      where: eq(schema.projectSectors.id, id),
      columns: { id: true }
    })
    .then(res => res !== undefined)
}

export async function createSector(param: ICreateSector) {
  const { db, sector } = getDbConnection(param.db)
  return await db.insert(sector).values(param.values).returning()
}

export async function updateSector(param: IUpdateSector) {
  const { db, sector } = getDbConnection(param.db)
  return await db
    .update(sector)
    .set(param.values)
    .where(eq(sector.id, param.id))
    .returning()
}
