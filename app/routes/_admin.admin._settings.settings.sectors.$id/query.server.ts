import { eq } from "drizzle-orm"
import { getDB } from "~/db"
import * as schema from "~/db/schema"
import type { ICreateSector, IUpdateSector } from "~/types"

export async function createSector(param: ICreateSector) {
  const { values } = param
  const db = getDB(param.db)
  return await db.insert(schema.projectSectors).values(values).returning()
}

export async function updateSector(param: IUpdateSector) {
  const db = getDB(param.db)
  const sector = schema.projectSectors
  return await db
    .update(sector)
    .set(param.values)
    .where(eq(sector.id, param.id))
    .returning()
}
