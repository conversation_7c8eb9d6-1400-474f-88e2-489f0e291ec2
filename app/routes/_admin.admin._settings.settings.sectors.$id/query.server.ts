import { eq } from "drizzle-orm"
import { getDB } from "~/db"
import * as schema from "~/db/schema"
import type { ICreateSector, IUpdateSector } from "~/types"

function getTable(name: string) {
  return schema[name]
}

function getDbConnection(database: D1Database) {
  return {
    db: getDB(database),
    sector: schema.projectSectors
  }
}

export async function checkSectorId(
  d1: D1Database,
  id: number
): Promise<boolean> {
  const { db } = getDbConnection(d1)
  return await db.query.projectSectors
    .findFirst({
      where: eq(schema.projectSectors.id, id),
      columns: { id: true }
    })
    .then(res => res !== undefined)
}

export async function createSector(param: ICreateSector) {
  const { db, sector } = getDbConnection(param.db)
  return await db.insert(sector).values(param.values).returning()
}

export async function updateSector(param: IUpdateSector) {
  const { db, sector } = getDbConnection(param.db)
  return await db
    .update(sector)
    .set(param.values)
    .where(eq(sector.id, param.id))
    .returning()
}
