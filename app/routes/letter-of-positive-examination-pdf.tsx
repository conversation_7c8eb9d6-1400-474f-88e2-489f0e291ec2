import { PDFDocument, rgb, StandardFonts } from "pdf-lib"

interface RequestBody {
  date: string
  projectID: number | null
  projectName: string | null
  totalVolumeAuthorized?: number
  totalVolumeReceviedPositiveExamination?: number
  remainVolume?: number
}

export const action = async ({ request }: { request: Request }) => {
  const body: RequestBody = await request.json()

  const pdfDoc = await PDFDocument.create()
  const page = pdfDoc.addPage([595, 842]) // A4 size in points
  const { width, height } = page.getSize()
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica)

  const drawText = (text: string, x: number, y: number, size = 12) => {
    if (y > 40) {
      page.drawText(text, { x, y, size, font, color: rgb(0, 0, 0) })
    }
  }

  drawText("LETTER OF POSITIVE EXAMINATION", width / 2 - 110, height - 40, 16)
  drawText(body.date ?? "N/A", width - 100, height - 70, 10)
  drawText(`From: Royal Government of Cambodia`, 30, height - 100)
  drawText(
    "[Name], Coordinator of the National Authority for Greenhouse Gas Emission Reduction Mechanisms",
    30,
    height - 140,
    10
  )
  drawText(
    `Letter of Positive Examination related to ${body.projectName ?? "N/A"}`,
    30,
    height - 170,
    12
  )
  page.drawText(
    `As the designated responsible entity of the Royal Government of Cambodia, the National Authority for ` +
      `Greenhouse Gas Emission Reduction Mechanisms hereby confirms that ${
        body.projectName ?? "N/A"
      }, registered as’, ` +
      `registered as ${body.projectID} in Cambodia’s National GHG ER Registry, is authorized to generate ` +
      `greenhouse gas emission reductions (GHG ERs) for international transfer under Article 6 of the Paris Agreement, ` +
      `as per the conditions specified in the corresponding Letter of Authorization ${body.projectID}.`,
    { x: 30, y: height - 200, size: 10, font, lineHeight: 14, maxWidth: 500 }
  )

  page.drawText(
    "The balance of GHG ERs generated from the underlying project that have been authorized, positively examined, and first transferred to date are shown in the table below.",
    { x: 30, y: height - 290, size: 10, font, lineHeight: 14, maxWidth: 500 }
  )

  // Draw Table
  const tableX = 30
  let tableY = height - 350
  const rowHeight = 20
  const col1Width = 400
  const col2Width = 120

  // Table Header
  page.drawRectangle({
    x: tableX,
    y: tableY,
    width: col1Width,
    height: rowHeight,
    color: rgb(0.8, 0.8, 0.8),
    borderWidth: 1
  })
  page.drawRectangle({
    x: tableX + col1Width,
    y: tableY,
    width: col2Width,
    height: rowHeight,
    color: rgb(0.8, 0.8, 0.8),
    borderWidth: 1
  })
  drawText("Information", tableX + 10, tableY + 5, 10)
  drawText("Volume (tCO2eq)", tableX + col1Width + 10, tableY + 5, 10) // Replaced "₂" with "2"

  // Table Rows
  tableY -= rowHeight
  const drawRow = (label: string, value?: number) => {
    if (tableY - rowHeight > 40) {
      page.drawRectangle({
        x: tableX,
        y: tableY,
        width: col1Width,
        height: rowHeight,
        color: rgb(1, 1, 1),
        borderWidth: 1
      })
      page.drawRectangle({
        x: tableX + col1Width,
        y: tableY,
        width: col2Width,
        height: rowHeight,
        color: rgb(1, 1, 1),
        borderWidth: 1
      })
      drawText(label, tableX + 10, tableY + 5, 10)
      drawText(
        value?.toString() ?? "N/A",
        tableX + col1Width + 10,
        tableY + 5,
        10
      )
      tableY -= rowHeight
    }
  }

  drawRow("Total volume of authorized GHG ERs", body.totalVolumeAuthorized)
  drawRow(
    "Total cumulative volume of GHG ERs that have received positive examination",
    body.totalVolumeReceviedPositiveExamination
  )
  drawRow(
    "Remaining volume eligible for positive examination",
    body.remainVolume
  )

  page.drawText(
    "The Letter of Positive Examination is signed by [Name], Coordinator of the National Authority for Greenhouse Gas Emission Reduction Mechanisms, Royal Government of Cambodia.",
    { x: 30, y: tableY - 25, size: 10, font, lineHeight: 14, maxWidth: 500 }
  )
  drawText("………………………………………", 30, tableY - 80)
  drawText("[Name]", 30, tableY - 120)
  drawText(
    "Coordinator of the National Authority for Greenhouse Gas Emission Reduction Mechanisms",
    30,
    tableY - 140,
    10
  )
  drawText("Issuing authority:", 30, tableY - 180, 10)
  drawText("National Council for Sustainable Development", 30, tableY - 200, 10)
  drawText(
    "3rd floor, Morodok Techo Building, Phnom Penh, Cambodia",
    30,
    tableY - 220,
    10
  )
  drawText("<EMAIL>", 30, tableY - 240, 10)

  // Generate the PDF
  const pdfBytes = await pdfDoc.save()

  // Return as a response for download
  return new Response(pdfBytes, {
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": 'attachment; filename="generated.pdf"'
    }
  })
}
