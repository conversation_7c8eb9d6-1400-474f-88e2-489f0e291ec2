import { ActionFunctionArgs } from "@remix-run/cloudflare"
import { Outlet, useLocation, useNavigate, useParams } from "@remix-run/react"
import { PDD_STEPS } from "~/enums/EPddStep"

const ProjectInformation = () => {
  const steps = [
    { label: "Identification", value: PDD_STEPS.IDENTIFICATION },
    { label: "Location & Timeline", value: PDD_STEPS.LOCATION_TIMELINE },
    { label: "Organization", value: PDD_STEPS.ORGANIZATION },
    { label: "Methodology", value: PDD_STEPS.METHOLOGY },
    { label: "Carbon Accounting", value: PDD_STEPS.CARBON_ACCOUNTING },
    { label: "Monitoring", value: PDD_STEPS.MONOTORING },
    {
      label: "Stakeholder Engagement",
      value: PDD_STEPS.STAKEHOLDER_ENGAGEMENT
    },
    {
      label: "Environmental & Social Safeguards",
      value: PDD_STEPS.ENVIRONMENTAL_AND_SOCIAL_SAFEGUARDS
    },

    { label: "Financial Information", value: PDD_STEPS.FINANCIAL_INFORMATION }
  ]

  const location = useLocation()
  const paramActiveStep = Number(location.pathname.split("/").pop())
  const activeStep = isNaN(paramActiveStep)
    ? PDD_STEPS.IDENTIFICATION
    : steps[paramActiveStep - 1].value

  return (
    <ProgressStepper steps={steps} activeStep={activeStep}>
      <div className="w-full ">
        <Outlet />
      </div>
    </ProgressStepper>
  )
}

type ProgressStep = {
  label: string
  value: string
}

type ProgressStepperProps = {
  steps: ProgressStep[]
  activeStep: string
  children?: React.ReactNode
}

const ProgressStepper = ({
  steps,
  activeStep,
  children
}: ProgressStepperProps) => {
  const activeStepIndex = steps.findIndex(item => item.value === activeStep)

  return (
    <div className="flex flex-col gap-1  my-6">
      {activeStepIndex > 0 && (
        <ProgressStepSection
          step={steps[activeStepIndex - 1]}
          steps={steps}
          variant="completed"
        />
      )}

      <ProgressStepSection
        step={steps[activeStepIndex]}
        steps={steps}
        variant="active"
        children={children}
      />

      {activeStepIndex < steps.length - 2 && (
        <ProgressStepSection
          step={steps[activeStepIndex + 1] || steps[activeStepIndex]}
          steps={steps}
          variant="inactive"
        />
      )}
      {activeStepIndex < steps.length - 1 && (
        <ProgressStepSection
          step={steps[steps.length - 1]}
          steps={steps}
          variant="inactive"
        />
      )}
    </div>
  )
}

type ProgressStepSectionProps = {
  steps: ProgressStep[]
  step: ProgressStep
  children?: React.ReactNode
  variant: "active" | "inactive" | "completed"
}

const ProgressStepSection = ({
  steps,
  step,
  variant,
  children
}: ProgressStepSectionProps) => {
  const stepIndex = steps.findIndex(item => item.value === step.value)
  const isLastStep = stepIndex === steps.length - 1

  const navigate = useNavigate()
  const params = useParams()

  let titleRender = null
  if (variant === "active") {
    titleRender = (
      <div className="flex justify-between items-center">
        <p className="font-bold">
          {stepIndex + 1}. {step.label}
        </p>
        <p className="text-sm text-primary">
          STEP {stepIndex + 1} OF {steps.length}
        </p>
      </div>
    )
  } else if (variant === "inactive") {
    titleRender = (
      <div className="opacity-30">
        <p className="font-bold">
          {stepIndex + 1}. {step.label}
        </p>
      </div>
    )
  } else if (variant === "completed") {
    titleRender = (
      <div
        className="flex justify-between items-center cursor-pointer"
        onClick={() => {
          navigate(
            `/user/projects/${params.id}/pdds/${
              params.pid
            }/project-information/${stepIndex + 1}`
          )
        }}>
        <p className="font-bold text-primary">
          {stepIndex + 1}. {step.label}
        </p>
        <p className="text-sm text-primary">Previous</p>
      </div>
    )
  }

  let contentRender = null
  if (variant === "active") {
    contentRender = (
      <div className="relative ml-[50px]">
        <div className="py-4">{children}</div>
        <div className="absolute top-0 left-0 ml-[-50px] h-full w-[19px] flex flex-col items-center">
          <div className="h-full w-1 bg-black/20" />
        </div>
      </div>
    )
  } else if (variant === "inactive" && !isLastStep) {
    contentRender = (
      <div className="relative ml-[50px]">
        <div className="py-3.5">
          <div className="h-[18px] bg-black/10 blur-sm w-[900px]"></div>
        </div>
        <div className="absolute top-0 left-0 ml-[-50px] h-full w-[19px] flex flex-col items-center">
          <div className="h-full w-1 border-l-4 border-dotted border-black/20" />
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="relative ml-[50px] w-[900px]">
        <div
          className={`${
            variant === "completed" ? "" : isLastStep ? "border-t" : "border-y"
          } border-black/20 py-2`}>
          {titleRender}
        </div>
        <div className="absolute top-0 left-0 ml-[-50px] h-[42px] flex items-center">
          <ProgressCircle variant={variant} />
        </div>
      </div>

      {contentRender}
    </div>
  )
}

type ProgressCircleProps = {
  variant?: "placeholder" | "active" | "inactive" | "completed"
}

const ProgressCircle = ({ variant }: ProgressCircleProps) => {
  if (variant === "completed") {
    return <div className="bg-black/20 size-4 rounded-full" />
  }

  if (variant === "active") {
    return (
      <div className="bg-white rounded-full border-[2.5px] border-black/80 p-[2px]">
        <div className="bg-primary rounded-full size-2.5" />
      </div>
    )
  }

  if (variant === "inactive") {
    return (
      <div className="rounded-full border-[2.5px] border-black/20 p-[2px]">
        <div className="rounded-full size-2.5" />
      </div>
    )
  }

  return <div className="bg-black/20 size-2.5 rounded-full" />
}

export default ProjectInformation
