import { <PERSON><PERSON> } from "@heroui/react"
import { json, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Link, useLoaderData, useNavigate } from "@remix-run/react"
import {
  BeanOff,
  Factory,
  Footprints,
  LifeBuoy,
  PartyPopper,
  PlusIcon,
  type LucideIcon
} from "lucide-react"
import { Trans, useTranslation } from "react-i18next"
import ActivitiesCard from "~/components/overView/ActivitiesCard"
import ApprovalRate from "~/components/overView/ApprovalRate"
import CarbonEmissionReduced from "~/components/overView/CarbonEmissionReduced"
import CarbonOverView from "~/components/overView/CarbonOverView"
import ProjectOverView from "~/components/overView/ProjectOverView"
import TaskCard from "~/components/overView/TaskCard"
import { getAuthenticator } from "~/services/auth"
import OverviewService from "~/services/db/overview"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const { authenticator } = getAuthenticator(
    context.cloudflare.env as CloudflareENV
  )
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: "/login"
  })
  const env = context.cloudflare.env as CloudflareENV
  const projectsOverview = await new OverviewService(env.DB).getProjectOverview(
    {
      user_id: user.id
    }
  )
  const carbonCreditOverview = await new OverviewService(
    env.DB
  ).getCarbonCreditOverview({
    user_id: user.id
  })

  return json({ user, projectsOverview, carbonCreditOverview })
}

export default function UserDashboard() {
  const { t } = useTranslation()
  const { user, projectsOverview, carbonCreditOverview } =
    useLoaderData<typeof loader>()

  const overview = [
    {
      Icon: PlusIcon,
      title: "Register New Project",
      description: "Begin the process of registering your first GHG ER project",
      link: "/user/projects/new"
    },
    {
      Icon: Factory,
      title: "Complete your Profile",
      description: "Update your organization details and contact information",
      link: "/profile"
    },
    {
      Icon: Footprints,
      title: "Learn About the Process",
      description: "View guidelines and requirements for project registration",
      link: "/resources"
    },
    {
      Icon: LifeBuoy,
      title: "Contact Support",
      description: "Get help from the registry administrators",
      link: "/about"
    }
  ]

  const navigate = useNavigate()

  return (
    <>
      {projectsOverview?.totalProject > 0 ? (
        <>
          <div className="flex justify-between items-center">
            <p>
              <Trans
                i18nKey="common.welcome_user"
                components={{ b: <b /> }}
                values={{ username: user.firstName }}
              />
            </p>
            <Button as={Link} href="/user/projects/new" color="primary">
              {t("project.create_new_project")}
            </Button>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <ProjectOverView projectsOverview={projectsOverview} />
            <CarbonOverView
              carbonCreditOverview={carbonCreditOverview}
              totalExpectCarbon={projectsOverview.totalExpectCarbon}
            />
            <ApprovalRate />
            <CarbonEmissionReduced
              carbonEmissionReduced={
                Number(carbonCreditOverview.totalIssuedCarbon) +
                Number(carbonCreditOverview.totalRetiredCarbon)
              }
            />
            <TaskCard />
            <ActivitiesCard />
          </div>
        </>
      ) : (
        <>
          <div className="grid gap-y-6">
            <div className="flex gap-x-2 bg-white p-6 rounded-lg">
              <div className="flex-1">
                <div className="grid gap-y-3">
                  <div>
                    <p className="text-[20px]">
                      Welcome to the{" "}
                      <span className="font-bold">
                        Cambodia Carbon Registry!
                      </span>
                    </p>
                  </div>
                  <div>
                    <p>
                      As a project proponent, you can register and manage GHG ER
                      projects, track carbon credits, and generate reports.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <PartyPopper className="w-12 h-12 text-gray-400" />
              </div>
            </div>
            <div className="grid gap-y-3">
              <div>
                <p className="font-bold text-[20px]">Getting Started</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-12">
                {overview.map(
                  (
                    item: {
                      Icon: LucideIcon
                      title: string
                      description: string
                      link: string
                    },
                    index: number
                  ) => {
                    return (
                      <Link
                        to={item?.link}
                        className={
                          index === 0
                            ? "bg-primary shadow-md border rounded-2xl p-4 grid grid-cols-1 h-[450px] cursor-pointer hover:bg-green-500"
                            : "bg-white shadow-md border rounded-2xl p-4 grid grid-cols-1 h-[450px] cursor-pointer hover:bg-gray-300"
                        }
                        key={item.title.length}
                        onClick={() => navigate(item?.link)}>
                        <div className="justify-center flex items-center">
                          <item.Icon
                            className={
                              index === 0
                                ? "size-20 text-white"
                                : "size-20 text-gray-400"
                            }
                          />
                        </div>
                        <div className="justify-center text-center px-10 ">
                          <p
                            className={`font-bold text-[18px] ${
                              index === 0 ? "text-white" : "text-black"
                            } my-4`}>
                            {item.title}
                          </p>
                          <p
                            className={`${
                              index === 0 ? "text-white" : "text-gray-600"
                            }`}>
                            {item?.description}
                          </p>
                        </div>
                      </Link>
                    )
                  }
                )}
              </div>
            </div>
            <div>
              <div className="bg-[#E4E4E7] shadow-md border rounded-2xl py-14 text-center grid gap-y-5">
                <div className="flex justify-center">
                  <BeanOff className="w-24 h-24 text-gray-400" />
                </div>
                <div className="text-[#52525B] text-[20px]">
                  <p>You have no active projects</p>
                  <p>
                    Click on the&nbsp;
                    <Link
                      to="/user/projects/new"
                      className="font-bold cursor-pointer">
                      Register New Project
                    </Link>{" "}
                    to begin
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}
