import { Button, Image } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { Send } from "lucide-react"
import { useTranslation } from "react-i18next"
import Logo from "~/components/logo"
import Title from "~/components/title"

const ForgotPasswordSuccess = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()
  return (
    <div className="grid xs:grid-cols-1 md:grid-cols-2 h-screen">
      <div className="container max-w-3xl mx-auto py-8 md:py-12 relative box-border">
        <div className="px-5 md:px-10 lg:px-20">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <Logo href="/" />
            </div>
            <div>
              <Title title="Recovery" />
            </div>
          </div>
        </div>
        <div className="absolute top-1/2 transform -translate-y-1/2 px-5 md:px-10 lg:px-20">
          <div className="grid grid-cols-1 gap-4">
            <div className="flex justify-center">
              <Send width="100px" height="100px" />
            </div>
            <div className="flex justify-center">
              <p className="text-[25px]">Check Your Email</p>
            </div>
            <div>
              <p className="text-center">
                We've sent instructions to reset your password to
                <EMAIL>
              </p>
              <p className="px-16 text-center mt-4">
                Please check your inbox and follow the link in the email to
                create a new password
              </p>
            </div>
            <div>
              <p className="text-gray-500 text-center">
                The link will expire in 30 minutes
              </p>
            </div>
            <div className="flex justify-center">
              <Button
                size="lg"
                color="primary"
                className="px-16"
                onPress={() => navigate("/login")}>
                Return to login
              </Button>
            </div>
            <div className="flex justify-center">
              <p className="text-center">
                Didn't receive the email?
                <br />
                <span
                  className="text-blue-600 cursor-pointer"
                  onClick={() => {
                    alert("resent password")
                  }}>
                  Resend
                </span>
              </p>
            </div>
            <div>
              <p className="text-center">
                Remember to also check your spam or junk folder
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="hidden md:block">
        <Image
          radius="none"
          src="/login-image.jpeg"
          classNames={{
            wrapper: "w-full h-full",
            img: "object-cover w-full h-full"
          }}
        />
      </div>
    </div>
  )
}

export default ForgotPasswordSuccess
