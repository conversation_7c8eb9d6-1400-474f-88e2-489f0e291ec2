import { Button } from "@heroui/react"
import {
  ActionFunctionArgs,
  defer,
  json,
  LoaderFunctionArgs,
  MetaFunction,
  redirect
} from "@remix-run/cloudflare"
import { Await, useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { X } from "lucide-react"
import { Suspense, useState } from "react"
import Loading from "~/components/loading"
import { EStatus } from "~/enums/EStatus"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"

export const handle = {
  title: "Users",
  backable: true
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const user = new UserService(env.DB).getById(parseInt(id))
  return defer({ id, user })
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()
  const userId = Number(data.get("id"))
  const status = data.get("status") as EStatus
  const active = data.get("active") === "true" ? true : false
  const user = await new UserService(env.DB).update(userId, { status, active })
  if (user) {
    const sent = await new MailService(
      env
    ).UserAccountHasBeenApproveRejectEmailNotification(user?.email, active)
    if (sent.error) return null
    return redirect("/admin/users")
  }
  return json({ error: true })
}

export default function ViewUser() {
  const { id, user } = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [status, SetStatus] = useState<string | null>()

  async function handleStatusUpdate(status: string) {
    setLoading(true)
    setError(null)
    SetStatus(status)

    try {
      const formData = new FormData()
      formData.append("id", id)
      formData.append("status", status)
      formData.append("active", status === EStatus.Approve ? "true" : "false")
      await fetch(`/admin/users/${id}`, {
        method: "POST",
        body: formData
      })
      navigate("/admin/users")
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <div
        className={[
          "h-16",
          "flex",
          "justify-between",
          "items-center",
          "border-b",
          "border-b-base-200",
          "pb-4",
          "mb-10"
        ].join(" ")}>
        <h2 className="font-bold text-lg">User ID: {id}</h2>
        <a
          href="/admin/users"
          title="Back"
          className="btn btn-square btn-ghost">
          <X width={24} />
        </a>
      </div>
      <Suspense fallback={<Loading />}>
        <Await resolve={user}>
          {user => (
            <div className="max-w-lg mx-auto">
              {user ? (
                <>
                  <table className="table table-auto">
                    <thead className="bg-base-100 uppercase">
                      <tr>
                        <th>Name</th>
                        <th>Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>ID</td>
                        <td>{user.id}</td>
                      </tr>
                      <tr>
                        <td>Email</td>
                        <td>{user.email}</td>
                      </tr>
                      <tr>
                        <td>First Name</td>
                        <td>{user.firstName}</td>
                      </tr>
                      <tr>
                        <td>Last Name</td>
                        <td>{user.lastName}</td>
                      </tr>
                      <tr>
                        <td>Status</td>
                        <td>{user.status}</td>
                      </tr>
                      <tr>
                        <td>Created</td>
                        <td>
                          <>
                            {dayjs(user.created_at).format("YYYY-MM-DD hh:mm")}
                          </>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div className="mt-5 flex justify-end space-x-2">
                    {(user.status === EStatus.Approve ||
                      user.status === EStatus.Review) && (
                      <Button
                        isDisabled={loading}
                        isLoading={status === EStatus.Reject && loading}
                        className="bg-danger"
                        onPress={() => handleStatusUpdate(EStatus.Reject)}>
                        Reject
                      </Button>
                    )}
                    {(user.status === EStatus.Reject ||
                      user.status === EStatus.Review) && (
                      <Button
                        isDisabled={loading}
                        isLoading={status === EStatus.Approve && loading}
                        className="bg-primary"
                        onPress={() => handleStatusUpdate(EStatus.Approve)}>
                        Approve
                      </Button>
                    )}

                    {/* <Button
                      isDisabled={loading}
                      isLoading={status === EStatus.Approve && loading}
                      className="bg-primary"
                      onPress={() => handleStatusUpdate(EStatus.Approve)}>
                      Approve
                    </Button> */}
                  </div>
                </>
              ) : (
                <div className="alert alert-error">User not found.</div>
              )}
            </div>
          )}
        </Await>
      </Suspense>
    </>
  )
}
