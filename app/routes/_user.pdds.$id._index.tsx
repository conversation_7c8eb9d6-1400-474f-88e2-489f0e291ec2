import { json, type LoaderFunctionArgs, redirect } from "@remix-run/cloudflare"
import { useLoaderData } from "@remix-run/react"
import Forehead from "~/components/layout/admin/forehead"
import PDDDetail from "~/components/pdd"
import { EStatus } from "~/enums/EStatus"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id

  if (!id) throw new Response("Not found", { status: 404 })

  const env = context.cloudflare.env as CloudflareENV
  const pdd = await new PDDService(env.DB).getById(parseInt(id))
  const user = await getAuthUser(context, request)
  return json({ pdd, user })
}

export async function action({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()

  const actionType = data.get("action")
  if (actionType === "submit-pdd") {
    await new PDDService(env.DB).update(parseInt(id), {
      status: EStatus.Pending
    })
    return redirect(`/pdds/${id}`)
  }

  return json({ success: true })
}

const PDD = () => {
  const { pdd, user } = useLoaderData<typeof loader>()
  return (
    <div>
      <Forehead
        title="PDD Detail"
        backref={`/user/projects/${pdd?.project_id}`}
      />
      <PDDDetail pdd={pdd} user={user} />
    </div>
  )
}

export default PDD
