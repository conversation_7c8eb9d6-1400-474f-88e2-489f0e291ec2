import { But<PERSON> } from "@heroui/react"
import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Form, useLoaderData, useNavigate, useParams } from "@remix-run/react"
import CoverImageInfo from "~/components/projectRegistration/CoverImageInfo"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import ProjectDescriptionInfo from "~/components/projectRegistration/ProjectDescriptionInfo"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import ProjectSectorService from "~/services/db/projectService"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const env = context.cloudflare.env as CloudflareENV
  const isNew = params.pid === "new"
  const action = data?.action

  if (isNew) {
    const result = await new PDDService(env.DB).create({
      project_id: params.id
    })
    await new PDDService(env.DB).update(result[0].id, {
      step: `/user/projects/${params.id}/pdds/${result[0].id}/project-information/1`
    })
    if (action === "save-and-continue") {
      return redirect(
        `/user/projects/${params.id}/pdds/${result[0].id}/project-information/2`
      )
    } else {
      return redirect(`/pdds/${result[0].id}`)
    }
  } else {
    if (action === "save-and-continue") {
      return redirect(
        `/user/projects/${params.id}/pdds/${params.pid}/project-information/2`
      )
    } else {
      return redirect(`/pdds/${params.pid}`)
    }
  }
}

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = await new ProjectService(env.DB).getById(parseInt(id))
  const sectors = await new ProjectSectorService(env.DB).getMany()
  return json({ project, sectors })
}

const ProjectInformation = () => {
  const { project, sectors } = useLoaderData<typeof loader>()
  const navigate = useNavigate()
  const params = useParams()
  const isNew = params.pid === "new"

  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid w-full gap-y-3">
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <div className="grid grid-cols-2 gap-x-3">
                <div className="">
                  <Select
                    label="Sector"
                    isRequired
                    isDisabled
                    placeholder="Select Project Sector"
                    data={sectors.map(sector => ({
                      key: sector.id.toString(),
                      label: sector.name
                    }))}
                    defaultSelectedKeys={[project?.sector_id?.toString()]}
                    name="sector_id"
                  />
                </div>
                <div></div>
              </div>
            </div>
            <div className="flex-1">
              <NoteInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px]">
          <Input
            label="Project Name"
            isDisabled
            defaultValue={project?.name ?? ""}
            isRequired
            placeholder="Enter Project Name"
            name="name"
          />
        </div>
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <TextArea
                isDisabled
                defaultValue={project?.description ?? ""}
                label="Description"
                placeholder="Enter project description"
                name="description"
                minRows={5}
              />
            </div>
            <div className="flex-1">
              <ProjectDescriptionInfo />
            </div>
          </div>
        </div>
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <FileSelect
                label="Cover Image"
                name="cover_img"
                fileNameSelected={project?.cover_img ?? null}
                acceptFileTypes=".jpg, .jpeg, .png ,.webp"
              />
            </div>
            <div className="flex-1">
              <CoverImageInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px] flex gap-x-3">
          <Button
            className="bg-primary text-white"
            name="action"
            value="save-and-continue"
            type="submit">
            Save & Continue
          </Button>
          <Button
            variant="bordered"
            color="primary"
            name="action"
            value="save"
            type="submit">
            Save
          </Button>
          <Button
            className="bg-transparent"
            onPress={() => {
              isNew
                ? navigate(`/user/projects/${project?.id}`)
                : navigate(`/pdds/${params.pid}`)
            }}>
            Cancel
          </Button>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
