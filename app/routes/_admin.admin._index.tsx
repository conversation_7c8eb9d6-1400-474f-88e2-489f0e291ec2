import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare"
import { useLoaderData } from "@remix-run/react"
import { Trans } from "react-i18next"
import ProjectOverView from "~/components/overView/ProjectOverView"
import { getAuthenticator } from "~/services/auth"
import OverviewService from "~/services/db/overview"
import type { CloudflareENV } from "~/types"

export const handle = {
  title: "Dashboard"
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const { authenticator } = getAuthenticator(
    context.cloudflare.env as CloudflareENV
  )
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: "/login"
  })
  const env = context.cloudflare.env as CloudflareENV
  const projectsOverview = await new OverviewService(env.DB).getProjectOverview(
    {
      user: user
    }
  )
  return { user, projectsOverview }
}

export default function AdminIndex() {
  const { user, projectsOverview } = useLoaderData<typeof loader>()

  return (
    <>
      <div className="flex justify-between items-center">
        <p>
          <Trans
            i18nKey="common.welcome_user"
            components={{ b: <b /> }}
            values={{ username: user.firstName }}
          />
        </p>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <ProjectOverView projectsOverview={projectsOverview} />
      </div>
    </>
  )
}
