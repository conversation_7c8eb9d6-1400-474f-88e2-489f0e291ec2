import { defer, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import { Suspense } from "react"
import ImplementationDetail from "~/components/implementation"
import Forehead from "~/components/layout/admin/forehead"

import Loading from "~/components/loading"
import ImplementationService from "~/services/db/implementation"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const implementation = new ImplementationService(env.DB).getById(parseInt(id))
  return defer({ implementation })
}

const Implementation = () => {
  const { implementation } = useLoaderData<typeof loader>()
  return (
    <>
      <Suspense fallback={<Loading />}>
        <Await resolve={implementation}>
          {implementation => (
            <>
              <Forehead
                title="Implementation Detail"
                backref={`/user/projects/${implementation?.project_id}`}
              />
              <ImplementationDetail
                linkToProject={`user/projects/${implementation?.project_id}`}
                implementation={implementation}></ImplementationDetail>
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}

export default Implementation
