import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react"
import { j<PERSON>, LoaderFunctionArgs } from "@remix-run/cloudflare"
import { useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { and, eq, ne } from "drizzle-orm"
import { Search } from "lucide-react"
import { useRef } from "react"
import DataTable, { createDataTableRow } from "~/components/table/data-table"
import { filterSearch, getListQuery } from "~/components/table/query"
import useListQuery from "~/components/table/use-list-query"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import { projects } from "~/db/schema/projects"
import { EProjectStatus } from "~/enums/EProjectStatus"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import projectSectors from "~/utils/projectSectors"
import type { ProjectFilter } from "./_admin.admin.projects._index"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV

  const { page, limit, search, sort, sector, status } =
    getListQuery<ProjectFilter>(request)

  const filter = and(
    filterSearch(search, [projects.id, projects.name]),
    ne(projects.status, "draft"),
    status ? eq(projects.status, status) : undefined,
    sector ? eq(projects.sector_id, Number(sector)) : undefined
  )

  const projectService = new ProjectService(env.DB)

  const { total, result } = await projectService.paginateTable({
    filter,
    page,
    limit,
    sort
  })
  return json({ result, total })
}

export default function Projects() {
  const { result, total } = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  const rows = createDataTableRow<typeof result>([
    {
      key: "id",
      headerLabel: "project ID"
    },
    {
      key: "name",
      headerLabel: "Project Title",
      allowSorting: true
    },
    {
      key: "sector",
      headerLabel: "Project Sector",
      renderCell: row => <div>{row.sector?.name}</div>
    },
    {
      key: "created_at",
      headerLabel: "Registration Date",
      renderCell: row => (
        <div>{dayjs(row.created_at).format("DD MMM YYYY")}</div>
      ),
      allowSorting: true
    },
    {
      key: "project_period",
      headerLabel: "Project Period",
      renderCell: row => (
        <div>
          {dayjs(row.ideaNote?.start_date).format("DD MMM YYYY")}&nbsp;-&nbsp;
          {dayjs(row.ideaNote?.end_date).format("DD MMM YYYY")}
        </div>
      )
    },
    {
      key: "status",
      headerLabel: "Status",
      renderCell: row => (
        <div
          className={`capitalize px-2 py-1 rounded-full w-fit  min-w-44 text-end
              ${
                row.status === "rejected"
                  ? "text-red-500 font-bold"
                  : "text-green-500 font-bold"
              }`}>
          {row.status}
        </div>
      )
    }
  ])

  const searchRef = useRef<HTMLInputElement>(null)

  const { query, updateQuery, sortDescriptor, handleSortChange } =
    useListQuery<ProjectFilter>()

  const handleSearch = () => {
    if (searchRef.current) {
      updateQuery({
        search: searchRef.current.value ?? "",
        page: 1
      })
    }
  }

  return (
    <div className="container mx-auto space-y-4 p-5 mg:p-10">
      <>
        <div className="grid grid-cols-1 md:flex space-x-0 md:space-x-3 justify-end mb-5 space-y-2 md:space-y-0">
          <div className="md:w-60">
            <Select
              onChange={e => {
                updateQuery({
                  status: e.target.value as EProjectStatus,
                  page: 1
                })
              }}
              placeholder="Status"
              selectedKeys={[query.status]}
              data={Object.values(EProjectStatus)
                .filter(item => item !== EProjectStatus?.Draft) // Exclude "STAFT_DRAF"
                .map(item => ({
                  value: item,
                  label: item,
                  key: item
                }))}
            />
          </div>
          <div className="md:w-60">
            <Select
              onChange={e => {
                updateQuery({
                  sector: e.target.value as EProjectStatus,
                  page: 1
                })
              }}
              placeholder="Project Sector"
              selectedKeys={[query.sector].toString()}
              data={projectSectors.map(item => ({
                value: item.name,
                label: item.name,
                key: item.key
              }))}
            />
          </div>
          <div>
            <div className="space-x-3 flex items-center">
              <Input
                ref={searchRef}
                defaultValue={query.search as string}
                placeholder="Search ..."
                onKeyDown={e => {
                  if (e.key === "Enter") {
                    handleSearch()
                  }
                }}
                endContent={
                  <div>
                    <Button
                      variant="flat"
                      className="bg-transparent hover:bg-transparent"
                      isIconOnly
                      onPress={handleSearch}>
                      <Search
                        onClick={() => {
                          updateQuery({
                            search: searchRef.current?.value ?? "",
                            page: 1
                          })
                        }}
                      />
                    </Button>
                  </div>
                }></Input>
            </div>
          </div>
        </div>
        <DataTable
          data={result}
          rows={rows}
          onSortChange={handleSortChange}
          sortDescriptor={sortDescriptor}
          onRowClick={id => {
            navigate(`/projects/${id}`)
          }}
        />
        {total > 0 && (
          <div className="flex justify-center">
            <Pagination
              showControls
              page={Number(query.page)}
              total={Math.ceil(total / Number(query.limit))}
              onChange={page => {
                updateQuery({ page })
              }}
            />
          </div>
        )}
      </>
    </div>
  )
}
