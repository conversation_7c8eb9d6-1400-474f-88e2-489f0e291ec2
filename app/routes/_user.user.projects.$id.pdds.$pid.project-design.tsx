import { Accordion, Accordion<PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  Link,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import { FileUp, MinusIcon, PlusIcon } from "lucide-react"
import { useState } from "react"
import { PDD_STEPS } from "~/enums/EPddStep"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const env = context.cloudflare.env as CloudflareENV
  const action = data?.action
  await new PDDService(env.DB).update(parseInt(params?.pid), {
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-design`
  })
  if (action === "submit-request") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/loa-request`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectDesign = () => {
  const params = useParams()
  const { pdd, project } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  const steps = [
    {
      label: "Identification",
      value: PDD_STEPS.IDENTIFICATION,
      fields: [
        {
          label: "Sector",
          value: project?.sector?.name
        },
        {
          label: "Project Name",
          value: project?.name
        },
        {
          label: "Description",
          value: project?.description
        },
        {
          label: "project Cover",
          value: <span className="text-primary">{project?.cover_img}</span>
        }
      ]
    },
    {
      label: "Location & Timeline",
      value: PDD_STEPS.LOCATION_TIMELINE,
      fields: [
        // {
        //   label: "City/Province",
        //   value: "Kompong Thom"
        // },
        {
          label: "Address",
          value: pdd?.address
        },
        {
          label: "Latitude",
          value: pdd?.lat
        },
        {
          label: "Longitude",
          value: pdd?.lng
        },
        {
          label: "Area Map",
          value: <span className="text-primary">{pdd?.kml_file}</span>
        },
        {
          label: "Expected Start Date",
          value: pdd?.start_date
        },
        {
          label: "Expected Completion Date",
          value: pdd?.end_date
        }
      ]
    },
    {
      label: "Organization",
      value: PDD_STEPS.ORGANIZATION,
      fields: [
        {
          label: "Organization Name",
          value: "Organization Name Value"
        },
        {
          label: "Organization Type",
          value: "Organization Type Value"
        },
        {
          label: "Address",
          value: "address value"
        },
        {
          label: "Province",
          value: "Kompong Thom"
        },
        {
          label: "Contact Person Name",
          value: "contact person name"
        },
        {
          label: "Contact Email",
          value: "<EMAIL>"
        },
        {
          label: "Contact Number",
          value: "0975064555"
        },
        {
          label: "Website",
          value: "www.test.com"
        }
      ]
    },
    {
      label: "Methodology",
      value: PDD_STEPS.METHOLOGY,
      fields: [
        {
          label: "Methodology Name",
          value: pdd?.methodology_name
        },
        {
          label: "Version",
          value: "001"
        },
        {
          label: "Baseline Scenario",
          value: pdd?.baseline_scenario
        },
        {
          label: "Project Scenario",
          value: pdd?.project_scenario
        }
      ]
    },
    {
      label: "Carbon Accounting",
      value: PDD_STEPS.CARBON_ACCOUNTING,
      fields: [
        {
          label: "Baseline Emission Factor",
          value: pdd?.baseline_emission_factor
        },
        {
          label: "Annual Emission Reductions",
          value: pdd?.annual_emission_reduction
        },
        {
          label: "Lifetime Emission Reductions",
          value: pdd?.lifetime_emission_reduction
        },
        {
          label: "Leakage Assessment",
          value: pdd?.leakage_assessment
        },
        {
          label: "Additionally Demonstration",
          value: (
            <span className="text-primary">
              {pdd?.additionality_assessment_file}
            </span>
          )
        },
        {
          label: "Baseline Emission Methodology",
          value: (
            <span className="text-primary">
              {pdd?.baseline_emission_methodology_file}
            </span>
          )
        },
        {
          label: "Baseline Emission Data",
          value: (
            <span className="text-primary">
              {pdd?.baseline_emission_data_file}
            </span>
          )
        },
        {
          label: "Carbon Stock Assessment",
          value: (
            <span className="text-primary">
              {pdd?.carbon_stock_assessment_file}
            </span>
          )
        },
        {
          label: "Leakage Assessment",
          value: (
            <span className="text-primary">{pdd?.leakage_assessment_file}</span>
          )
        },
        {
          label: "Additionality Assessment",
          value: (
            <span className="text-primary">
              {pdd?.additionality_assessment_file}
            </span>
          )
        }
      ]
    },
    {
      label: "Monitoring",
      value: PDD_STEPS.MONOTORING,
      fields: [
        {
          label: "Monitoring Plan",
          value: pdd?.monitoring_plan
        },
        {
          label: "Monitoring Frequency",
          value: pdd?.monitoring_frequency
        },
        {
          label: "Tool/Method",
          value: pdd?.tool_method
        },
        {
          label: "Detailed Monitoring Plan",
          value: (
            <span className="text-primary">
              {pdd?.detailed_monitoring_plan_file}
            </span>
          )
        }
      ]
    },
    {
      label: "Stakeholder Engagement",
      value: PDD_STEPS.STAKEHOLDER_ENGAGEMENT,
      fields: [
        {
          label: "Community Involvement",
          value: pdd?.communtiy_consultation_records_file
        },
        {
          label: "Stakeholder Consultations",
          value: pdd?.stakeholder_consultations
        },
        {
          label: "Community Consultation Records",
          value: (
            <span className="text-primary">
              {pdd?.community_consultation_records_file}
            </span>
          )
        }
      ]
    },
    {
      label: "Environmental and Social Safeguards",
      value: PDD_STEPS.ENVIRONMENTAL_AND_SOCIAL_SAFEGUARDS,
      fields: [
        {
          label: "Environmental Impact Assessment (EIA)",
          value: pdd?.environmental_impact_assessment
        },
        {
          label: "Social Impact Assessment (SIA)",
          value: pdd?.social_impact_assessment
        },
        {
          label: "Risk Mitigation Measures",
          value: pdd?.risk_mitigation_measures
        }
      ]
    },
    {
      label: "Financial Information",
      value: PDD_STEPS.FINANCIAL_INFORMATION,
      fields: [
        {
          label: "Project Development Cost",
          value: pdd?.project_development_cost
        },
        {
          label: "Operational Cost (USD/year)",
          value: pdd?.operational_cost
        },
        {
          label: "Expected Revenue from Carbon Credits (USD/year)",
          value: pdd?.expected_revenue
        },
        {
          label: "Funding Source",
          value: pdd?.funding_source
        }
      ]
    }
  ]

  const [activeStep, setActiveStep] = useState<string>("")

  const handleAccordionChange = (key: string) => {
    setActiveStep(key)
  }

  const navigate = useNavigate()

  return (
    <div className="pt-4">
      <div className="flex justify-between">
        <div>
          <p>Summary</p>
        </div>
        <div>
          <Button
            className="bg-transparent"
            startContent={<FileUp color="gray" />}>
            <p className="text-primary">Export</p>
          </Button>
        </div>
      </div>
      <div className="container max-w-3xl mx-auto space-y-8 py-8">
        <Accordion selectedKeys={[activeStep]}>
          {steps.map((step, index) => {
            const isActive = activeStep === step.value
            return (
              <AccordionItem
                onPress={() => {
                  handleAccordionChange(step.value)
                }}
                indicator={({ isOpen }) =>
                  isOpen ? <MinusIcon size={18} /> : <PlusIcon size={18} />
                }
                key={step.value}
                title={
                  <div className="flex">
                    <p>{`${index + 1}. ${step.label}`}</p>
                    {isActive && (
                      <span className="text-sm font-normal ml-12">
                        <Link
                          to={`/user/projects/${params.id}/pdds/${
                            params.pid
                          }/project-information/${index + 1}`}
                          className="text-primary">
                          EDIT
                        </Link>
                      </span>
                    )}
                  </div>
                }
                classNames={{
                  indicator: "data-[open=true]:-rotate-0",
                  title: `data-[open=true]:font-bold text-gray-400 data-[open=true]:text-black px-2`,
                  content: `p-6 bg-white border-t border-gray-300 `
                }}>
                <div className="grid grid-cols-[1fr_2fr] grid-tem">
                  {step.fields?.map((field, index) => {
                    const isLast = step.fields.length - 1 === index
                    return (
                      <>
                        <p
                          className={`${
                            !isLast ? "border-b" : ""
                          } p-2 text-gray-400`}>
                          {field.label}
                        </p>
                        <div className={`${!isLast ? "border-b" : ""} p-2`}>
                          {field.value}
                        </div>
                      </>
                    )
                  })}
                </div>
              </AccordionItem>
            )
          })}
        </Accordion>
        <Form method="POST">
          <div className="flex justify-center gap-x-2">
            <Button
              color="primary"
              name="action"
              value="submit-request"
              type="submit">
              Submit Request for LOA
            </Button>
            <Button
              className="bg-transparent"
              name="action"
              value="cancel"
              type="submit">
              Cancel
            </Button>
          </div>
        </Form>
      </div>
    </div>
  )
}

export default ProjectDesign
