import { Button, CalendarDate, DateValue } from "@heroui/react"
import { parseDate } from "@internationalized/date"
import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import {
  Form,
  useLoaderData,
  useNavigate,
  useNavigation,
  useRouteLoaderData
} from "@remix-run/react"
import { useEffect, useState } from "react"
import AddressInfo from "~/components/projectRegistration/AddressInfor"
import CoverImageInfo from "~/components/projectRegistration/CoverImageInfo"
import NameInfo from "~/components/projectRegistration/NameInfo"
import ProjectDescriptionInfo from "~/components/projectRegistration/ProjectDescriptionInfo"
import SectorInfo from "~/components/projectRegistration/SectorInfo"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { Project_STEPS } from "~/enums/EProjectStep"
import { getKey } from "~/helpers/r2"
import { provinces } from "~/lib/location"
import IdeaNoteService from "~/services/db/ideaNote"
import OrganizationService from "~/services/db/organization"
import ProjectService from "~/services/db/project"
import ProjectSectorService from "~/services/db/projectService"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"
import { ProjectValidator } from "~/validators/project"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const isNew = params.id === "new"
  const payload = await request.formData()
  const validator = new ProjectValidator(env.DB)
  const { data, error } = await validator.validateCreate(payload)
  if (error) {
    return { error: true }
  }
  if (data) {
    const action = data?.action
    if (isNew) {
      const newProjectData = {
        ...data,
        cover_img: "",
        kml_file: "",
        user_id: user.id,
        project_step: Project_STEPS.PROJECT_INFORMATION,
        status: EProjectStatus.Draft
      }
      if (data?.cover_img) {
        const fileKey = getKey(data.cover_img, "project_cover")
        await env.R2.put(fileKey, data.cover_img)
        newProjectData.cover_img = fileKey
      }

      if (data?.kml_file) {
        const fileKey = getKey(data?.kml_file, "kml_file")
        await env.R2.put(fileKey, data?.kml_file)
        newProjectData.kml_file = fileKey
      }
      const result = await new ProjectService(env.DB).create(newProjectData)
      if (!result?.project?.id) {
        return json({ error: "project create unsuccessfully", saved: null })
      }
      if (action === "save-and-continue") {
        return redirect(
          `/user/projects/${result.project.id}/${Project_STEPS.ALIGNMENTS}`
        )
      } else if (action === "save") {
        return redirect(`/user/projects/${result.project.id}`)
      }
    } else {
      const newProjectData: Omit<typeof data, "cover_img"> = {
        ...data
      }
      if (data?.cover_img) {
        const fileKey = getKey(data.cover_img, "project_cover")
        await env.R2.put(fileKey, data.cover_img)
        newProjectData.cover_img = fileKey
      }
      if (data?.kml_file) {
        const fileKey = getKey(data?.kml_file, "kml_file")
        await env.R2.put(fileKey, data?.kml_file)
        newProjectData.kml_file = fileKey
      }
      await new ProjectService(env.DB).update(
        parseInt(params.id ?? ""),
        newProjectData
      )
      data["con.id"] &&
        (await new IdeaNoteService(env.DB).update(Number(data["con.id"]), {
          positive: data["con.positive"],
          dev_prio: data["con.dev_prio"],
          total_area: data["con.total_area"],
          estimated_er: data["con.estimated_er"],
          start_date: data["con.start_date"],
          end_date: data["con.end_date"]
        }))

      if (action === "save-and-continue") {
        return redirect(
          `/user/projects/${params.id}/${Project_STEPS.ALIGNMENTS}`
        )
      } else if (action === "save") {
        return redirect(`/user/projects/${params.id}`)
      }
    }
  }
}

export async function loader({ context, request }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const sectors = await new ProjectSectorService(env.DB).getMany()
  const user = await getAuthUser(context, request)

  const org = await new OrganizationService(env.DB).getById(user.org_id)

  return json({ sectors, user, org })
}

const ProjectInformation = () => {
  const { sectors, user, org } = useLoaderData<typeof loader>()
  const { project } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id._form"
  )
  const [startDate, setStartDate] = useState<CalendarDate | null>(null)
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)
  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting"
  useEffect(() => {
    if (project?.ideaNote?.start_date) {
      setStartDate(parseDate(project.ideaNote.start_date))
    }
    if (project?.ideaNote?.end_date) {
      setEndDate(parseDate(project.ideaNote.end_date))
    }
  }, [project])

  const navigate = useNavigate()

  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid grid-cols-3 py-8 gap-x-8">
        <div className="col-span-2 space-y-6">
          <div className="grid grid-cols-2 space-y-3">
            <input
              type="hidden"
              name="con.id"
              value={project?.ideaNote?.id ?? ""}></input>

            <div className="">
              <Select
                label="Sector"
                isRequired
                placeholder="Select Project Sector"
                data={sectors.map(sector => ({
                  key: sector.id.toString(),
                  label: sector.name
                }))}
                defaultSelectedKeys={[project?.sector_id?.toString()]}
                name="sector_id"
              />
            </div>
            <div className="col-span-2">
              <Input
                label="Project Name"
                defaultValue={project?.name ?? ""}
                isRequired
                placeholder="Enter Project Name"
                name="name"
              />
            </div>
            <input type="hidden" name="org_id" value={org.id ?? ""} />
            <div className="col-span-2">
              <TextArea
                label="Description"
                placeholder="Enter project description"
                name="description"
                defaultValue={project?.description ?? ""}
              />
            </div>
            <div className="col-span-2">
              <FileSelect
                label="Cover Image"
                name="cover_img"
                fileNameSelected={project?.cover_img ?? null}
                acceptFileTypes=".jpg, .jpeg, .png ,.webp"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 space-y-3">
            <div>
              <p className="font-bold text-[20px]">Scale</p>
            </div>
            <div className="grid grid-cols-2 space-x-3">
              <div>
                <Input
                  label="Total Area (hectares)"
                  isRequired
                  placeholder="Enter project total area size"
                  name="con.total_area"
                  defaultValue={project?.ideaNote?.total_area}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
              <div>
                <Input
                  label="Estimated ER (tCO2e/year)"
                  isRequired
                  placeholder="Enter estimated Emission Reduction"
                  name="con.estimated_er"
                  defaultValue={project?.ideaNote?.estimated_er}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 space-y-3">
            <div>
              <p className="font-bold text-[20px]">Timeline</p>
            </div>
            <div className="grid grid-cols-2 space-x-3">
              <div>
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Project Start Date"
                  name="con.start_date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  maxValue={endDate}
                />
              </div>
              <div>
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Project End Date"
                  name="con.end_date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  minValue={startDate}
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 space-y-3">
            <div>
              <p className="font-bold text-[20px]">Location</p>
            </div>
            <div className="grid grid-cols-2 gap-x-3 gap-y-3">
              <div>
                <Select
                  label="City/Province"
                  isRequired
                  placeholder="Select City/Province"
                  name="province"
                  defaultSelectedKeys={[project?.province?.toString()]}
                  data={provinces}
                />
              </div>
              <div className="col-span-2">
                <Input
                  label="Address"
                  placeholder="Enter project address"
                  name="address"
                  defaultValue={project?.address}
                />
              </div>
              <div>
                <Input
                  label="Latitude"
                  isRequired
                  placeholder="Enter project latitude"
                  name="lat"
                  defaultValue={project?.lat}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
              <div>
                <Input
                  label="Longitude"
                  isRequired
                  placeholder="Enter project longitude"
                  name="lng"
                  defaultValue={project?.lng}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
              <div className="col-span-2">
                <FileSelect
                  label="Area Map (KML)"
                  name="kml_file"
                  fileNameSelected={project?.kml_file ?? null}
                  acceptFileTypes=".kml"
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 space-y-3">
            <div>
              <p className="font-bold text-[20px]">Organization Information</p>
            </div>
            <div className="grid grid-cols-3 gap-x-3 gap-y-3">
              <div className="col-span-2">
                <Input
                  label="Organization Name"
                  isRequired
                  value={org?.name}
                  isDisabled
                  placeholder="Enter your organization name"
                />
              </div>
              <div>
                <Select
                  label="Type"
                  placeholder="Select Type of Organization"
                  isDisabled
                  data={[
                    { key: "1", label: "1" },
                    { key: "2", label: "2" }
                  ]}
                />
              </div>
              <div className="col-span-2">
                <Input
                  label="Address"
                  isRequired
                  placeholder="Enter project address"
                  value={org?.address}
                  isDisabled
                />
              </div>
              <div>
                <Select
                  isDisabled
                  label="Province/City"
                  placeholder="Select Province/City"
                  data={[
                    { key: "kompong cham", label: "Kompong Cham" },
                    { key: "phnom penh", label: "Phnom Penh" }
                  ]}
                />
              </div>
              <div className="col-span-3">
                <Input
                  isDisabled
                  label="Contact Person Name"
                  isRequired
                  placeholder="Enter your organization contact person full name"
                  value={org?.name}
                />
              </div>
              <div className="col-span-3">
                <div className="grid grid-cols-2 gap-x-3">
                  <div>
                    <Input
                      isDisabled
                      label="Contact Email"
                      isRequired
                      placeholder="Enter your organization email"
                      value={org?.email}
                    />
                  </div>
                  <div>
                    <Input
                      isDisabled
                      label="Contact Number"
                      value={org?.phone}
                      isRequired
                      placeholder="Enter your organization number"
                    />
                  </div>
                </div>
              </div>
              <div className="col-span-3">
                <Input
                  isDisabled
                  label="Website"
                  value={org?.website}
                  isRequired
                  placeholder="Enter your organization website"
                />
              </div>
            </div>
          </div>
          <div className="flex gap-x-3">
            <Button
              className="bg-primary text-white"
              name="action"
              value="save-and-continue"
              type="submit"
              isDisabled={isSubmitting}>
              Save & Continue
            </Button>
            <Button
              variant="bordered"
              color="primary"
              name="action"
              value="save"
              type="submit"
              isDisabled={isSubmitting}>
              Save
            </Button>
            <Button
              className="bg-transparent"
              isDisabled={isSubmitting}
              onPress={() => {
                navigate(
                  project?.id
                    ? `/user/projects/${project?.id}`
                    : "/user/projects"
                )
              }}>
              Cancel
            </Button>
          </div>
        </div>
        <div className="space-y-6">
          <SectorInfo />
          <NameInfo />
          <ProjectDescriptionInfo />
          <CoverImageInfo />
          <AddressInfo />
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
