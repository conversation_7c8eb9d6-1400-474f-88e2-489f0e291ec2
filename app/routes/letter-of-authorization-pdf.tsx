import { PDFDocument, rgb, StandardFonts } from "pdf-lib"

interface RequestBody {
  date: string
  projectID: number
  projectName: string
  totalVolumeAuthorized: number
  totalVolumeReceviedPositiveExamination: number
  remainVolume: number
}

export const action = async ({ request }: { request: Request }) => {
  const body: RequestBody = await request.json()
  const pdfDoc = await PDFDocument.create()
  const page = pdfDoc.addPage([595, 842]) // A4 size
  const { width, height } = page.getSize()
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica)

  page.drawText("LETTER OF AUTHORIZATION", {
    x: width / 2 - 100,
    y: height - 50,
    size: 14,
    font,
    color: rgb(0, 0, 0)
  })

  page.drawText(`Date: ${body.date}`, {
    x: 400,
    y: height - 70,
    size: 12,
    font
  })
  page.drawText(`Reference number: ${body.projectID}`, {
    x: 50,
    y: height - 100,
    size: 12,
    font
  })
  page.drawText("FROM: Royal Government of Cambodia", {
    x: 50,
    y: height - 130,
    size: 12,
    font
  })

  page.drawText(`Letter of Authorization related to ${body.projectName}`, {
    x: 50,
    y: height - 160,
    size: 12,
    font
  })

  page.drawText(
    `As the designated responsible entity of the Royal Government of Cambodia, ` +
      `the National Authority for Greenhouse Gas Emission Reduction Mechanisms hereby informs you that ‘${body.projectName}’, ` +
      `registered as ${body.projectID} in Cambodia’s National GHG ER Registry, is authorized to generate ` +
      `greenhouse gas emission reductions (GHG ERs) for international transfer under Article 6 of the Paris Agreement, ` +
      `as per the conditions specified in this Letter.`,
    { x: 50, y: height - 200, size: 10, font, lineHeight: 14, maxWidth: 500 }
  )

  // Draw Table
  const tableStartY = height - 300
  const cellWidth = 250
  const cellHeight = 30
  const tableX = 50

  const headers = ["Authorization element", "Details"]

  const data = [
    ["Authorized cooperative approach", body.projectName],
    [
      "Authorized entity",
      "[Insert name of GHG ER project implementing entity]"
    ],
    [
      "Total cumulative maximum volume of GHG ERs",
      `${body.totalVolumeAuthorized} tCO2eq`
    ],
    ["Authorization period", "[Years over which volume may be generated]"],
    ["Issuing carbon mechanism", "[Insert carbon mechanism name]"],
    ["Authorized use for GHG ERs", "Use towards the achievement of an NDC"]
  ]

  // Draw headers
  headers.forEach((header, index) => {
    page.drawRectangle({
      x: tableX + index * cellWidth,
      y: tableStartY,
      width: cellWidth,
      height: cellHeight,
      borderColor: rgb(0, 0, 0),
      borderWidth: 1
    })
    page.drawText(header, {
      x: tableX + 5 + index * cellWidth,
      y: tableStartY + 10,
      size: 10,
      font
    })
  })

  // Draw table rows
  data.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      const y = tableStartY - (rowIndex + 1) * cellHeight
      page.drawRectangle({
        x: tableX + colIndex * cellWidth,
        y,
        width: cellWidth,
        height: cellHeight,
        borderColor: rgb(0, 0, 0),
        borderWidth: 1
      })
      page.drawText(cell, {
        x: tableX + 5 + colIndex * cellWidth,
        y: y + 10,
        size: 10,
        font
      })
    })
  })

  const pdfBytes = await pdfDoc.save()

  return new Response(pdfBytes, {
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": 'attachment; filename="letterOfAuthorization.pdf"'
    }
  })
}
