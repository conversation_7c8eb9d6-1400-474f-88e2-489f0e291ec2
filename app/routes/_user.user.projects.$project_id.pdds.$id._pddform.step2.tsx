import { Button } from "@heroui/react"
import { Form, useMatches } from "@remix-run/react"
import AddressInfo from "~/components/projectRegistration/AddressInfor"
import CoverImageInfo from "~/components/projectRegistration/CoverImageInfo"
import NameInfo from "~/components/projectRegistration/NameInfo"
import ProjectDescriptionInfo from "~/components/projectRegistration/ProjectDescriptionInfo"
import SectorInfo from "~/components/projectRegistration/SectorInfo"
import TextArea from "~/components/ui/textArea"

const PDDStep2 = () => {
  const matches = useMatches()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid grid-cols-3">
        <div className="col-span-2 pr-24 space-y-6">
          <div className="grid gap-y-3">
            <div>
              <TextArea
                label="Description"
                placeholder="Enter project description"
                name="description"
              />
            </div>
            <div>
              <TextArea
                label="Description"
                placeholder="Enter project description"
                name="description"
              />
            </div>
            <div>
              <TextArea
                label="Description"
                placeholder="Enter project description"
                name="description"
              />
            </div>
          </div>
          <div className="">item</div>
          <div className="">item</div>
          <div className="">item</div>
          <div className="flex gap-x-3">
            <Button
              className="bg-primary text-white"
              name="action"
              value="save-and-continue"
              type="submit">
              Save & Continue
            </Button>
            <Button
              variant="bordered"
              color="primary"
              name="action"
              value="save"
              type="submit">
              Save
            </Button>
            <Button className="bg-transparent">Cancel</Button>
          </div>
        </div>
        <div className="space-y-6">
          <SectorInfo />
          <NameInfo />
          <ProjectDescriptionInfo />
          <CoverImageInfo />
          <AddressInfo />
        </div>
      </div>
    </Form>
  )
}

export default PDDStep2
