import { ActionFunctionArgs, json } from "@remix-run/cloudflare"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"

export async function action({ request, context }: ActionFunctionArgs) {
  const payload = await request.formData()
  const email = payload.get("email")?.toString()
  if (!email) return json({ error: "Email is required", sent: null })
  const env = context.cloudflare.env as CloudflareENV
  const sent = await new MailService(env).sendOtpEmail(email)
  return json({ error: null, sent })
}

export function loader() {
  throw new Response("404", { status: 404 })
}

export default function SignupResendOTP() {
  return <></>
}
