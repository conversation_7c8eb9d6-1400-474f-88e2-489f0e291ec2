import { But<PERSON> } from "@heroui/react"
import { json, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Outlet, useLocation, useNavigate, useParams } from "@remix-run/react"
import { X } from "lucide-react"
import { useTranslation } from "react-i18next"
import { Project_STEPS } from "~/enums/EProjectStep"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

const projectOrders = [
  Project_STEPS.PROJECT_INFORMATION,
  Project_STEPS.ALIGNMENTS,
  Project_STEPS.LNOREQUEST
]

export async function loader({ context, params, request }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  if (params.id === "new") {
    return json({ project: null })
  } else {
    const id = params.id
    if (!id) throw new Response("Not found", { status: 404 })
    const project = await new ProjectService(env.DB).getById(parseInt(id))
    return json({ project })
  }
}

export default function NewProject() {
  const location = useLocation()
  const activeStep = location.pathname.split("/").pop()
  const { t } = useTranslation()
  const param = useParams()
  const isNew = param.id === "new"
  const navigate = useNavigate()

  return (
    <div className="px-10">
      <div className="space-y-3 ">
        <div className="flex justify-between">
          <div className="items-center flex">
            <p className="font-bold text-[22px]">
              {activeStep === Project_STEPS.LNOREQUEST
                ? t(`project_step.${activeStep}`)
                : "NEW PROJECT IDEA"}
            </p>
          </div>
          <div>
            <Button
              isIconOnly
              className="bg-transparent"
              onPress={() => {
                navigate(
                  isNew ? "/user/projects" : `/user/projects/${param.id}`
                )
              }}>
              <X />
            </Button>
          </div>
        </div>
        <div className="border-t-2 border-t-[#E4E4E7] border-b-2 border-b-[#E4E4E7] py-6 flex space-x-8 text-[18px] items-center">
          {projectOrders.map((step, index) => {
            return (
              <>
                <div
                  key={index}
                  className={
                    index < projectOrders.length - 1
                      ? activeStep === step
                        ? "font-bold text-primary"
                        : "font-bold text-[#A1A1AA]"
                      : activeStep === step ||
                          activeStep === "lno-request-success"
                        ? "font-bold text-primary"
                        : "font-bold text-[#A1A1AA]"
                  }>
                  <p className="">
                    <span
                      className={
                        index < projectOrders.length - 1
                          ? activeStep === step
                            ? "text-primary"
                            : ""
                          : activeStep === step ||
                              activeStep === "lno-request-success"
                            ? "text-primary"
                            : ""
                      }>
                      0{index + 1}&nbsp;
                    </span>
                    {t(`project_step.${step}`)}
                  </p>
                </div>
                {index < projectOrders.length - 1 && (
                  <div className="flex-1 border-t-2 border-[#D4D4D8]" />
                )}
              </>
            )
          })}
        </div>
      </div>
      <Outlet />
    </div>
  )
}
