import { But<PERSON> } from "@heroui/react"
import { DateValue, parseDate } from "@internationalized/date"
import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
  useNavigation,
  useSubmit
} from "@remix-run/react"
import { useEffect, useRef, useState } from "react"
import Forehead from "~/components/layout/admin/forehead"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import { getImage, getKey } from "~/helpers/r2"
import { getFormDataFromObject, getUpdatedFormData } from "~/lib/form"
import IdeaNoteService from "~/services/db/ideaNote"
import ProjectService from "~/services/db/project"
import ProjectSectorService from "~/services/db/projectService"
import ProjectTypeService from "~/services/db/projectType"
import type { CloudflareENV } from "~/types"
import { ProjectValidator } from "~/validators/project"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new ProjectValidator(env.DB)
  const { data, error } = await validator.validateEdit(payload)
  if (!data) return json({ error, saved: null })
  const newProjectData: Omit<typeof data, "cover_img"> = {
    ...data
  }
  if (data?.cover_img) {
    const fileKey = getKey(data.cover_img, "project_cover")
    await env.R2.put(fileKey, data.cover_img)
    newProjectData.cover_img = fileKey
  }

  await new ProjectService(env.DB).update(parseInt(id), newProjectData)

  data["con.id"] &&
    (await new IdeaNoteService(env.DB).update(Number(data["con.id"]), {
      ghg: data["con.ghg"],
      positive: data["con.positive"],
      dev_prio: data["con.dev_prio"],
      start_date: data["con.start_date"],
      end_date: data["con.end_date"],
      first_date_delivery: data["con.first_date_delivery"],
      technology_tranfer: data["con.technology_transfer"],
      economic_benefit: data["con.economic_benefits"]
    }))
  return redirect("/user/projects")
}

export async function loader({ context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = await new ProjectService(env.DB).getById(parseInt(id))
  const sectors = await new ProjectSectorService(env.DB).getMany()
  const types = await new ProjectTypeService(env.DB).getMany()

  return json({ id, project, sectors, types })
}

export default function EditProject() {
  const { id, project } = useLoaderData<typeof loader>()
  const projectForm = useRef<HTMLFormElement | null>(null)
  const navigation = useNavigation()
  const navigate = useNavigate()
  const submit = useSubmit()
  const actionData = useActionData<typeof action>()
  const actionPath = `/user/projects/${id}/edit`
  const isSubmitting = navigation.formAction === actionPath
  const error = actionData?.error

  const handleSubmit = (project: object) => {
    const form = projectForm.current
    if (form) {
      const a = new FormData(form)
      const b = getFormDataFromObject(project)
      const formData = getUpdatedFormData(a, b)
      if (Array.from(formData.keys()).length > 0) {
        submit(formData, {
          action: actionPath,
          method: "POST",
          encType: "multipart/form-data"
        })
      }
    }
  }
  const { types } = useLoaderData<typeof loader>()

  const availableTypes = types.filter(
    type => type.sector_id === project?.sector_id
  )

  const [cover, setCover] = useState<string>(
    getImage(project?.cover_img ? project?.cover_img : "")
  )

  const [startDate, setStartDate] = useState<DateValue | null>(null)
  const [endDate, setEndDate] = useState<DateValue | null>(null)
  const [firstDateDelivery, setFirstDateDelivery] = useState<DateValue | null>(
    null
  )
  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date)
  }
  const handleFirstDateDeliveryChange = (date: DateValue | null) => {
    setFirstDateDelivery(date)
  }

  useEffect(() => {
    if (project?.ideaNote?.start_date) {
      setStartDate(parseDate(project?.ideaNote?.start_date))
    }
    if (project?.ideaNote?.end_date) {
      setEndDate(parseDate(project?.ideaNote?.end_date))
    }
    if (project?.ideaNote?.first_date_delivery) {
      setFirstDateDelivery(parseDate(project?.ideaNote?.first_date_delivery))
    }
  }, [])

  return (
    <>
      <Forehead title={`Edit project: ${id}`} backref="/user/projects" />
      {project && (
        <Form ref={projectForm} method="POST">
          <div className="container max-w-3xl mx-auto">
            <div className="bg-white shadow-md border p-6 rounded-xl">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>Project Basic Information</div>
                  <div className="grid gap-x-4 gap-y-2">
                    <div>
                      <Input
                        isRequired
                        label={"Project Name"}
                        name="name"
                        placeholder="Enter Project Name"
                        defaultValue={project?.name ? project?.name : ""}
                      />
                    </div>
                    <div>
                      {/* <FileSelect
                        label="Project Location"
                        name="location"
                        fileNameSelected={project?.location}></FileSelect> */}
                    </div>

                    <div>
                      <label className="relative col-span-2 md:col-span-2  border-2 flex items-center justify-center h-40 rounded-2xl cursor-pointer border-black/50 overflow-hidden">
                        <p className="text-black/60">Upload Cover</p>
                        {cover && (
                          <img
                            className="absolute top-0 left-0 w-full h-full object-cover"
                            src={cover}
                          />
                        )}
                        <input
                          id="cover_img"
                          type="file"
                          name="cover_img"
                          hidden
                          accept="image/*"
                          onChange={e => {
                            if (e.target.files?.length) {
                              setCover(URL.createObjectURL(e.target.files[0]))
                            }
                          }}
                        />
                      </label>
                    </div>
                    <div>
                      <div className="flex justify-end">
                        <Button
                          type="button"
                          as="label"
                          htmlFor="cover_img"
                          color="primary">
                          {cover ? "Change Cover" : "Upload Cover"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-3">
                  <div>Idea Note</div>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="col-span-2 md:col-span-2">
                      <input
                        type="hidden"
                        name="con.id"
                        value={project?.ideaNote?.id ?? ""}></input>
                      <Input
                        label="Greenhouse Gases Targeted"
                        name="con.ghg"
                        placeholder="Greenhouse Gases Targeted"
                        defaultValue={project?.ideaNote?.ghg ?? ""}
                      />
                    </div>
                    <div className="col-span-2 md:col-span-2">
                      <TextArea
                        label="Positive List"
                        name="con.positive"
                        defaultValue={project?.ideaNote?.positive ?? ""}
                        placeholder={
                          "GHG ER Project Alignment with Cambodia's Positive List"
                        }
                      />
                    </div>
                    <div className="col-span-2 md:col-span-2">
                      <TextArea
                        label="Sustainable Development Priorities"
                        name="con.dev_prio"
                        defaultValue={project?.ideaNote?.dev_prio ?? ""}
                        placeholder={"Sustainable Development Priorities"}
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <DatePicker
                        isRequired
                        variant="bordered"
                        label="Project Start Date"
                        name="con.start_date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        maxValue={endDate}
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <DatePicker
                        isRequired
                        variant="bordered"
                        label="Project End Date"
                        name="con.end_date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        minValue={startDate}
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <DatePicker
                        isRequired
                        variant="bordered"
                        label="First Year GHG ER Delivery"
                        name="con.first_date_delivery"
                        value={firstDateDelivery}
                        onChange={handleFirstDateDeliveryChange}
                        minValue={startDate}
                        maxValue={endDate}
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <Input
                        label={"Technology Transfer"}
                        name="con.technology_transfer"
                        placeholder={"Enter Technology Transfer"}
                        defaultValue={
                          project?.ideaNote?.technology_tranfer ?? ""
                        }
                      />
                    </div>
                    <div className="col-span-2 md:col-span-2">
                      <Input
                        label={"Economic Benefits"}
                        name="con.economic_benefits"
                        placeholder={"Enter Economic Benefits"}
                        defaultValue={project?.ideaNote?.economic_benefit ?? ""}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-10 flex flex-col gap-2">
              <button
                type="button"
                onClick={() => handleSubmit(project)}
                className="btn btn-primary"
                disabled={isSubmitting}>
                {isSubmitting ? (
                  <span className="flex gap-2 items-center">
                    <span className="loading loading-spinner"></span>
                    <span>Saving...</span>
                  </span>
                ) : (
                  "Save"
                )}
              </button>
              <button
                className="btn btn-ghost"
                disabled={isSubmitting}
                onClick={() => navigate(-1)}>
                Cancel
              </button>
            </div>
          </div>
        </Form>
      )}
    </>
  )
}
