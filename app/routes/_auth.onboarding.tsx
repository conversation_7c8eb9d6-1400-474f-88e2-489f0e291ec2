import { Card, CardBody } from "@heroui/card"
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { CircleCheckBig, X } from "lucide-react"
import { useTranslation } from "react-i18next"
import Logo from "~/components/logo"
import Title from "~/components/title"

// export async function loader({ request, context }: LoaderFunctionArgs) {
//   const env = context.cloudflare.env as CloudflareENV
//   const { authenticator, sessionStorage } = getAuthenticator(env)
//   authenticator.isAuthenticated(request, { failureRedirect: "/login" })
//   const session = await sessionStorage.getSession(request.headers.get("Cookie"))
//   const message = session.get("message") || null
//   if (!message) throw new Response("Page Not Found", { status: 404 })
//   session.unset("message")
//   const cookie = await setCookie(sessionStorage, session)
//   return json({ message }, { headers: { ...cookie } })
// }

export default function Onboarding() {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div className="container mx-auto py-5 grid gap-2">
      <div className="flex justify-between px-5">
        <div className="w-1">
          <Logo href="/" />
        </div>
        <div className="hidden md:block justify-center">
          <Title title="common.registration" />
        </div>
        <div className="w-1 flex justify-end">
          <Button
            isIconOnly
            aria-label="Close"
            variant="light"
            onPress={() => navigate("/")}>
            <X />
          </Button>
        </div>
      </div>
      <div className="md:hidden flex justify-center">
        <Title title="common.registration" />
      </div>

      <div className="container max-w-3xl mx-auto mt-24">
        <Card>
          <CardBody className="p-24 flex items-center">
            <CircleCheckBig size={80} />
            <p className="py-8 text-center">
              Your application has been successfully submitted. We appreciate
              the time and effort you took to apply, and we will review your
              submission carefully. You will be contacted if any additional
              information is needed or to discuss the next steps in the process.
            </p>
            <Button as={Link} href="/" color="primary">
              {t("common.close")}
            </Button>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}
