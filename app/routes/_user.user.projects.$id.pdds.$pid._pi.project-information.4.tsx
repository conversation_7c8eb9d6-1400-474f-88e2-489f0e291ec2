import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const env = context.cloudflare.env as CloudflareENV
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), {
      ...data,
      step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/4`
    }))
  const action = data?.action
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/5`
    )
  } else {
    return redirect(`/pdds/${params?.pid}`)
  }
}

const ProjectInformation = () => {
  const navigate = useNavigate()
  const params = useParams()
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  return (
    <Form method="post" encType="multipart/form-data">
      <div className="flex gap-x-8">
        <div className="w-[900px]">
          <div className="grid grid-cols-2 gap-x-3 gap-y-3">
            <div>
              <Input
                name="methodology_name"
                label="Methodology Name"
                isRequired
                placeholder="Enter methodology name"
                defaultValue={pdd?.methodology_name}
              />
            </div>
            <div className="">
              <Input
                name="version"
                label="Version"
                isRequired
                placeholder="Enter methodology name"
                defaultValue={pdd?.version}
              />
            </div>
            <div className="col-span-2">
              <Input
                name="methodology_link"
                label="Methodology Link (URL)"
                isRequired
                placeholder="Enter the URL of the methodology"
                defaultValue={pdd?.methodology_link}
                type="text"
                pattern="https?://.+"
              />
            </div>
            <div className="col-span-2">
              <TextArea
                name="baseline_scenario"
                label="Baseline Scenario"
                placeholder="Describe the baseline scenario before the project."
                defaultValue={pdd?.baseline_scenario}
              />
            </div>
            <div className="col-span-2">
              <TextArea
                label="Project Scenario"
                placeholder="Outline the expected changes due to project activities"
                name="project_scenario"
                defaultValue={pdd?.project_scenario}
              />
            </div>
            <div className="col-span-2 flex gap-x-3">
              <Button
                className="bg-primary text-white"
                name="action"
                value="save-and-continue"
                type="submit">
                Save & Continue
              </Button>
              <Button
                variant="bordered"
                color="primary"
                name="action"
                value="save"
                type="submit">
                Save
              </Button>
              <Button
                className="bg-transparent"
                onPress={() => {
                  navigate(`/pdds/${params?.pid}`)
                }}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
        <div>
          <NoteInfo />
        </div>
      </div>
    </Form>
  )
}
export default ProjectInformation
