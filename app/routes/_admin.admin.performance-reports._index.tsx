import { Pagination } from "@heroui/react"
import type { j<PERSON>, LoaderFunctionArgs } from "@remix-run/cloudflare"
import { useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { and, eq } from "drizzle-orm"
import { MonitorUp } from "lucide-react"
import DataTable, { createDataTableRow } from "~/components/table/data-table"
import { filterSearch, getListQuery } from "~/components/table/query"
import useListQuery from "~/components/table/use-list-query"
import { implementations } from "~/db/schema/implementations"
import { EProjectStatus } from "~/enums/EProjectStatus"
import {
  getTypeOfCarbonName,
  getUseOfGhgErsName
} from "~/lib/performanceDetail"
import ImplementationService from "~/services/db/implementation"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export type ImplementationFilter = {
  status?: EProjectStatus
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const { page, limit, search, sort, status } =
    getListQuery<ImplementationFilter>(request)

  const filter = and(
    filterSearch(search, [implementations.id]),
    status ? eq(implementations.status, status) : undefined
  )
  const implementationService = new ImplementationService(env.DB)

  const { total, result } = await implementationService.paginateTable({
    filter,
    page,
    limit,
    sort,
    user
  })
  return json({ result, total })
}

const AdminImplementationList = () => {
  const { result, total } = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  const rows = createDataTableRow<typeof result>([
    {
      key: "id",
      headerLabel: "Implementation ID"
    },
    {
      key: "project_name",
      headerLabel: "Project Name",
      renderCell: row => <div>{row?.project?.name}</div>
    },
    {
      key: "co2",
      headerLabel: "Co2"
    },
    {
      key: "share_co2",
      headerLabel: "Share Co2"
    },
    {
      key: "carbon_credit",
      headerLabel: "Carbon Credit"
    },
    {
      key: "type_of_carbon_mechanism",
      headerLabel: "Type Of Carbon Mechanism",
      renderCell: row => (
        <div>
          {row?.type_of_carbon_mechanism
            ? getTypeOfCarbonName(row?.type_of_carbon_mechanism)
            : row?.other_type_of_carbon_mechanism}
        </div>
      )
    },
    {
      key: "user_of_ghg_ers",
      headerLabel: "Use of GHG ERs",
      renderCell: row => <div>{getUseOfGhgErsName(row.user_of_ghg_ers)}</div>
    },
    {
      key: "create_at",
      headerLabel: "Created At",
      renderCell: row => (
        <div>{dayjs(row?.created_at).format("DD MMM YYYY")}</div>
      )
    },
    {
      key: "status",
      headerLabel: "Status",
      renderCell: row => (
        <div
          className={`capitalize px-2 py-1 rounded-full w-fit  min-w-44 text-end
            ${
              row.status === "Rejected_LPE"
                ? "text-red-500 font-bold"
                : "text-green-500 font-bold"
            }`}>
          {row.status}
        </div>
      )
    }
  ])

  const { query, updateQuery, sortDescriptor, handleSortChange } =
    useListQuery<ImplementationFilter>()

  return (
    <>
      <div className="space-y-4">
        <div className="flex space-x-2">
          <div>
            <MonitorUp />{" "}
          </div>
          <div>
            <p className="font-bold">Performance Report list</p>
          </div>
        </div>
        <DataTable
          data={result}
          rows={rows}
          onSortChange={handleSortChange}
          sortDescriptor={sortDescriptor}
          onRowClick={id => {
            navigate(`/admin/performance-reports/${id}`)
          }}
        />
        {total > 0 && (
          <div className="flex justify-center">
            <Pagination
              showControls
              page={Number(query.page)}
              total={Math.ceil(total / Number(query.limit))}
              onChange={page => {
                updateQuery({ page })
              }}
            />
          </div>
        )}
      </div>
    </>
  )
}

export default AdminImplementationList
