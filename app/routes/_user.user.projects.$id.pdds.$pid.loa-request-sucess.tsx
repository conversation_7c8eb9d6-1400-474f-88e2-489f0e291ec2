import { But<PERSON> } from "@heroui/react"
import { useNavigate } from "@remix-run/react"
import { PartyPopper } from "lucide-react"

const LOARequestSuccess = () => {
  const navigate = useNavigate()
  return (
    <div className=" container max-w-3xl mx-auto space-y-8 py-8 ">
      <div className="grid justify-center">
        <PartyPopper className="w-32 h-32" />
      </div>

      <div className="grid gap-y-2">
        <div>
          <p className="font-bold text-[20px] text-center">
            LOA Request Submitted
          </p>
        </div>
        <div>
          <p className="text-center">
            Your Project PDD has been submitted successfully!
          </p>
        </div>
      </div>

      <div className="grid bg-[#EDECEC] p-8 rounded-2xl">
        <div className="grid gap-y-4">
          <div>
            <p>
              Thank you for submitting your request for a Letter of
              Authorization (LOA) to the Cambodia National Carbon Registry.
            </p>
          </div>
          <div className="px-6">
            <ul className="list-disc">
              <li>
                Your submission will be reviewed within 15 business days.
              </li>
              <li>
                You may be contacted for additional documentation or
                clarifications.
              </li>
              <li>
                Approval will result in issuance of the Letter of Authorization
                (LOA).
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="flex justify-center">
        <p className="text-center max-w-md justify-center">
          Thank you for supporting Cambodia’s climate action and sustainable
          development goals.
        </p>
      </div>
      <div className="flex justify-center gap-x-2">
        <div>
          <Button
            color="primary"
            onPress={() => {
              navigate("/user")
            }}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
}

export default LOARequestSuccess
