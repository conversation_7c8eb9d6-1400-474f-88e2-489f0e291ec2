import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Textarea,
  useDisclosure
} from "@heroui/react"
import { useFetcher } from "@remix-run/react"
import { PenBox } from "lucide-react"
import { useEffect, useRef } from "react"
import type { action } from "~/routes/_admin.admin._settings.settings.sectors.$id/route"
import type { ProjectSector } from "~/types"

interface Props {
  sector: ProjectSector
}

export default function EditButton(props: Props) {
  const { sector } = props
  const { isOpen, onOpen, onOpenChange } = useDisclosure()
  const fetcher = useFetcher<typeof action>()
  const isSubmitting = fetcher.state !== "idle"
  const actionData = fetcher.data
  const error = actionData?.fieldErrors
  const frm = useRef<HTMLFormElement>(null)

  useEffect(() => {
    if (actionData?.ok) onOpenChange()
  }, [actionData])

  function handleSubmit() {
    if (frm.current) {
      fetcher.submit(frm.current, {
        method: "PUT",
        action: `/admin/settings/sectors/${sector.id}`
      })
    }
  }

  const sectorForm = (item: ProjectSector) => {
    return (
      <div className="space-y-4">
        {actionData?.ok === false && (
          <Alert color="danger">Oops, Somethig went wrong!</Alert>
        )}
        <fetcher.Form ref={frm} method="PUT">
          <fieldset className="space-y-4" disabled={isSubmitting}>
            <Input
              name="name"
              errorMessage={error?.name ? error.name[0] : ""}
              defaultValue={item.name}
              isInvalid={error?.name !== undefined}
              label="Name"
              variant="bordered"
            />
            <Input
              name="abbr"
              defaultValue={item.abbr}
              errorMessage={error?.abbr ? error.abbr[0] : ""}
              isInvalid={error?.abbr !== undefined}
              label="Abbreviation"
              variant="bordered"
              maxLength={10}
              width={"sm"}
            />
            <Textarea
              name="description"
              defaultValue={sector.description ?? ""}
              label="Description"
              rows={3}
              errorMessage={error?.description ? error.description[0] : ""}
              isInvalid={error?.description !== undefined}
              variant="bordered"
            />
          </fieldset>
        </fetcher.Form>
      </div>
    )
  }
  return (
    <span>
      <Button isIconOnly onPress={onOpen} size="sm" variant="light">
        <PenBox size={16} />
      </Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {onClose => (
            <>
              <ModalHeader>Edit Sector</ModalHeader>
              <ModalBody>{sectorForm(sector)}</ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleSubmit}>
                  Save Changes
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </span>
  )
}
