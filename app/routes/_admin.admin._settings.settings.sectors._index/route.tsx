import {
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow
} from "@heroui/react"
import type {
  ActionFunctionArgs,
  LoaderFunctionArgs
} from "@remix-run/cloudflare"
import { useLoaderData } from "@remix-run/react"
import { Trash } from "lucide-react"
import { ProjectSectorService } from "~/services/db"
import type { CloudflareENV } from "~/types"
import { ProjectSectorValidator } from "~/validators/projectSector"
import EditButton from "./editButton"
import NewButton from "./newButton"

export async function loader({ context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const sectors = await new ProjectSectorService(env.DB).getMany()
  return { sectors }
}

export async function action({ request, context }: ActionFunctionArgs) {
  const method = request.method
  if (method === "POST") {
    const env = context.cloudflare.env as CloudflareENV
    const payload = await request.formData()
    const v = new ProjectSectorValidator()
    const { data, error } = await v.validateCreate(payload)
    if (!data) return { error, sector: null }
    const sector = await new ProjectSectorService(env.DB).create(data)
    return { error, sector }
  }
  return null // do nothing
}

export default function SectorsIndex() {
  const { sectors } = useLoaderData<typeof loader>()
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-400">Project Sectors</h2>
        <NewButton />
      </div>
      <Table aria-label="Example static collection table">
        <TableHeader>
          <TableColumn>ID</TableColumn>
          <TableColumn>Name</TableColumn>
          <TableColumn>ABBR</TableColumn>
          <TableColumn>Description</TableColumn>
          <TableColumn>Actions</TableColumn>
        </TableHeader>
        <TableBody>
          {sectors.map(item => (
            <TableRow key={item.id}>
              <TableCell>{item.id}</TableCell>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.abbr}</TableCell>
              <TableCell>{item.description || "-"}</TableCell>
              <TableCell>
                <EditButton sector={item} />
                <Button
                  isIconOnly
                  size="sm"
                  as="a"
                  variant="light"
                  href={`/admin/settings/sectors/${item.id}/delete`}>
                  <Trash size={16} />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
