import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Textarea,
  useDisclosure
} from "@heroui/react"
import { useFetcher } from "@remix-run/react"
import { useEffect, useRef } from "react"
import type { action } from "./route"

export default function NewButton() {
  const { isOpen, onOpenChange } = useDisclosure()
  const fetcher = useFetcher<typeof action>()
  const isSubmitting = fetcher.state !== "idle"
  const frm = useRef<HTMLFormElement>(null)
  const actionData = fetcher.data
  const error = actionData?.error

  useEffect(() => {
    if (actionData?.sector) onOpenChange()
  }, [actionData])

  const handleSubmit = () => {
    if (frm.current) {
      fetcher.submit(frm.current, {
        method: "POST",
        action: "/admin/settings/sectors"
      })
    }
  }

  const renderForm = () => {
    return (
      <fetcher.Form ref={frm}>
        <fieldset className="space-y-4" disabled={isSubmitting}>
          <Input
            name="name"
            errorMessage={error?.name ? error.name[0] : ""}
            isInvalid={error?.name !== undefined}
            label="Name"
            variant="bordered"
          />
          <Input
            name="abbr"
            errorMessage={error?.abbr ? error.abbr[0] : ""}
            isInvalid={error?.abbr !== undefined}
            label="Abbreviation"
            variant="bordered"
            maxLength={10}
            width={"sm"}
          />
          <Textarea
            name="description"
            label="Description"
            rows={3}
            errorMessage={error?.description ? error.description[0] : ""}
            isInvalid={error?.description !== undefined}
            variant="bordered"
          />
        </fieldset>
      </fetcher.Form>
    )
  }

  return (
    <div className="inline-flex">
      <Button variant="flat" onPress={onOpenChange}>
        New Sector
      </Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {onClose => (
            <>
              <ModalHeader>New Sector</ModalHeader>
              <ModalBody>{renderForm()}</ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleSubmit}>
                  Create
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}
