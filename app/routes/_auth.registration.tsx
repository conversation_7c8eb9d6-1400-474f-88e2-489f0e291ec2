import { But<PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigate
} from "@remix-run/react"
import { X } from "lucide-react"
import { useState } from "react"
import Logo from "~/components/logo"
import Title from "~/components/title"
import AgreementsStep from "~/components/userRegistration/Agreement"
import ApplicationFormStep from "~/components/userRegistration/Application-form"
import OverviewStep from "~/components/userRegistration/Overview"
import { getKey } from "~/helpers/r2"
import { getAuthenticator, setCookie } from "~/services/auth"
import OrganizationService from "~/services/db/organization"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"
import { OrganizationValidator } from "~/validators/organization"
import { RegistrationValidator } from "~/validators/registration"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const { sessionStorage } = getAuthenticator(env)
  const org_id = payload.get("org.id")

  if (!org_id) {
    const validator = new OrganizationValidator(env.DB)
    const { data, error } = await validator.validateCreate(payload)

    if (!data) return json({ error, sent: null })
  }
  const validator = new RegistrationValidator(env.DB)
  const { data, error } = await validator.validateCreate(payload)
  if (!data) return json({ error, sent: null })
  const newUserData = {
    ...data,
    national_id_passport_document: "",
    business_registration_document: "",
    organization_authorization_letter_document: ""
  }
  if (data?.national_id_passport_document) {
    const fileKey = getKey(
      data.national_id_passport_document,
      "national_id_passport_document"
    )
    await env.R2.put(fileKey, data.national_id_passport_document)
    newUserData.national_id_passport_document = fileKey
  }

  if (data?.business_registration_document) {
    const fileKey = getKey(
      data.business_registration_document,
      "business_registration_document"
    )
    await env.R2.put(fileKey, data.business_registration_document)
    newUserData.business_registration_document = fileKey
  }
  if (data?.organization_authorization_letter_document) {
    const fileKey = getKey(
      data.organization_authorization_letter_document,
      "organization_authorization_letter_document"
    )
    await env.R2.put(fileKey, data.organization_authorization_letter_document)
    newUserData.organization_authorization_letter_document = fileKey
  }

  const email = data.email
  const sent = await new MailService(env).sendOtpEmail(email)
  if (sent.error) return json({ error, sent })
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  session.set("signup", { next: "/checkpoint", email, data: newUserData })
  const cookie = await setCookie(sessionStorage, session)
  return redirect("/verify", { headers: { ...cookie } })
}

export const loader = async ({ request, context }: ActionFunctionArgs) => {
  const env = context.cloudflare.env as CloudflareENV

  const organizations = await new OrganizationService(
    env.DB
  ).getAllOrganizations()

  return json({ organizations })
}

enum EFormStep {
  Overview = "overview",
  Agreement = "agreement",
  ApplicationForm = "application-form"
}

export default function Registration() {
  const [step, setStep] = useState<EFormStep>(EFormStep.Overview)
  const actionData = useActionData<typeof action>()
  const error = actionData?.error
  const navigate = useNavigate()

  const { organizations } = useLoaderData<typeof loader>()

  return (
    <div className="container mx-auto py-5 grid gap-2">
      <div className="flex justify-between px-5">
        <div className="w-1">
          <Logo href="/" />
        </div>
        <div className="hidden md:block justify-center">
          <Title title="common.registration" />
        </div>
        <div className="w-1 flex justify-end">
          <Button
            isIconOnly
            aria-label="Close"
            variant="light"
            onPress={() => navigate(-1)}>
            <X />
          </Button>
        </div>
      </div>
      <div className="md:hidden flex justify-center">
        <Title title="common.registration" />
      </div>

      <Form method="POST" encType="multipart/form-data">
        {step === EFormStep.Overview && (
          <OverviewStep
            onContinue={() => {
              setStep(EFormStep.Agreement)
            }}
          />
        )}

        {step === EFormStep.Agreement && (
          <AgreementsStep
            onContinue={() => {
              setStep(EFormStep.ApplicationForm)
            }}
          />
        )}

        {step === EFormStep.ApplicationForm && (
          <ApplicationFormStep error={error} organizations={organizations} />
        )}
      </Form>
    </div>
  )
}
