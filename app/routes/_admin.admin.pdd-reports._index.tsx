import { Pagination } from "@heroui/react"
import type { json, LoaderFunctionArgs } from "@remix-run/cloudflare"
import { useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { and, eq, ne } from "drizzle-orm"
import { FileText } from "lucide-react"
import DataTable, { createDataTableRow } from "~/components/table/data-table"
import { filterSearch, getListQuery } from "~/components/table/query"
import useListQuery from "~/components/table/use-list-query"
import { pdds } from "~/db/schema/pdds"
import { EStatus } from "~/enums/EStatus"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export type PddFilter = {
  status?: EStatus
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV

  const { page, limit, search, sort, status } = getListQuery<PddFilter>(request)

  const filter = and(
    filterSearch(search, [pdds.id]),
    ne(pdds.status, "draft"),
    status ? eq(pdds.status, status) : undefined
  )
  const pddService = new PDDService(env.DB)

  const { total, result } = await pddService.paginateTable({
    filter,
    page,
    limit,
    sort
  })
  return json({ result, total })
}

const AdminPDDList = () => {
  const { result, total } = useLoaderData<typeof loader>()

  const navigate = useNavigate()
  const rows = createDataTableRow<typeof result>([
    {
      key: "id",
      headerLabel: "PDD ID"
    },
    {
      key: "methodology_name",
      headerLabel: "Methodology Name",
      allowSorting: true
    },
    {
      key: "baseline_scenario",
      headerLabel: "Baseline Scenario"
    },
    {
      key: "monitoring_plan",
      headerLabel: "Monitoring Plan"
    },
    {
      key: "project_development_cost",
      headerLabel: "Project Development Cost"
    },
    {
      key: "create_at",
      headerLabel: "Created At",
      renderCell: row => (
        <div>{dayjs(row?.created_at).format("DD MMM YYYY")}</div>
      ),
      allowSorting: true
    },
    {
      key: "status",
      headerLabel: "Status",
      renderCell: row => (
        <div
          className={`capitalize px-2 py-1 rounded-full w-fit  min-w-44 text-end
            ${
              row.status === "rejected"
                ? "text-red-500 font-bold"
                : "text-green-500 font-bold"
            }`}>
          {row.status}
        </div>
      )
    }
  ])

  const { query, updateQuery, sortDescriptor, handleSortChange } =
    useListQuery<PddFilter>()

  return (
    <>
      <div className="space-y-4">
        <div className="flex space-x-2">
          <div>
            <FileText />
          </div>
          <div>
            <p className="font-bold">PDD list</p>
          </div>
        </div>
        <DataTable
          data={result}
          rows={rows}
          onSortChange={handleSortChange}
          sortDescriptor={sortDescriptor}
          onRowClick={id => {
            navigate(`/admin/pdd-reports/${id}`)
          }}
        />
        {total > 0 && (
          <div className="flex justify-center">
            <Pagination
              showControls
              page={Number(query.page)}
              total={Math.ceil(total / Number(query.limit))}
              onChange={page => {
                updateQuery({ page })
              }}
            />
          </div>
        )}
      </div>
    </>
  )
}

export default AdminPDDList
