import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Select,
  SelectItem,
  SharedSelection
} from "@heroui/react"
import type { json, LoaderFunctionArgs, redirect } from "@remix-run/cloudflare"
import { Form, useLoaderData } from "@remix-run/react"
import React from "react"
import AssignCard from "~/components/assign/assignCard"
import DownloadButton from "~/components/buttonDownload"
import { useDialog } from "~/components/dialog"
import Forehead from "~/components/layout/admin/forehead"
import PDDDetail from "~/components/pdd/PddDetail"
import Input from "~/components/ui/input"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EStatus } from "~/enums/EStatus"
import { EModules, ERole } from "~/enums/EUserRole"
import { getImage, getKey } from "~/helpers/r2"
import PDDService from "~/services/db/pdd"
import PddAssignService from "~/services/db/pddAssign"
import ProjectService from "~/services/db/project"
import RequestLOAService from "~/services/db/requestLOA"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { hasPermissions } from "~/utils/permission"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const pdd = await new PDDService(env.DB).getById(parseInt(id))
  const user = await getAuthUser(context, request)
  const PDDAssignList = await new PddAssignService(env.DB).getByPDDID(
    parseInt(id)
  )

  const internalReviewerList = await new UserService(env.DB).getByRole(
    ERole.InternalReviewer
  )
  const externalAuditorList = await new UserService(env.DB).getByRole(
    ERole.IndependentAuditor
  )

  const coordinatorList = await new UserService(env.DB).getByRole(
    ERole.Coordinator
  )
  return json({
    pdd,
    user,
    PDDAssignList,
    internalReviewerList,
    externalAuditorList,
    coordinatorList
  })
}

export async function action({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  const data = await request.formData()
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const projectService = new ProjectService(env.DB)
  const actionType = data.get("action")
  const projectID = Number(data.get("project_id"))
  if (actionType === "review") {
    await projectService.update(projectID, {
      status: EProjectStatus.PendingPDDReview
    })
    return redirect(`/admin/pdd-reports/${id}`)
  }
  const file = data.get("support_document")
  const isInternalReviewer = data.get("isInternalReviewer")
  if (actionType === "assign") {
    const assignees = data.getAll("assign")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      pdd_id: Number(id),
      user_ids: assigneesTypeNumber,
      support_document: "",
      isInternalReviewer: isInternalReviewer === "true" ? true : false
    }
    if (file) {
      const fileKey = getKey(file as File, "assign_doucment")
      await env.R2.put(fileKey, data.get("support_document"))
      newAssignData.support_document = fileKey
    }
    await new PddAssignService(env.DB).create(newAssignData)
  }

  if (actionType === "reviewer_approve" || actionType === "reviewer_reject") {
    const pdd_id = Number(data.get("pdd_id"))
    const updateData = {
      status: actionType === "reviewer_approve" ? "approved" : "rejected",
      review_document: ""
    }
    const review_document = data.get("review_document")
    if (review_document) {
      const fileKey = getKey(review_document as File, "review_document")
      await env.R2.put(fileKey, review_document)
      updateData.review_document = fileKey
    }
    try {
      await new PddAssignService(env.DB).update(pdd_id, updateData)
    } catch (error) {
      return { error: true }
    }
  }

  if (actionType === "request_issuing_loa") {
    const assignees = data.getAll("request_from")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      pdd_id: Number(id),
      user_ids: assigneesTypeNumber
    }
    const result = await new RequestLOAService(env.DB).create(newAssignData)

    if (result.length > 0) {
      await projectService.update(projectID, {
        status: EProjectStatus.PendingIssuingLOARequest
      })
    } else {
      console.error("No reviewers were successfully assigned.")
    }
  }
  if (actionType === "approve" || actionType === "reject") {
    try {
      const updateData = {
        status:
          actionType === "approve"
            ? EProjectStatus.Approved
            : EProjectStatus.Rejected,
        review_pdd_document: ""
      }
      const review_pdd_document = data.get("review_pdd_document")
      if (review_pdd_document) {
        const fileKey = getKey(
          review_pdd_document as File,
          "review_pdd_document"
        )
        await env.R2.put(fileKey, review_pdd_document)
        updateData.review_pdd_document = fileKey
      }
      const reason = data.get("reason")
      if (reason) {
        updateData.reject_reason = reason
      }
      await new PDDService(env.DB).update(Number(id), updateData)
      await projectService.update(projectID, {
        status:
          actionType === "approve"
            ? EProjectStatus.SignedLOA
            : EProjectStatus.RejectedLOARequestModification
      })
    } catch (error) {
      return { error: true }
    }
  }
  return json({ success: true })
}

const AdminViewPDD = () => {
  const {
    pdd,
    user,
    internalReviewerList,
    externalAuditorList,
    PDDAssignList,
    coordinatorList
  } = useLoaderData<typeof loader>()

  const pddAssignInternalReviewer = PDDAssignList?.filter(
    (item: any) => item?.isInternalReviewer
  )

  const pddAssignExternalAuditor = PDDAssignList?.filter(
    (item: any) => !item?.isInternalReviewer
  )

  const pddByUser = PDDAssignList.filter(
    (item: any) => item?.user_id === user?.id
  )

  const { openDialog } = useDialog()

  const AssignComponent = ({
    isInternalReviewer
  }: {
    isInternalReviewer: any
  }) => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={
            isInternalReviewer ? internalReviewerList : externalAuditorList
          }
          label="Assigned to"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>
        <Input required type="file" name="support_document" />
        <input type="hidden" name="id" value={pdd?.id} />
        <input
          type="hidden"
          name="isInternalReviewer"
          value={isInternalReviewer}
        />
        <input type="hidden" name="action" value="assign" />
        {Array.from(values).map((value, index) => (
          <input name="assign" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }

  const handleAssign = ({
    isInternalReviewer
  }: {
    isInternalReviewer?: any
  }) => {
    openDialog({
      title: "Assign",
      component: <AssignComponent isInternalReviewer={isInternalReviewer} />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/pdd-reports/${pdd?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: "Assign"
      }
    })
  }

  const RequestLOAComponent = () => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={coordinatorList}
          label="Request Issuing LOA from"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>
        <input
          type="hidden"
          name="project_id"
          value={Number(pdd?.project_id)}
        />
        <input type="hidden" name="action" value="request_issuing_loa" />
        {Array.from(values).map((value, index) => (
          <input name="request_from" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }
  const onRequestLOA = () => {
    openDialog({
      title: "Request LOA",
      component: <RequestLOAComponent />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/pdd-reports/${pdd?.id}`,
          encType: "multipart/form-data"
        }
      }
    })
  }

  const onApproveRejectPDD = ({ approve }: { approve: boolean }) => {
    const approveRejectLabel = approve ? "Approve" : "Reject"
    openDialog({
      title: approveRejectLabel,
      component: (
        <>
          {approve ? (
            <>
              <DownloadButton
                projectID={Number(pdd?.project?.id)}
                projectName={pdd?.project?.name ?? ""}
                type="letter-of-authorization"
              />
              <Input required type="file" name="review_pdd_document" />
            </>
          ) : (
            <>
              <Input name="reason" label="Reason" placeholder="Enter reason" />
            </>
          )}

          <input
            type="hidden"
            name="project_id"
            value={pdd?.project?.id}></input>
          <input
            type="hidden"
            name="action"
            value={approve ? "approve" : "reject"}></input>
        </>
      ),
      submitOption: {
        options: {
          method: "post",
          action: `/admin/pdd-reports/${pdd?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: approveRejectLabel
      }
    })
  }

  return (
    <>
      <Forehead title="PDD Report Detail" backref={`/admin/pdd-reports`} />
      <PDDDetail pdd={pdd} />
      {user.role && hasPermissions(user.role, [EModules.ReviewPDD]) && (
        <div className="flex justify-end">
          <Form method="POST">
            <input type="hidden" name="action" value="review"></input>
            {pdd?.project_id && (
              <input type="hidden" name="project_id" value={pdd?.project_id} />
            )}
            {pdd?.project?.status === EProjectStatus.PendingLOARequest && (
              <Button color="primary" type="submit">
                Click To Review
              </Button>
            )}
          </Form>
        </div>
      )}

      {pdd?.project?.status !== EProjectStatus.PendingLOARequest && (
        <>
          {(user.role === ERole.InternalReviewer ||
            user.role === ERole.IndependentAuditor) &&
            pddByUser.length > 0 && (
              <div>
                <Card className="p-4 my-4">
                  <div className=" flex justify-between items-center">
                    <div className="mb-2">
                      {user.role === ERole.InternalReviewer
                        ? "Internal Reviewer"
                        : "External Auditor"}
                    </div>
                  </div>
                  <AssignCard assignList={pddByUser} data={pdd} type="pdd" />
                </Card>
                {pddByUser[0]?.support_doucment && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      Doucment: {pddByUser[0].support_doucment}
                    </div>
                  </>
                )}
              </div>
            )}
          {(user?.role === ERole.Administrator ||
            user?.role === ERole.Coordinator ||
            user?.role === ERole.Secretariat) && (
            <>
              <div>
                <Card className="p-4 my-4">
                  <div className=" flex justify-between items-center">
                    <div>Internal Reviewer</div>
                  </div>
                  {pddAssignInternalReviewer?.length > 0 ? (
                    pddAssignInternalReviewer?.map(
                      (item: any, index: number) => {
                        return (
                          <div key={index}>
                            <div className="bg-gray-200 p-3 grid gap-3 rounded-md my-2">
                              <div className="grid grid-cols-3">
                                <div>Name</div>
                                <div>Status</div>
                                <div>Document</div>
                              </div>
                              <div className="grid grid-cols-3">
                                <div>
                                  {item.user?.firstName} {item?.user?.lastName}
                                </div>
                                <div>{item?.status}</div>
                                <div>
                                  {item?.review_document ? (
                                    <Link
                                      target="_blank"
                                      href={getImage(item?.review_document)}>
                                      Review Document
                                    </Link>
                                  ) : (
                                    "N/A"
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      }
                    )
                  ) : (
                    <>
                      <div className="flex justify-center">
                        {pdd?.project?.status ===
                        EProjectStatus.PendingPDDReview
                          ? "There is no Internal Reviewer Assign. Please, Click button assign below"
                          : "There is no Internal Reviewer Assign"}
                      </div>
                      {pdd?.project?.status ===
                        EProjectStatus.PendingPDDReview && (
                        <div className="flex justify-center mt-3">
                          <Button
                            color="primary"
                            type="submit"
                            className="bg-primary text-white"
                            onPress={() =>
                              handleAssign({
                                isInternalReviewer: true
                              })
                            }>
                            Assign
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </Card>
              </div>
              <div>
                {pddAssignInternalReviewer[0]?.support_document && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      Internal Reviewer Doucment:{" "}
                      <Link
                        target="_blank"
                        href={getImage(
                          pddAssignInternalReviewer[0].support_document
                        )}
                        className="text-black">
                        Support Doucment
                      </Link>
                    </div>
                  </>
                )}
              </div>
              <div>
                <Card className="p-4 my-4">
                  <div className=" flex justify-between items-center">
                    <div>External Auditor</div>
                  </div>
                  {pddAssignExternalAuditor?.length > 0 ? (
                    <>
                      {pddAssignExternalAuditor?.map(
                        (item: any, index: number) => {
                          return (
                            <div key={index}>
                              <div className="bg-gray-200 p-3 grid gap-3 rounded-md my-2">
                                <div className="grid grid-cols-3">
                                  <div>Name</div>
                                  <div>Status</div>
                                  <div>Document</div>
                                </div>
                                <div className="grid grid-cols-3">
                                  <div>
                                    {item.user?.firstName}{" "}
                                    {item?.user?.lastName}
                                  </div>
                                  <div>{item?.status}</div>
                                  <div>
                                    {item?.review_document ? (
                                      <Link
                                        target="_blank"
                                        href={getImage(item?.review_document)}>
                                        Review Document
                                      </Link>
                                    ) : (
                                      "N/A"
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        }
                      )}
                    </>
                  ) : (
                    <>
                      <div className="flex justify-center">
                        {pdd?.project?.status ===
                        EProjectStatus.PendingPDDReview
                          ? "There is no External Auditor Assign, Please Click Button Assign below"
                          : "There is no External Auditor Assign"}
                      </div>
                      {pdd?.project?.status ===
                        EProjectStatus.PendingPDDReview && (
                        <div className="flex justify-center mt-3">
                          <Button
                            color="primary"
                            type="submit"
                            className="bg-primary text-white"
                            onPress={() =>
                              handleAssign({
                                isInternalReviewer: false
                              })
                            }>
                            Assign
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </Card>
              </div>
              <div>
                {pddAssignExternalAuditor[0]?.support_document && (
                  <>
                    <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                      External Auditor Doucment:{" "}
                      <Link
                        target="_blank"
                        href={getImage(
                          pddAssignExternalAuditor[0].support_document
                        )}
                        className="text-black">
                        Support Doucment
                      </Link>
                    </div>
                  </>
                )}
              </div>
            </>
          )}

          {pdd?.status === EStatus.Pending &&
            hasPermissions(user?.role, [
              EModules.ApproveProject,
              EModules.RejectProject
            ]) && (
              <div className="flex">
                <DownloadButton
                  projectID={Number(pdd?.project?.id)}
                  projectName={pdd?.project?.name ?? ""}
                  type="letter-of-authorization"
                />
              </div>
            )}

          {pdd?.project?.status === EProjectStatus.PendingPDDReview &&
            user?.role &&
            hasPermissions(user.role, [EModules.RequestIssuingLOA]) && (
              <div className="flex justify-end">
                <Button
                  color="primary"
                  onPress={() => {
                    onRequestLOA()
                  }}>
                  Request Issuing LNO
                </Button>
              </div>
            )}
          {pdd?.project?.status === EProjectStatus.PendingIssuingLOARequest &&
            user?.role &&
            hasPermissions(user.role, [
              EModules.ApproveProject,
              EModules.RejectProject
            ]) && (
              <div className="flex justify-end space-x-2">
                <Button
                  color="primary"
                  onPress={() => {
                    onApproveRejectPDD({
                      approve: true
                    })
                  }}>
                  Approve
                </Button>
                <Button
                  color="danger"
                  onPress={() => {
                    onApproveRejectPDD({
                      approve: false
                    })
                  }}>
                  Reject
                </Button>
              </div>
            )}
        </>
      )}
    </>
  )
}

export default AdminViewPDD
