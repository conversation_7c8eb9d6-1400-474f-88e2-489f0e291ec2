import { Button, CalendarDate, DateValue } from "@heroui/react"
import { parseDate } from "@internationalized/date"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import { Form, useNavigate, useRouteLoaderData } from "@remix-run/react"
import { useEffect, useState } from "react"
import AddressInfo from "~/components/projectRegistration/AddressInfor"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import FileSelect from "~/components/userRegistration/FileSelect"
import { getKey } from "~/helpers/r2"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const action = data.action
  const env = context.cloudflare.env as CloudflareENV
  const newData = {
    ...data,
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/2`,
    kml_file: ""
  }
  if (data?.kml_file) {
    const fileKey = getKey(data?.kml_file, "kml_file")
    await env.R2.put(fileKey, data?.kml_file)
    newData.kml_file = fileKey
  }
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), newData))

  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/3`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const { pdd, project } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  const [startDate, setStartDate] = useState<CalendarDate | null>()
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)
  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }

  useEffect(() => {
    if (pdd?.start_date) {
      setStartDate(parseDate(pdd.start_date))
    }
    if (pdd?.end_date) {
      setEndDate(parseDate(pdd.end_date))
    }
  }, [pdd])
  const navigate = useNavigate()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid gap-y-2">
        <div>
          <p>Location</p>
        </div>
        <div className="flex gap-x-8">
          <div className="w-[900px]">
            <div className="grid grid-cols-2 gap-y-3 gap-x-3">
              <div className="col-span-2">
                <Input
                  label="Address"
                  placeholder="Enter project address"
                  name="address"
                  defaultValue={project.address}
                />
              </div>
              <div>
                <Input
                  label="Latitude"
                  isRequired
                  placeholder="Enter project latitude"
                  name="lat"
                  defaultValue={project?.lat}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
              <div>
                <Input
                  label="Longitude"
                  isRequired
                  placeholder="Enter project longitude"
                  name="lng"
                  defaultValue={project?.lng}
                  inputMode="numeric"
                  onInput={e => {
                    e.currentTarget.value = e.currentTarget.value.replace(
                      /[^0-9.]/g,
                      ""
                    )
                  }}
                />
              </div>
              <div className="col-span-2">
                <FileSelect
                  label="Area Map (KML)"
                  name="kml_file"
                  acceptFileTypes=".kml"
                  fileNameSelected={project?.kml_file}
                />
              </div>
              <div className="col-span-2">
                <p>Timeline</p>
              </div>
              <div>
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Planned Start Date"
                  name="start_date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  maxValue={endDate}
                />
              </div>
              <div>
                <DatePicker
                  isRequired
                  variant="bordered"
                  label="Expected Completion Date"
                  name="end_date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  minValue={startDate}
                />
              </div>
              <div className="col-span-2 flex gap-x-3">
                <Button
                  className="bg-primary text-white"
                  name="action"
                  value="save-and-continue"
                  type="submit">
                  Save & Continue
                </Button>
                <Button
                  variant="bordered"
                  color="primary"
                  name="action"
                  value="save"
                  type="submit">
                  Save
                </Button>
                <Button
                  className="bg-transparent"
                  onPress={() => {
                    navigate(`/pdds/${pdd?.id}`)
                  }}>
                  Cancel
                </Button>
              </div>
            </div>
          </div>
          <div className="flex-1">
            <AddressInfo />
          </div>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
