import { defer, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import { Suspense } from "react"
import Loading from "~/components/loading"
import ProjectDetail from "~/components/project/ProjectDetail"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = new ProjectService(env.DB).getById(parseInt(id))
  const pdd = await new PDDService(env.DB).getByProjectID(parseInt(id))

  return defer({ project, pdd })
}

const PublicViewProject = () => {
  const { project, pdd } = useLoaderData<typeof loader>()

  return (
    <div className="container mx-auto space-y-4">
      <Suspense fallback={<Loading />}>
        <Await resolve={project}>
          {project => <ProjectDetail project={project} pdd={pdd} />}
        </Await>
      </Suspense>
    </div>
  )
}

export default PublicViewProject
