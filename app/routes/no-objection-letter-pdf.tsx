import { PDFDocument, rgb, StandardFonts } from "pdf-lib"

interface RequestBody {
  date: string
  projectID: number
  projectName: string
}

export const action = async ({ request }: { request: Request }) => {
  const body: RequestBody = await request.json()
  const pdfDoc = await PDFDocument.create()
  const page = pdfDoc.addPage([595, 842]) // A4 size in points (width x height)
  const { width, height } = page.getSize()
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
  const drawText = (
    text: string,
    x: number,
    y: number,
    size = 12,
    color = rgb(0, 0, 0)
  ) => {
    page.drawText(text, { x, y, size, font, color: color })
  }
  drawText("LETTER OF NO OBJECTION", width / 2 - 110, height - 30, 16)
  drawText(body.date, width - 80, height - 70, 10)
  drawText(`Reference number: ${body.projectID}`, 30, height - 100)
  drawText("FROM: Royal Government of Cambodia", 30, height - 140)
  drawText(
    "[Name], Coordinator of the National Authority for Greenhouse Gas Emission Reduction Mechanisms",
    30,
    height - 160,
    10
  )

  drawText(
    `Letter of No Objection related to ${body.projectName}`,
    30,
    height - 200,
    14
  )

  drawText(
    `As the designated responsible entity of the Royal Government of Cambodia, the National Authority for`,
    30,
    height - 240,
    10
  )
  drawText(
    `Greenhouse Gas Emission Reduction Mechanisms hereby informs you that it has No Objection to ${body.projectName}.`,
    30,
    height - 255,
    10
  )

  drawText(
    `${body.projectName} is eligible to apply for the authorization of GHG ERs generated by the project,`,
    30,
    height - 270,
    10
  )

  drawText(
    `pursuant to the provisions and processes outlined in the Article 6 Operations Manual of Cambodia.`,
    30,
    height - 285,
    10
  )

  drawText(
    `This Letter of No Objection does not constitute a guarantee that any GHG ERs generated by the project will`,
    30,
    height - 310,
    10
  )
  drawText(
    `be authorized by the National Authority for international transfer under Article 6 of the Paris Agreement.`,
    30,
    height - 325,
    10
  )

  drawText(
    `The Letter of No-Objection is signed by [Name], Coordinator of the National Authority for Greenhouse Gas Emission`,
    30,
    height - 350,
    10
  )
  drawText(
    `Reduction Mechanisms, Royal Government of Cambodia.`,
    30,
    height - 365,
    10
  )
  drawText(`----------------------`, 30, height - 440)

  drawText("[Name]", 30, height - 460, 10)
  drawText(
    "Coordinator of the National Authority for Greenhouse Gas Emission Reduction Mechanisms",
    30,
    height - 500,
    10
  )
  drawText("Issuing authority:", 30, height - 520, 10)
  drawText("National Council for Sustainable Development", 30, height - 540, 10)
  drawText(
    "3rd floor, Morodok Techo Building, Lot 503, Tonle Bassac, Chamkarmon, Phnom Penh, Cambodia",
    30,
    height - 560,
    10
  )
  drawText("<EMAIL>", 30, height - 580, 10, rgb(0, 0, 1))

  const pdfBytes = await pdfDoc.save()

  return new Response(pdfBytes, {
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": 'attachment; filename="generated.pdf"'
    }
  })
}
