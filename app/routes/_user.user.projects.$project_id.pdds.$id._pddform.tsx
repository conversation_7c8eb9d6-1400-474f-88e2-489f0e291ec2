import { But<PERSON> } from "@heroui/react"
import { json, type LoaderFunctionArgs, redirect } from "@remix-run/cloudflare"
import { Outlet, useNavigate, useParams } from "@remix-run/react"
import { X } from "lucide-react"
import { EProjectStatus } from "~/enums/EProjectStatus"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

export async function loader({ context, params }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const isNew = params.id === "new"

  if (!params.project_id) throw new Response("Not found", { status: 404 })
  const project = await new ProjectService(env.DB).getById(
    Number(params.project_id)
  )
  if (project?.status !== EProjectStatus.SignedLNO) {
    return redirect(`/user/projects/${params.project_id}`)
  }

  if (isNew) {
    return json({ pdd: null })
  } else {
    //Call to get pdd by id but if can not get data then reject or redirect to page 404
    return null
  }
}

export default function PDDForm() {
  const params = useParams()
  const isNew = params.id === "new"
  const navigate = useNavigate()
  return (
    <div className="px-10 space-y-8">
      <div className="flex justify-between">
        <div className="items-center flex">
          <p className="font-bold text-[22px]">PROJECT Document Design (PDD)</p>
        </div>
        <div>
          <Button
            isIconOnly
            className="bg-transparent"
            onPress={() => {
              navigate(`/user/projects/${params.project_id}`)
            }}>
            <X />
          </Button>
        </div>
      </div>
      <Outlet />
    </div>
  )
}
