import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react"
import { j<PERSON>, LoaderFunction<PERSON>rgs, MetaFunction } from "@remix-run/cloudflare"
import { useLoaderData, useNavigate } from "@remix-run/react"
import { and, eq } from "drizzle-orm"
import { Plus, Search } from "lucide-react"
import { useRef } from "react"
import { useTranslation } from "react-i18next"
import DataTable, { createDataTableRow } from "~/components/table/data-table"
import { filterSearch, getListQuery } from "~/components/table/query"
import useListQuery from "~/components/table/use-list-query"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import { users } from "~/db/schema/users"
import { EStatus } from "~/enums/EStatus"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { ProjectFilter } from "./_admin.admin.projects._index"

export const handle = {
  title: "Users"
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export type UserFilter = {
  status?: EStatus
}
export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV

  const { page, limit, search, sort, status } =
    getListQuery<UserFilter>(request)

  const filter = and(
    filterSearch(search, [users.id, users.firstName, users.lastName]),
    status ? eq(users.status, status) : undefined
  )

  const userService = new UserService(env.DB)

  const { total, result } = await userService.paginateTable({
    filter,
    page,
    limit,
    sort
  })
  return json({ result, total })
}

const AdminUsers = () => {
  const { result, total } = useLoaderData<typeof loader>()

  const { t } = useTranslation()
  const navigate = useNavigate()
  const rows = createDataTableRow<typeof result>([
    {
      key: "id",
      headerLabel: "User ID"
    },
    {
      key: "name",
      headerLabel: "User Name",
      renderCell: row => <div>{`${row?.firstName} ${row?.lastName}`}</div>,
      allowSorting: true
    },
    {
      key: "email",
      headerLabel: "Email"
    },
    {
      key: "role",
      headerLabel: "User Role"
    },
    {
      key: "status",
      headerLabel: "Status",
      renderCell: row => (
        <div
          className={`capitalize px-2 py-1 rounded-full w-fit min-w-44 text-end
            ${
              row.status === EStatus.Reject
                ? "text-red-500 font-bold"
                : "text-green-500 font-bold"
            }`}>
          {row.status}
        </div>
      )
    }
  ])
  const searchRef = useRef<HTMLInputElement>(null)

  const { query, updateQuery, sortDescriptor, handleSortChange } =
    useListQuery<ProjectFilter>()

  return (
    <div className="grid grid-cols-1 gap-y-3">
      <div className="flex space-x-3 justify-end mb-5">
        <div className="w-80">
          <Select
            onChange={e => {
              updateQuery({ status: e.target.value as EStatus, page: 1 })
            }}
            placeholder="Status"
            selectedKeys={[query.status]}
            data={Object.values(EStatus).map(item => ({
              value: item,
              label: item,
              key: item
            }))}
          />
        </div>
        <div>
          <div className="space-x-3 flex items-center">
            <Input
              ref={searchRef}
              defaultValue={query.search as string}
              placeholder="Search ..."
              endContent={
                <div>
                  <Button
                    variant="flat"
                    className="bg-transparent hover:bg-transparent"
                    isIconOnly
                    onPress={() => {
                      updateQuery({
                        search: searchRef.current?.value ?? "",
                        page: 1
                      })
                    }}>
                    <Search
                      onClick={() => {
                        updateQuery({
                          search: searchRef.current?.value ?? "",
                          page: 1
                        })
                      }}
                    />
                  </Button>
                </div>
              }></Input>
          </div>
        </div>
        <Button
          className="bg-primary text-white h-full"
          startContent={<Plus />}
          onPress={() => navigate("/admin/users/new")}>
          Add NewUser
        </Button>
      </div>
      <div>
        <DataTable
          data={result}
          rows={rows}
          onSortChange={handleSortChange}
          sortDescriptor={sortDescriptor}
          onRowClick={id => {
            navigate(`/admin/users/${id}`)
          }}
        />
      </div>
      <div className="mt-2"></div>
      {total > 0 && (
        <div className="flex justify-center">
          <Pagination
            showControls
            page={Number(query.page)}
            total={Math.ceil(total / Number(query.limit))}
            onChange={page => {
              updateQuery({ page })
            }}
          />
        </div>
      )}
    </div>
  )
}
export default AdminUsers
