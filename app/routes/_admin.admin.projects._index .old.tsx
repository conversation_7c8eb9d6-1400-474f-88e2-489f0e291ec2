import { But<PERSON> } from "@heroui/react"
import { defer, LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare"
import { Await, useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { Eye, PenBoxIcon, Trash2 } from "lucide-react"
import { Suspense } from "react"
import Loading from "~/components/loading"
import Pagination from "~/components/pagination"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export const handle = {
  title: "Projects"
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const url = new URL(request.url)
  const page = Number(url.searchParams.get("page") || "1")
  const user = await getAuthUser(context, request)
  const res = new ProjectService(env.DB).getMany({
    page,
    pageSize: 25,
    user: user
  })
  return defer({ url, res })
}

export default function AdminProjects() {
  const { url, res } = useLoaderData<typeof loader>()
  const navigate = useNavigate()
  return (
    <>
      <Suspense fallback={<Loading />}>
        <Await resolve={res}>
          {res => (
            <>
              {res.data.length > 0 && (
                <>
                  <table className="table table-auto">
                    <thead className="bg-base-100 uppercase">
                      <tr>
                        <th>Id</th>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {res.data.map((item, index) => (
                        <tr key={index} className="hover">
                          <td>{item.id}</td>
                          <td>{item.name}</td>
                          <td>{item.status}</td>
                          <td>
                            {dayjs(item.created_at).format("YYYY-MM-DD hh:mm")}
                          </td>
                          <td>
                            <div className="flex gap-1">
                              <Button
                                onPress={() => {
                                  navigate(`/admin/projects/${item.id}`)
                                }}
                                className="p-0 w-[7px]"
                                isIconOnly
                                color="default">
                                <Eye size={16} />
                              </Button>

                              <Button
                                onPress={() => {
                                  navigate(`/projects/${item.id}/edit`)
                                }}
                                className="p-0 w-[7px]"
                                isIconOnly
                                color="default">
                                <PenBoxIcon size={16} />
                              </Button>
                              <Button
                                onPress={() => {
                                  navigate(`/projects/${item.id}/delete`)
                                }}
                                className="p-0 w-[7px]"
                                isIconOnly
                                color="default">
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {res.paginate.pageCount > 1 && (
                    <div className="mt-10">
                      <Pagination pageUrl={url.toString()} {...res.paginate} />
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}
