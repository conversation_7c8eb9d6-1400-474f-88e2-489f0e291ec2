import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const action = data?.action
  const env = context.cloudflare.env as CloudflareENV
  const newDate = {
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/3`
  }
  await new PDDService(env.DB).update(parseInt(params.pid), newDate)
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/4`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const navigate = useNavigate()
  const params = useParams()
  const { org } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )

  return (
    <div className="flex gap-x-8">
      <Form method="POST">
        <div className="w-[900px] grid gap-y-3">
          <div className="grid grid-cols-3 gap-x-3">
            <div className="grid col-span-2 ">
              <Input
                label="Organization Name"
                isRequired
                isDisabled
                placeholder="Enter your organization name"
                defaultValue={org?.name}
              />
            </div>
            <div>
              <Select
                label="Type"
                placeholder="Select Type of Organization"
                isDisabled
                data={[
                  { key: "1", label: "1" },
                  { key: "2", label: "2" }
                ]}
              />
            </div>
          </div>
          <div className="grid grid-cols-3 gap-x-3">
            <div className="grid col-span-2">
              <Input
                label="Address"
                isRequired
                placeholder="Enter project address"
                isDisabled
                defaultValue={org?.address}
              />
            </div>
            <div>
              <Select
                isDisabled
                label="Province/City"
                placeholder="Select Province/City"
                data={[
                  { key: "kompong cham", label: "Kompong Cham" },
                  { key: "phnom penh", label: "Phnom Penh" }
                ]}
              />
            </div>
          </div>
          <div>
            <Input
              isDisabled
              label="Contact Person Name"
              isRequired
              placeholder="Enter your organization contact person full name"
            />
          </div>
          <div className="grid grid-cols-2 gap-x-3">
            <div>
              <Input
                isDisabled
                label="Contact Email"
                isRequired
                placeholder="Enter your organization email"
                defaultValue={org?.email}
              />
            </div>
            <div>
              <Input
                isDisabled
                label="Contact Number"
                isRequired
                placeholder="Enter your organization number"
                defaultValue={org?.phone}
              />
            </div>
          </div>
          <div>
            <Input
              isDisabled
              label="Website"
              isRequired
              placeholder="Enter your organization website"
              defaultValue={org?.website}
            />
          </div>
          <div className="flex gap-x-3">
            <Button
              className="bg-primary text-white"
              name="action"
              value="save-and-continue"
              type="submit"
              onPress={() => {
                navigate(
                  `/user/projects/${params.id}/pdds/${params.pid}/project-information/4`
                )
              }}>
              Save & Continue
            </Button>
            <Button
              variant="bordered"
              color="primary"
              name="action"
              value="save"
              type="submit">
              Save
            </Button>
            <Button
              className="bg-transparent"
              onPress={() => {
                navigate(`/pdds/${params.pid}`)
              }}>
              Cancel
            </Button>
          </div>
        </div>
      </Form>
      <div>
        <NoteInfo />
      </div>
    </div>
  )
}

export default ProjectInformation
