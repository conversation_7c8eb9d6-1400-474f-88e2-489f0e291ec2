import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import TextArea from "~/components/ui/textArea"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const data = Object.fromEntries(formData)

  const action = data?.action

  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), {
      ...data,
      step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/8`
    }))

  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/9`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const params = useParams()
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  const navigate = useNavigate()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid w-full gap-y-3">
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2">
                <div className="col-span-2">
                  <TextArea
                    label="Environmental Impact Assessment (EIA)"
                    placeholder="Summarize the key findings and attach the full report."
                    name="environmental_impact_assessment"
                    minRows={5}
                    defaultValue={pdd?.environmental_impact_assessment}
                  />
                </div>
                <div className="col-span-2">
                  <TextArea
                    label="Social Impact Assessment (SIA)"
                    placeholder="Summarize the full assessment."
                    name="social_impact_assessment"
                    minRows={5}
                    defaultValue={pdd?.social_impact_assessment}
                  />
                </div>
                <div className="col-span-2">
                  <TextArea
                    label="Risk Mitigation Measures"
                    placeholder="Describe mitigation measures for environmental and social risks."
                    name="risk_mitigation_measures"
                    minRows={5}
                    defaultValue={pdd?.risk_mitigation_measures}
                  />
                </div>
              </div>
            </div>
            <div className="flex-1">
              <NoteInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px] flex gap-x-3">
          <Button
            className="bg-primary text-white"
            name="action"
            value="save-and-continue"
            type="submit">
            Save & Continue
          </Button>
          <Button
            variant="bordered"
            color="primary"
            name="action"
            value="save"
            type="submit">
            Save
          </Button>
          <Button
            className="bg-transparent"
            onPress={() => {
              navigate(`/pdds/${params.pid}`)
            }}>
            Cancel
          </Button>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
