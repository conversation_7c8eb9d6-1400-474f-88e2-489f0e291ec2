import { But<PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import { getKey } from "~/helpers/r2"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const action = data?.action
  const env = context.cloudflare.env as CloudflareENV
  const newData = {
    ...data,
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/5`
  }
  if (data?.baseline_emission_methodology_file) {
    const fileKey = getKey(
      data?.baseline_emission_methodology_file,
      "baseline_emission_methodology_file"
    )
    await env.R2.put(fileKey, data?.baseline_emission_methodology_file)
    newData.baseline_emission_methodology_file = fileKey
  }
  if (data?.baseline_emission_data_file) {
    const fileKey = getKey(
      data?.baseline_emission_data_file,
      "baseline_emission_data_file"
    )
    await env.R2.put(fileKey, data?.baseline_emission_data_file)
    newData.baseline_emission_data_file = fileKey
  }
  if (data?.carbon_stock_assessment_file) {
    const fileKey = getKey(
      data?.carbon_stock_assessment_file,
      "carbon_stock_assessment_file"
    )
    await env.R2.put(fileKey, data?.carbon_stock_assessment_file)
    newData.carbon_stock_assessment_file = fileKey
  }
  if (data?.leakage_assessment_file) {
    const fileKey = getKey(
      data?.leakage_assessment_file,
      "leakage_assessment_file"
    )
    await env.R2.put(fileKey, data?.leakage_assessment_file)
    newData.leakage_assessment_file = fileKey
  }
  if (data?.additionality_assessment_file) {
    const fileKey = getKey(
      data?.additionality_assessment_file,
      "additionality_assessment_file"
    )
    await env.R2.put(fileKey, data?.additionality_assessment_file)
    newData.additionality_assessment_file = fileKey
  }
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), newData))
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/6`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const params = useParams()
  const navigate = useNavigate()
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  return (
    <Form method="post" encType="multipart/form-data">
      <div className="flex gap-x-8">
        <div className="grid grid-cols-2 w-[900px] gap-y-3 gap-x-3">
          <div className="col-span-2">
            <Input
              label="Baseline Emission Factor (tCO₂e/year)"
              name="baseline_emission_factor"
              placeholder="tCO₂e/year"
              defaultValue={pdd?.baseline_emission_factor}
              inputMode="numeric"
              onInput={e => {
                e.currentTarget.value = e.currentTarget.value.replace(
                  /[^0-9.]/g,
                  ""
                )
              }}
            />
          </div>
          <div className="col-span-2">Projected Emission Reductions</div>
          <div>
            <Input
              label="Annual Emission Reductions"
              placeholder="tCO₂e/year"
              name="annual_emission_reduction"
              defaultValue={pdd?.annual_emission_reduction}
              inputMode="numeric"
              onInput={e => {
                e.currentTarget.value = e.currentTarget.value.replace(
                  /[^0-9.]/g,
                  ""
                )
              }}
            />
          </div>
          <div>
            <Input
              label="Lifetime Emission Reductions"
              placeholder="tCO₂e"
              name="lifetime_emission_reduction"
              defaultValue={pdd?.lifetime_emission_reduction}
              inputMode="numeric"
              onInput={e => {
                e.currentTarget.value = e.currentTarget.value.replace(
                  /[^0-9.]/g,
                  ""
                )
              }}
            />
          </div>
          <div className="col-span-2">
            <TextArea
              label="Leakage Assessment"
              placeholder="Provide an analysis of potential leakage and mitigation measures."
              name="leakage_assessment"
              defaultValue={pdd?.leakage_assessment}
            />
          </div>
          <div className="col-span-2">
            <TextArea
              label="Additionally Demonstration"
              placeholder="Explain how the project goes beyond business-as-usual scenarios (financial, regulatory, or technological barriers)."
              name="additionally_demonstration"
              defaultValue={pdd?.additionally_demonstration}
            />
          </div>
          <div className="col-span-2 grid gap-y-3">
            <div>Supporting Documents</div>
            <div>
              <FileSelect
                label="Baseline Emission Methodology"
                name="baseline_emission_methodology_file"
                fileNameSelected={pdd?.baseline_emission_methodology_file}
                acceptFileTypes=".pdf"
              />
            </div>
            <div>
              <FileSelect
                label="Baseline Emission Data"
                name="baseline_emission_data_file"
                fileNameSelected={pdd?.baseline_emission_data_file}
                acceptFileTypes=".pdf"
              />
            </div>
            <div>
              <FileSelect
                label="Carbon Stock Assessment"
                name="carbon_stock_assessment_file"
                fileNameSelected={pdd?.carbon_stock_assessment_file}
                acceptFileTypes=".pdf"
              />
            </div>
            <div>
              <FileSelect
                label="Leakage Assessment"
                name="leakage_assessment_file"
                fileNameSelected={pdd?.leakage_assessment_file}
                acceptFileTypes=".pdf"
              />
            </div>
            <div>
              <FileSelect
                label="Additionality Assessment"
                name="additionality_assessment_file"
                fileNameSelected={pdd?.additionality_assessment_file}
                acceptFileTypes=".pdf"
              />
            </div>
            <div className="flex gap-x-3">
              <Button
                className="bg-primary text-white"
                name="action"
                value="save-and-continue"
                type="submit">
                Save & Continue
              </Button>
              <Button
                variant="bordered"
                color="primary"
                name="action"
                value="save"
                type="submit">
                Save
              </Button>
              <Button
                className="bg-transparent"
                onPress={() => {
                  navigate(`/pdds/${params.pid}`)
                }}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
        <div>
          <NoteInfo />
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
