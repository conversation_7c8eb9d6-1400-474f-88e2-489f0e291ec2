import {
  Button,
  CalendarDate,
  Card,
  CardBody,
  Checkbox,
  DateValue
} from "@heroui/react"
import { parseDate } from "@internationalized/date"
import { type ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useLoaderData,
  useNavigate,
  useNavigation,
  useParams
} from "@remix-run/react"
import { useEffect, useState } from "react"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import { EProjectStatus } from "~/enums/EProjectStatus"
import OrganizationService from "~/services/db/organization"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  await new ProjectService(env.DB).update(Number(params.id), {
    status: EProjectStatus.PendingLOARequest
  })
  const PDDSubmit = await new PDDService(env.DB).update(parseInt(params?.pid), {
    status: EProjectStatus.PendingPDDReview
  })
  if (PDDSubmit[0].id) {
    return redirect(
      `/user/projects/${params.id}/pdds/${PDDSubmit[0].id}/loa-request-sucess`
    )
  }
  return json({ error: true })
}

export async function loader({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const org = await new OrganizationService(env.DB).getById(user.org_id)
  const project = await new ProjectService(env.DB).getById(parseInt(params.id))
  const pdd = await new PDDService(env.DB).getById(Number(params.pid))
  return json({ org, user, pdd, project })
}

const LOARequest = () => {
  const navigate = useNavigate()
  const [isConfirm, setIsConfirm] = useState(false)
  const param = useParams()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting"
  const { org, user, pdd, project } = useLoaderData<typeof loader>()
  const [startDate, setStartDate] = useState<CalendarDate | null>()
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)
  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }

  useEffect(() => {
    if (pdd?.start_date) {
      setStartDate(parseDate(pdd.start_date))
    }
    if (pdd?.end_date) {
      setEndDate(parseDate(pdd.end_date))
    }
  }, [pdd])

  return (
    <Form method="post">
      <div className=" container max-w-3xl mx-auto space-y-8 py-8">
        <div className="grid gap-y-1">
          <div>
            <p className="text-lg font-bold text-center">
              Confirm Your Submission
            </p>
          </div>
          <div className="text-center">
            <p>Request for the Letter of Authorization (LOA)</p>
          </div>
        </div>
        <div className="grid gap-y-3">
          <div>
            <Card className="">
              <CardBody className="p-8">
                <div className="grid gap-y-4">
                  <div className="grid gap-y-2">
                    <div>Project Detail</div>
                    <div className="px-6">
                      <ul className="list-disc">
                        <li>Project Name: {pdd?.project?.name}</li>
                        <li>Sector: {project?.sector?.name}</li>
                        <li>Location: {pdd?.address}</li>
                        <li>
                          Total Area: {project?.ideaNote?.total_area} hectares
                        </li>
                        <li>
                          Estimated Reductions:{" "}
                          {project?.ideaNote?.estimated_er} tCO₂e/year
                        </li>{" "}
                      </ul>
                    </div>
                  </div>
                  <div className="grid gap-y-2">
                    <div>Organization Details</div>
                    <div className="px-6">
                      <ul className="list-disc">
                        <li>Organization: {org?.name}</li>
                        <li>
                          Contact Person: {user?.firstName} {user?.lastName}
                          &nbsp;, &nbsp;{user?.email} &nbsp;, &nbsp;
                          {user?.phone}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
          <div className="p-8 bg-[#EDECEC] rounded-2xl">
            <div className="grid gap-y-1">
              <div>
                <p className="font-bold text-[18px]">
                  False Information Warning
                </p>
              </div>
              <div>
                Misleading or incomplete data may lead to rejection of your
                request.
              </div>
            </div>
          </div>
          <div>
            <TextArea
              label="Note"
              placeholder="Enter additional note or summary of this submission"></TextArea>
          </div>
          <div>
            <p>Authorized Representative</p>
          </div>
          <div>
            <div>
              <Card className="">
                <CardBody className="p-4">
                  <div className="grid gap-y-4 grid-cols-2 gap-x-4">
                    <div>
                      <Input
                        label="Name"
                        defaultValue={project?.name ?? ""}
                        isRequired
                        placeholder="Full legal name"
                        name="name"
                      />
                    </div>
                    <div>
                      <Input
                        label="Title"
                        defaultValue={project?.name ?? ""}
                        isRequired
                        placeholder="Role within organization"
                        name="role"
                      />
                    </div>
                    <div>
                      <DatePicker
                        isRequired
                        variant="bordered"
                        label="Planned Start Date"
                        name="start_date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        maxValue={endDate}
                      />
                    </div>
                    <div>
                      <DatePicker
                        isRequired
                        variant="bordered"
                        label="Planned Start Date"
                        name="start_date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        minValue={startDate}
                      />
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
          <div>
            <Checkbox
              isSelected={isConfirm}
              color="primary"
              onChange={() => {
                setIsConfirm(prev => !prev)
              }}>
              I confirm that all information provided is complete and accurate.
            </Checkbox>
          </div>
        </div>
        <div className="flex justify-center gap-x-2">
          <div>
            <Button
              color="primary"
              isDisabled={!isConfirm}
              type="submit"
              isLoading={isSubmitting}>
              Confirm & Submit
            </Button>
          </div>
          <div>
            <Button
              className="bg-transparent"
              isDisabled={isSubmitting}
              onPress={() => {
                navigate(`/pdds/${param.pid}`)
              }}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </Form>
  )
}

export default LOARequest
