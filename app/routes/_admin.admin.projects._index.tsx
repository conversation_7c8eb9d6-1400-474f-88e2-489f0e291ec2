import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react"
import { j<PERSON>, LoaderFunction<PERSON>rgs, MetaFunction } from "@remix-run/cloudflare"
import { useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { and, eq, ne } from "drizzle-orm"
import { Search } from "lucide-react"
import { useRef } from "react"
import DataTable, { createDataTableRow } from "~/components/table/data-table"
import { filterSearch, getListQuery } from "~/components/table/query"
import useListQuery from "~/components/table/use-list-query"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import { projects } from "~/db/schema/projects"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EStatus } from "~/enums/EStatus"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import projectSectors from "~/utils/projectSectors"
import { getAuthUser } from "~/utils/user"

export const handle = {
  title: "Projects"
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export type ProjectFilter = {
  status?: EProjectStatus | EStatus
  sector?: string //Sector Id
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV

  const user = await getAuthUser(context, request)

  const { page, limit, search, sort, sector, status } =
    getListQuery<ProjectFilter>(request)

  const filter = and(
    filterSearch(search, [projects.id, projects.name]),
    ne(projects.status, "draft"),
    status ? eq(projects.status, status) : undefined,
    sector ? eq(projects.sector_id, Number(sector)) : undefined
  )

  const projectService = new ProjectService(env.DB)

  const { total, result } = await projectService.paginateTable({
    filter,
    page,
    limit,
    sort,
    user
  })
  return json({ result, total })
}

export default function AdminProjects() {
  const { result, total } = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  const rows = createDataTableRow<typeof result>([
    {
      key: "id",
      headerLabel: "project ID"
    },
    {
      key: "name",
      headerLabel: "Project Title"
    },
    {
      key: "sector",
      headerLabel: "Project Sector",
      renderCell: row => <div>{row.sector?.name}</div>
    },
    {
      key: "created_at",
      headerLabel: "Registration Date",
      renderCell: row => (
        <div>{dayjs(row.created_at).format("DD MMM YYYY")}</div>
      ),
      allowSorting: true
    },
    {
      key: "project_period",
      headerLabel: "Project Period",
      renderCell: row => (
        <div>
          {dayjs(row.ideaNote?.start_date).format("DD MMM YYYY")}&nbsp;-&nbsp;
          {dayjs(row.ideaNote?.end_date).format("DD MMM YYYY")}
        </div>
      )
    },
    {
      key: "status",
      headerLabel: "Status",
      renderCell: row => (
        <div
          className={`capitalize px-2 py-1 rounded-full w-fit  min-w-44 text-end
            ${
              row.status === "rejected"
                ? "text-red-500 font-bold"
                : "text-green-500 font-bold"
            }`}>
          {row.status}
        </div>
      )
    }
  ])

  const searchRef = useRef<HTMLInputElement>(null)

  const { query, updateQuery, sortDescriptor, handleSortChange } =
    useListQuery<ProjectFilter>()

  const handleSearch = () => {
    if (searchRef.current) {
      updateQuery({
        search: searchRef.current.value ?? "",
        page: 1
      })
    }
  }

  return (
    <>
      <div className="flex space-x-3 justify-end mb-5">
        <div className="w-80">
          <Select
            onChange={e => {
              updateQuery({ status: e.target.value as EProjectStatus, page: 1 })
            }}
            placeholder="Status"
            selectedKeys={[query.status]}
            data={Object.values(EProjectStatus).map(item => ({
              value: item,
              label: item,
              key: item
            }))}
          />
        </div>

        <div className="w-80">
          <Select
            onChange={e => {
              updateQuery({ sector: e.target.value as EProjectStatus, page: 1 })
            }}
            placeholder="Project Sector"
            selectedKeys={[query.sector].toString()}
            data={projectSectors.map(item => ({
              value: item.name,
              label: item.name,
              key: item.key
            }))}
          />
        </div>
        <div>
          <div className="space-x-3 flex items-center">
            <Input
              ref={searchRef}
              defaultValue={query.search as string}
              placeholder="Search ..."
              onKeyDown={e => {
                if (e.key === "Enter") {
                  handleSearch()
                }
              }}
              endContent={
                <div>
                  <Button
                    variant="flat"
                    className="bg-transparent hover:bg-transparent"
                    isIconOnly
                    onPress={handleSearch}>
                    <Search
                      onClick={() => {
                        updateQuery({
                          search: searchRef.current?.value ?? "",
                          page: 1
                        })
                      }}
                    />
                  </Button>
                </div>
              }></Input>
          </div>
        </div>
      </div>
      <DataTable
        data={result}
        rows={rows}
        onSortChange={handleSortChange}
        sortDescriptor={sortDescriptor}
        onRowClick={id => {
          navigate(`/admin/projects/${id}`)
        }}
      />
      <div className="mt-2"></div>
      {total > 0 && (
        <div className="flex justify-center">
          <Pagination
            showControls
            page={Number(query.page)}
            total={Math.ceil(total / Number(query.limit))}
            onChange={page => {
              updateQuery({ page })
            }}
          />
        </div>
      )}
    </>
  )
}
