import { type ActionFunctionArgs, json } from "@remix-run/cloudflare"
import CommentService from "~/services/db/comment"
import type { CloudflareENV } from "~/types"
import { CommentValidator } from "~/validators/comment"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new CommentValidator(env.DB)
  const { data, error } = await validator.validateCreate(payload)
  try {
    const comment = await new CommentService(env.DB).create(data)

    if (!comment.length) {
      return json({ error: "Something went wrong", comment: null })
    }
    return json({ success: true })
  } catch (error) {
    console.log("Error", error)
  }
}
