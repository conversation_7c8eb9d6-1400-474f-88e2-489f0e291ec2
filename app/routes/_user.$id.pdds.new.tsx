import {
  <PERSON><PERSON>,
  CalendarDate,
  DateValue,
  Radio,
  RadioGroup
} from "@heroui/react"
import { type ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import { Form } from "@remix-run/react"
import { useState } from "react"
import Forehead from "~/components/layout/admin/forehead"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { typeOfCarbon, useOfGhgErs } from "~/lib/performanceDetail"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const formData = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const data = Object.fromEntries(formData)
  const newData = {
    ...data,
    project_id: id
  }
  const result = await new PDDService(env.DB).create(newData)
  const updateProjectStuatus = await new ProjectService(env.DB).update(
    Number(id),
    {
      status: EProjectStatus.PendingLOARequest
    }
  )
  if (!result?.length) {
    return json({ error: "Something went wrong", saved: null })
  }
  return redirect(`/pdds/${result[0].id}`)
}

export default function NewPDD() {
  const [startDate, setStartDate] = useState<CalendarDate | null>(null)
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)

  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }

  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }

  const [selectedTypeOfCarbon, setSelectedTypeOfCarbon] = useState<
    string | null
  >(null)

  const [otherTypeOfCarbon, setOtherTypeOfCarbon] = useState("")

  const [selectedUseOfGhgErs, setSelectedUseOfGhgErs] = useState<string | null>(
    null
  )
  return (
    <>
      <Forehead title="Add PDD Report" />
      <Form method="post" encType="multipart/form-data">
        <div className="container max-w-3xl mx-auto space-y-6 px-5">
          <div>
            <p className="text-[22px] text-black">PDD FORM</p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <TextArea
                isRequired
                label={"positive list of GHG ER"}
                name="positive_list"
                placeholder="Enter positive list of GHG ER"
              />
            </div>
            <div className="col-span-2">
              <Input
                label={"Share of GHG ERs"}
                name="share_ghg_ers"
                placeholder="Enter Share of GHG ERs"
                endContent={"%"}
              />
            </div>
            <div>
              <DatePicker
                isRequired
                variant="bordered"
                label="Start Date"
                name="start_date"
                value={startDate}
                onChange={handleStartDateChange}
                maxValue={endDate}
              />
            </div>
            <div>
              <DatePicker
                isRequired
                variant="bordered"
                label="End Date"
                name="end_date"
                value={endDate}
                onChange={handleEndDateChange}
                minValue={startDate}
              />
            </div>
            <div>
              <RadioGroup
                label={"Type of carbon mechanism"}
                isRequired
                color="primary"
                name="type_of_carbon_mechanism"
                value={selectedTypeOfCarbon}
                onValueChange={setSelectedTypeOfCarbon}
                classNames={{
                  label: "text-lg font-medium"
                }}>
                {typeOfCarbon.map(item => (
                  <Radio key={item.id} value={String(item.id)}>
                    {item.name}
                  </Radio>
                ))}
              </RadioGroup>
              {selectedTypeOfCarbon === "other" && (
                <Input
                  variant="underlined"
                  isRequired
                  name="other_type_of_carbon_mechanism"
                  label={"Other carbon mechanism name"}
                  value={otherTypeOfCarbon}
                  onChange={e => setOtherTypeOfCarbon(e.target.value)}
                  placeholder="Enter other sector"></Input>
              )}
            </div>
            <div>
              <RadioGroup
                isRequired
                label={"Use of GHG ERs"}
                color="primary"
                name="user_of_ghg_ers"
                value={selectedUseOfGhgErs}
                onValueChange={setSelectedUseOfGhgErs}
                classNames={{
                  label: "text-lg font-medium"
                }}>
                {useOfGhgErs.map(item => (
                  <Radio key={item.id} value={String(item.id)}>
                    {item.name}
                  </Radio>
                ))}
              </RadioGroup>
            </div>
            <div className="col-span-2">
              <TextArea
                minRows={9}
                isRequired
                label={"Environmental Integrity Detail"}
                name="environmental_integrity_detail"
                placeholder="Enter Environmental Integrity Detail"
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit" color="primary">
              Submit
            </Button>
          </div>
        </div>
      </Form>
    </>
  )
}
