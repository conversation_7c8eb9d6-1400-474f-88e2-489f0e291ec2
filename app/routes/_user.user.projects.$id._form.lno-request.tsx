import { <PERSON><PERSON>, <PERSON>, CardBody, Checkbox } from "@heroui/react"
import { type ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useLoaderData,
  useNavigate,
  useNavigation,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import { useState } from "react"
import TextArea from "~/components/ui/textArea"
import { ENotification } from "~/enums/ENotification"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { Project_STEPS } from "~/enums/EProjectStep"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"
import IdeaNoteService from "~/services/db/ideaNote"
import NotificationService from "~/services/db/notification"
import OrganizationService from "~/services/db/organization"
import ProjectService from "~/services/db/project"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const project = await new ProjectService(env.DB).update(Number(params.id), {
    status: EProjectStatus.PendingLnoRequest
  })
  const url = new URL(request.url)
  const host = url.origin
  const link = `${host}/admin/projects/${params.id}`
  if (project) {
    const administrators = await new UserService(env.DB).getByRole(
      ERole.Administrator
    )
    project?.idea_note_id &&
      (await new IdeaNoteService(env.DB).update(project?.idea_note_id, {
        status: EStatus.Pending
      }))
    if (administrators) {
      for (const administrator of administrators) {
        try {
          await new MailService(env).NewProjectSubmitEmailNotification(
            // administrator?.email,
            env.RESEND_CONTACT_TO_EMAIL,
            link
          )
          await new NotificationService(env.DB).create({
            type: ENotification.Project,
            user_id: administrator.id,
            data: params.id,
            link: `/admin/projects/${params.id}`
          })
        } catch (error) {
          console.error("Error sending email:", error)
        }
      }
    }
    return redirect(
      `/user/projects/${params.id}/${Project_STEPS.LNOREQUEST}-success`
    )
  }
  return json({ error: true })
}

export async function loader({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const org = await new OrganizationService(env.DB).getById(user.org_id)
  return json({ org, user })
}

const LNORequest = () => {
  const navigate = useNavigate()
  const [isConfirm, setIsConfirm] = useState(false)
  const param = useParams()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting"
  const { project } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id._form"
  )
  const { org, user } = useLoaderData<typeof loader>()

  return (
    <Form method="post">
      <div className=" container max-w-3xl mx-auto space-y-8 py-8">
        <div className="grid gap-y-1">
          <div>
            <p className="text-lg font-bold text-center">
              Confirm Your Submission
            </p>
          </div>
          <div className="text-center">
            <p>Request for the Letter of No Objection (LNO)</p>
          </div>
        </div>
        <div className="grid gap-y-3">
          <div>
            <Card className="">
              <CardBody className="p-8">
                <div className="grid gap-y-4">
                  <div className="grid gap-y-2">
                    <div>Project Detail</div>
                    <div className="px-6">
                      <ul className="list-disc">
                        <li>Project Name: {project?.name}</li>
                        <li>Sector: {project?.sector?.name}</li>
                        <li>Location: {project?.address}</li>
                        <li>
                          Total Area: {project?.ideaNote?.total_area} hectares
                        </li>
                        <li>
                          Estimated Reductions:{" "}
                          {project?.ideaNote?.estimated_er} tCO₂e/year
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="grid gap-y-2">
                    <div>Organization Details</div>
                    <div className="px-6">
                      <ul className="list-disc">
                        <li>Organization: {org?.name}</li>
                        <li>
                          Contact Person: {user?.firstName} {user?.lastName}
                          &nbsp;, &nbsp;{user?.email} &nbsp;, &nbsp;
                          {user?.phone}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
          <div className="p-8 bg-[#EDECEC] rounded-2xl">
            <div className="grid gap-y-1">
              <div>
                <p className="font-bold text-[18px]">
                  False Information Warning
                </p>
              </div>
              <div>
                Misleading or incomplete data may lead to rejection of your
                request.
              </div>
            </div>
          </div>
          <div>
            <TextArea
              label="Note"
              placeholder="Enter additional note or summary of this submission"></TextArea>
          </div>
          <div>
            <Checkbox
              isSelected={isConfirm}
              color="primary"
              onChange={() => {
                setIsConfirm(prev => !prev)
              }}>
              I confirm that all information provided is complete and accurate.
            </Checkbox>
          </div>
        </div>
        <div className="flex justify-center gap-x-2">
          <div>
            <Button
              color="primary"
              isDisabled={!isConfirm}
              type="submit"
              isLoading={isSubmitting}>
              Confirm & Submit
            </Button>
          </div>
          <div>
            <Button
              className="bg-transparent"
              isDisabled={isSubmitting}
              onPress={() => {
                navigate(`/user/projects/${param.id}`)
              }}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </Form>
  )
}

export default LNORequest
