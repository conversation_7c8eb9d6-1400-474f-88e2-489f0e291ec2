import { Image } from "@heroui/image"
import { <PERSON><PERSON>, Checkbox } from "@heroui/react"
import { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare"
import {
  Form,
  json,
  Link,
  useLoaderData,
  useNavigate,
  useNavigation
} from "@remix-run/react"
import { LogIn } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import Logo from "~/components/logo"
import Title from "~/components/title"
import Input from "~/components/ui/input"
import InputPassword from "~/components/ui/inputPassword"
import { getAuthenticator, setCookie } from "~/services/auth"
import type { CloudflareENV } from "~/types"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator } = getAuthenticator(env)
  const redirectUrl = new URL(request.url).searchParams.get("redirect")
  return await authenticator.authenticate("user-pass", request, {
    successRedirect: redirectUrl ? redirectUrl : "/user",
    failureRedirect: "/login"
  })
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator, sessionStorage } = getAuthenticator(env)
  await authenticator.isAuthenticated(request, { successRedirect: "/user" })
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  const error = session.get(authenticator.sessionErrorKey)
  const cookie = await setCookie(sessionStorage, session)
  return json({ error }, { headers: { ...cookie } })
}

export default function Login() {
  const navigation = useNavigation()
  const navigate = useNavigate()
  const isSubmitting = navigation.formAction === "/login"
  const { error } = useLoaderData<typeof loader>()
  const { t } = useTranslation()
  const [isRememberMe, setIsRememberMe] = useState(false)

  return (
    <div className="grid xs:grid-cols-1 md:grid-cols-2 h-screen">
      <div className="container max-w-3xl mx-auto py-8 md:py-12 relative box-border">
        <div className="px-5 md:px-10 lg:px-20">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <Logo href="/" />
            </div>
            <div>
              <Title title="Login" />
            </div>
          </div>
        </div>
        <div className="absolute top-1/2 transform -translate-y-1/2 px-5 md:px-10 lg:px-20">
          <Form method="POST">
            {error && (
              <div className="alert alert-error mb-4">{error.message}</div>
            )}
            <div className="grid grid-cols-1 gap-2">
              <div className="mb-4">
                <p className={"text"}>
                  Please enter your email and password to log in. If you're new,
                  click the&nbsp;
                  <Link to="/registration">
                    <strong>Sign Up</strong>
                  </Link>
                  &nbsp; link to create an account and get started.
                </p>
              </div>
              <div>
                <Input
                  isRequired
                  name="email"
                  label="Email"
                  type="email"
                  placeholder="Enter your email address"
                />
              </div>
              <div>
                <InputPassword
                  isRequired
                  name="password"
                  label="Password"
                  placeholder="Enter your password"
                />
              </div>
              <div className="flex justify-between items-center ">
                <div className="text-center">
                  <Checkbox
                    isSelected={isRememberMe}
                    color="primary"
                    onChange={() => {
                      setIsRememberMe(prev => !prev)
                    }}>
                    {t("auth.login.remember_me")}
                  </Checkbox>
                </div>
                <div>
                  <Button
                    radius="sm"
                    variant="light"
                    color="primary"
                    onPress={() => {
                      navigate("/forgot-password")
                    }}>
                    {t("auth.login.forgot_password")}
                  </Button>
                </div>
              </div>
              <div className="flex space-x-3">
                <div>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    startContent={<LogIn size={"16px"} />}
                    color="primary"
                    radius="sm">
                    {isSubmitting ? t("common.submitting") : t("common.login")}
                  </Button>
                </div>
                <div>
                  <Button
                    radius="sm"
                    variant="light"
                    disabled={isSubmitting}
                    onPress={() => navigate(-1)}>
                    {t("auth.registration.cancel")}
                  </Button>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </div>
      <div className="hidden md:block">
        <Image
          radius="none"
          src="/login-image.jpeg"
          classNames={{
            wrapper: "w-full h-full",
            img: "object-cover w-full h-full"
          }}
        />
      </div>
    </div>
  )
}
