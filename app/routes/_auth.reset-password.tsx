import { type ActionFunctionArgs, json } from "@remix-run/cloudflare"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { UserValidator } from "~/validators/user"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const oldPassword = payload.get("oldPassword")
  const validator = new UserValidator(env.DB)
  const { data, error } = await validator.validateChangePassword(payload)
  if (!data) return json({ error, sent: null })

  try {
    const user = await new UserService(env.DB).authenticate(
      data?.user_email?.toString() || "",
      oldPassword?.toString() || ""
    )
    if (user?.id) {
      const resetPasswordData = {
        password: data.password,
        confirmPassword: data.confirmPassword
      }
      await new UserService(env.DB).update(
        Number(data.user_id),
        resetPasswordData
      )
      return json({ success: true })
    } else {
      console.log("Error Old Password is incorrect")
      return json({ error: true, sent: null }, { status: 400 })
    }
  } catch (error) {
    return null
  }
}
