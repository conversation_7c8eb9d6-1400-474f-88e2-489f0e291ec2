import { <PERSON><PERSON>, <PERSON><PERSON>, Input } from "@heroui/react"
import type {
  ActionFunctionArgs,
  LoaderFunctionArgs
} from "@remix-run/cloudflare"
import { data, Form, useActionData, useLoaderData } from "@remix-run/react"
import { EStatus } from "~/enums/EStatus"
import {
  getAuthenticator,
  getCookie,
  getSessionStorage,
  setCookie
} from "~/services/auth"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { UserValidator } from "~/validators/user"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const storage = getSessionStorage(env)
  const session = await getCookie(request, storage)
  const message = session.get("message")
  const hasSuper = await new UserService(env.DB).hasSuper()
  if (hasSuper && !message)
    throw new Response("Page not found", { status: 404 })
  const cookie = await setCookie(storage, session)
  return data({ message }, { headers: { ...cookie } })
}

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { sessionStorage } = getAuthenticator(env)
  const payload = await request.formData()
  const validator = new UserValidator(env.DB)
  const v = await validator.validateCreateSuper(payload)
  if (!v.data) return { data: null, error: null, fieldErrors: v.error }
  const user = await new UserService(env.DB).add({
    ...v.data,
    active: true,
    isSuper: true,
    status: EStatus.Approve
  })
  if (!user) return { error: "Something went wrong" }
  const session = await getCookie(request, sessionStorage)
  session.flash(
    "message",
    "Super Admin account has been created. Please keep this credential safe."
  )
  const cookie = await setCookie(sessionStorage, session)
  return data(
    { data: null, error: null, fieldErrors: null },
    { headers: { ...cookie } }
  )
}

export default function SetupSuper() {
  const { message } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()
  const errors = actionData?.fieldErrors

  const renderForm = (
    <div className="space-y-2">
      <div className="text-center mb-4">
        <h1 className="font-bold text-2xl">Super User</h1>
        <p>
          Please enter the username and password for the super user. This will
          only be created ONCE.
        </p>
      </div>
      {actionData?.error && (
        <Alert
          color="danger"
          description="Someting went wrong. Please reload and try again."
          title="Oops"
        />
      )}
      <Form method="POST" action="/setup/super">
        <fieldset className="space-y-3">
          <div className="flex gap-2">
            <Input
              type="text"
              name="firstName"
              label="First Name"
              isInvalid={!!errors?.firstName}
              errorMessage={errors?.firstName ? errors.firstName[0] : null}
              placeholder="First name"
              variant="bordered"
              isRequired
            />
            <Input
              type="text"
              name="lastName"
              isInvalid={!!errors?.lastName}
              errorMessage={errors?.lastName ? errors.lastName[0] : null}
              label="Last Name"
              placeholder="Last name"
              variant="bordered"
              isRequired
            />
          </div>
          <Input
            type="email"
            name="email"
            label="Email"
            isInvalid={!!errors?.email}
            errorMessage={errors?.email ? errors.email[0] : null}
            placeholder="username"
            variant="bordered"
            isRequired
          />
          <Input
            type="password"
            name="password"
            label="Password"
            isInvalid={!!errors?.password}
            errorMessage={errors?.password ? errors.password[0] : null}
            placeholder="password"
            variant="bordered"
            isRequired
          />
          <Button variant="solid" color="primary" type="submit">
            Submit
          </Button>
        </fieldset>
      </Form>
    </div>
  )

  const renderSuccess = (
    <Alert description={message} title="Success">
      <div className="my-3">
        <Button as="a" href="/admin" size="sm" color="primary" variant="solid">
          Go to Admin
        </Button>
      </div>
    </Alert>
  )

  return (
    <div className="container mx-auto max-w-md mt-20 space-y-2">
      {message && renderSuccess}
      {!message && renderForm}
    </div>
  )
}
