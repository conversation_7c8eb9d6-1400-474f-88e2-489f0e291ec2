import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import { Form, useNavigate, useRouteLoaderData } from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const env = context.cloudflare.env as CloudflareENV
  const action = data.action
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), {
      ...data,
      step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/9`
    }))
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-design`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  const navigate = useNavigate()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid w-full gap-y-3">
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2">
                <div className="col-span-2">
                  <Input
                    label={"Project Development Cost"}
                    name="project_development_cost"
                    placeholder="Enter in US Dollar"
                    endContent={"USD"}
                    isRequired
                    defaultValue={pdd?.project_development_cost}
                    inputMode="numeric"
                    onInput={e => {
                      e.currentTarget.value = e.currentTarget.value.replace(
                        /[^0-9.]/g,
                        ""
                      )
                    }}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    label={"Operational Cost (USD/year)"}
                    name="operational_cost"
                    placeholder="Enter in US Dollar per year"
                    endContent={"USD/Year"}
                    isRequired
                    defaultValue={pdd?.operational_cost}
                    inputMode="numeric"
                    onInput={e => {
                      e.currentTarget.value = e.currentTarget.value.replace(
                        /[^0-9.]/g,
                        ""
                      )
                    }}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    label={"Expected Revenue from Carbon Credits (USD/year)"}
                    name="expected_revenue"
                    placeholder="Enter in US Dollar per year"
                    endContent={"USD/Year"}
                    isRequired
                    defaultValue={pdd?.expected_revenue}
                    inputMode="numeric"
                    onInput={e => {
                      e.currentTarget.value = e.currentTarget.value.replace(
                        /[^0-9.]/g,
                        ""
                      )
                    }}
                  />
                </div>
                <div>
                  <Select
                    label="Funding Source"
                    placeholder="Select Project Sector"
                    data={[{ id: "Public Grant", name: "Public Grant" }]?.map(
                      sector => ({
                        key: sector.id.toString(),
                        label: sector.name
                      })
                    )}
                    defaultSelectedKeys={[pdd?.funding_source?.toString()]}
                    name="funding_source"
                  />
                </div>
              </div>
            </div>
            <div className="flex-1">
              <NoteInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px] flex gap-x-3">
          <Button
            className="bg-primary text-white"
            name="action"
            value="save-and-continue"
            type="submit">
            Save & Continue
          </Button>
          <Button
            variant="bordered"
            color="primary"
            name="action"
            value="save"
            type="submit">
            Save
          </Button>
          <Button
            className="bg-transparent"
            onPress={() => {
              navigate(`/pdds/${pdd?.id}`)
            }}>
            Cancel
          </Button>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
