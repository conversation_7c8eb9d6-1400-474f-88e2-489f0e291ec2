import {
  type ActionFunctionArgs,
  json,
  type LoaderFunction,
  redirect
} from "@remix-run/cloudflare"
import {
  Form,
  useActionData,
  useFetcher,
  useLoaderData,
  useNavigation
} from "@remix-run/react"
import Logo from "~/components/logo"
import { fieldError } from "~/helpers/form"
import { getAuthenticator } from "~/services/auth"
import { OTPService } from "~/services/otp"
import type { CloudflareENV, SignupData } from "~/types"
import type { action as ResendAction } from "./_auth.registration"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const code = payload.get("code")
  const next = payload.get("next")
  if (!code || !next) throw new Response("Bad request", { status: 400 })
  const ok = await new OTPService(env).verify(code.toString())
  if (!ok) return json({ error: "Code is invalid" })
  return redirect(next.toString())
}

export const loader: LoaderFunction = async ({ request, context }) => {
  const env = context.cloudflare.env as CloudflareENV
  const { sessionStorage } = getAuthenticator(env)
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  const signupData = session.get("signup")
  if (!signupData) throw new Response("Not Found", { status: 404 })
  const { next, email } = signupData as SignupData
  if (!email || !next) throw new Response("Not Found", { status: 404 })
  return json({ next, email })
}

export default function Verify() {
  const resendFetcher = useFetcher<typeof ResendAction>()
  const navigation = useNavigation()
  const actionData = useActionData<typeof action>()
  const loaderData = useLoaderData<typeof loader>()
  const resendData = resendFetcher.data
  const error = actionData?.error
  const { email, next } = loaderData as SignupData
  const isSubmitting = navigation.formAction === "/verify"
  const isResending = resendFetcher.state != "idle"

  return (
    <dialog className="modal modal-open">
      <div className="modal-box text-center ">
        <div className="flex flex-col gap-4 justify-center items-center">
          <span className="inline-flex w-32 sm:w-32">
            <Logo />
          </span>

          <h2 className=" text-xl sm:text-2xl font-semibold">
            Email Verification
          </h2>
          <p className=" text-sm py-4">
            We've sent a verification to <strong>{email}</strong> to verify your
            email address and activate your account.
          </p>
          <Form
            method="post"
            action="/verify"
            className="flex flex-col items-center space-y-2">
            <label className="form-control space-y-1">
              <input
                type="number"
                name="code"
                className="input input-bordered [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none disabled:bg-transparent"
              />
              {error && error && fieldError(error)}
            </label>
            <input type="hidden" name="next" value={next} />
            <div className="text-xs text-base-300">
              Code will be expired soon
            </div>
            <button className="btn btn-primary px-8" disabled={isSubmitting}>
              {isSubmitting && (
                <span className="loading loading-spinner loading-sm"></span>
              )}
              {isSubmitting ? "Verifying..." : "Verify"}
            </button>
          </Form>
          {resendData ? (
            resendData.sent?.data ? (
              <div className="alert alert-info flex justify-center items-center w-full">
                Another Code has been sent. Please check your inbox.
              </div>
            ) : (
              <div className="alert alert-error flex justify-center items-center w-full">
                Oops! Something went wrong
              </div>
            )
          ) : null}

          <div className="flex">
            <div>Didn’t receive any code?</div>
            <resendFetcher.Form method="POST" action="/registration/resend">
              <input type="hidden" name="email" value={email} />
              <button
                className="btn btn-link btn-sm pb-2"
                disabled={isResending}>
                {isResending ? "Resending..." : "Resend"}
              </button>
            </resendFetcher.Form>
          </div>
        </div>
      </div>
    </dialog>
  )
}
