import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import {
  ActionFunctionArgs,
  json,
  LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Form, useActionData, useLoaderData } from "@remix-run/react"
import { jwtVerify } from "jose"
import { useTranslation } from "react-i18next"
import Logo from "~/components/logo"
import Title from "~/components/title"
import InputPassword from "~/components/ui/inputPassword"
import { fieldError } from "~/helpers/form"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { UserValidator } from "~/validators/user"

export async function loader({ context, params }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  try {
    const secret = new TextEncoder().encode(env.RESET_PASSWORD_SECRET)
    const { payload } = await jwtVerify(id, secret)
    const user = await new UserService(env.DB).getByEmail(payload?.email)
    if (user?.resetTokenKey === id) {
      return json({ success: true, user_id: user?.id })
    } else {
      throw new Response("Invalid Token", { status: 404 })
    }
  } catch (error) {
    console.log("Error", error)
    throw new Response("Invalid Token", { status: 404 })
  }
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new UserValidator(env.DB)
  const { data, error } = await validator.validateResetPassword(payload)
  if (!data) return json({ error, sent: null })
  const resetPasswordData = {
    password: data.password,
    confirmPassword: data.confirmPassword
  }
  try {
    await new UserService(env.DB).update(Number(data.user_id), {
      ...resetPasswordData,
      resetTokenKey: null
    })

    return redirect("/login")
  } catch (error) {
    return null
  }
}

export default function ResetPassword() {
  const actionData = useActionData<typeof action>()
  const error = actionData?.error
  const { t } = useTranslation()
  const { user_id } = useLoaderData<typeof loader>()
  return (
    <div className="grid xs:grid-cols-1 md:grid-cols-2 h-screen">
      <div className="container max-w-3xl mx-auto py-8 md:py-12 relative box-border">
        <div className="px-5 md:px-10 lg:px-20">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <Logo href="/" />
            </div>
            <div>
              <Title title="Reset Password" />
            </div>
          </div>
        </div>
        <div className="mt-32">
          <Form method="post">
            <div className="grid grid-cols-1 gap-4 px-5 md:px-10 lg:px-20 ">
              <div>
                <p className="font-bold text-[30px]">Reset Password</p>
              </div>
              <div>
                <InputPassword
                  label={"New Password"}
                  name="password"
                  isRequired
                  placeholder={"Enter New Password"}
                />
                {error && error.password && fieldError(error.password[0])}
              </div>
              <div>
                <InputPassword
                  label={t("auth.registration.confirm_password")}
                  name="confirmPassword"
                  isRequired
                  placeholder={t("auth.registration.enter_confirm_password")}
                />
                <input type="hidden" name="user_id" value={user_id}></input>
              </div>
              <div>
                <Button type="submit" color="primary" fullWidth>
                  {t("common.submit")}
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </div>
      <div className="hidden md:block">
        <Image
          radius="none"
          src="/login-image.jpeg"
          classNames={{
            wrapper: "w-full h-full",
            img: "object-cover w-full h-full"
          }}
        />
      </div>
    </div>
  )
}
