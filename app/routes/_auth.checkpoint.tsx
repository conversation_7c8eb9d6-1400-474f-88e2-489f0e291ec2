import {
  ActionFunctionArgs,
  defer,
  LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import { Suspense, useEffect, useRef, useState } from "react"
import { ENotification } from "~/enums/ENotification"
import { ERole } from "~/enums/EUserRole"
import { getAuthenticator, setCookie } from "~/services/auth"
import NotificationService from "~/services/db/notification"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { AuthUser, CloudflareENV, ISignUp, SignupData } from "~/types"

export async function action({ request, context }: ActionFunctionArgs) {
  const payload = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator, sessionStorage } = getAuthenticator(env)
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  session.set(authenticator.sessionKey, Object.fromEntries(payload))
  session.flash("message", "Welcome to Cambodia Carbon Registry")
  return redirect("/onboarding")
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { sessionStorage } = getAuthenticator(env)
  const session = await sessionStorage.getSession(request.headers.get("Cookie"))
  const signupData = session.get("signup") as SignupData | null
  if (!signupData) throw new Response("Not Found", { status: 404 })
  const data = signupData.data as ISignUp
  const user = await new UserService(env.DB).create(data)
  const administrators = await new UserService(env.DB).getByRole(
    ERole.Administrator
  )
  const url = new URL(request.url)
  const host = url.origin
  const link = `${host}/admin/users/${user?.id}`
  if (administrators) {
    for (const administrator of administrators) {
      try {
        await new MailService(env).NewUserRegisterEmailNotification(
          // administrator?.email,
          env.RESEND_CONTACT_TO_EMAIL,
          link
        )
        await new NotificationService(env.DB).create({
          type: ENotification.User,
          user_id: administrator.id,
          data: user?.id,
          link: `/admin/users/${user?.id}`
        })
      } catch (error) {
        console.log(error)
      }
    }
  }
  session.unset("signup")
  const cookie = await setCookie(sessionStorage, session)
  return defer({ user }, { headers: { ...cookie } })
}

const loading = (
  <div className="flex flex-col justify-center h-screen items-center">
    <div className="card border border-base-300 w-96 shadow-xl">
      <div className="card-body items-center gap-5">
        <span className="bg-primary loading loading-spinner loading-lg"></span>
        <p>Creating your account...</p>
      </div>
    </div>
  </div>
)

export default function CheckPoint() {
  const formRef = useRef<HTMLFormElement>(null)
  const { user } = useLoaderData<typeof loader>()
  const [authUser, setAuthUser] = useState<AuthUser | null>(null)

  useEffect(() => {
    if (authUser && formRef && formRef.current) formRef.current.submit()
  }, [authUser, formRef])

  return (
    <>
      <Suspense fallback={loading}>
        <Await resolve={user}>
          {user => (
            <>
              {setAuthUser(user as AuthUser)}
              {user && (
                <form method="post" ref={formRef} action="/checkpoint">
                  <input type="hidden" value={user.email} name="email" />
                  <input
                    type="hidden"
                    value={user.firstName}
                    name="firstName"
                  />
                  <input type="hidden" value={user.lastName} name="lastName" />
                </form>
              )}
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}
