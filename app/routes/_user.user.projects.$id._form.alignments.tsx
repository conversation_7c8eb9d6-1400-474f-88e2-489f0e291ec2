import { <PERSON><PERSON> } from "@heroui/react"
import { ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import AddressInfo from "~/components/projectRegistration/AddressInfor"
import CoverImageInfo from "~/components/projectRegistration/CoverImageInfo"
import NameInfo from "~/components/projectRegistration/NameInfo"
import ProjectDescriptionInfo from "~/components/projectRegistration/ProjectDescriptionInfo"
import SectorInfo from "~/components/projectRegistration/SectorInfo"
import Input from "~/components/ui/input"
import TextArea from "~/components/ui/textArea"
import { Project_STEPS } from "~/enums/EProjectStep"
import IdeaNoteService from "~/services/db/ideaNote"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import { ProjectValidator } from "~/validators/project"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new ProjectValidator(env.DB)
  const { data, error } = await validator.validateEdit(payload)
  const id = params.id
  if (error) {
    return { error: true }
  }
  const action = data?.action
  await new ProjectService(context.cloudflare.env.DB).update(
    parseInt(id ?? ""),
    {
      ...data,
      project_step: Project_STEPS.ALIGNMENTS
    }
  )
  data &&
    data["con.id"] &&
    (await new IdeaNoteService(env.DB).update(Number(data["con.id"]), {
      positive: data["con.positive"] ?? "",
      dev_prio: data["con.dev_prio"] ?? ""
    }))
  if (action === "save") {
    return redirect(`/user/projects`)
  } else if (action === "submit-lno-request") {
    return redirect(`/user/projects/${params.id}/${Project_STEPS.LNOREQUEST}`)
  } else {
    return null
  }
}

const ProjectAlignments = () => {
  const navigate = useNavigate()
  const { project } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id._form"
  )
  const param = useParams()

  return (
    <Form method="POST">
      <div className="grid grid-cols-3 py-8">
        <div className="col-span-2 pr-20 space-y-6">
          <div className="grid gap-y-3">
            <input
              type="hidden"
              name="con.id"
              value={project?.ideaNote?.id ?? ""}></input>

            <div>
              <Input
                name="con.positive"
                label="Cambodia’s Positive List"
                defaultValue={project?.ideaNote?.positive ?? ""}
                placeholder="Please specify how the project aligns to Cambodia’s ‘positive list’ of GHG ER projects"
              />
            </div>
            <div>
              <TextArea
                name="con.dev_prio"
                defaultValue={project?.ideaNote?.dev_prio ?? ""}
                label="Cambodia’s sustainable development priorities"
                placeholder="Please specify how the project is aligned with Cambodia’s sustainable development priorities"
              />
            </div>
          </div>
          <div className="grid gap-y-3">
            <div>
              <p className="font-bold text-[20px]">
                Alignment with Sustainable Development Priorities
              </p>
            </div>
            <div>
              <TextArea
                name="con.poverty_alleviation"
                label="Poverty alleviation"
                placeholder="Describe how the project will alleviate poverty"
              />
            </div>
            <div>
              <TextArea
                label="Provision of Community Infrastructure"
                placeholder="Describe what infrastructure will be provision by the project "
              />
            </div>
            <div>
              <TextArea
                label="Stakeholder Consultation"
                placeholder="Describe how the project is consulted with stakeholder"
              />
            </div>
            <div>
              <TextArea
                label="Access to Community Assets"
                placeholder="Describe how the project will provide to the community assets"
              />
            </div>
            <div>
              <TextArea
                label="Equity in Accessing Benefits"
                placeholder="Describe how the project will ensure equity in accessing benefits"
              />
            </div>
            <div>
              <TextArea
                label="Creation of Employment"
                placeholder="Describe how the project will create jobs"
              />
            </div>
            <div>
              <TextArea
                label="Impact on Public Health"
                placeholder="Describe how the project will make the impact on public health"
              />
            </div>
            <div>
              <TextArea
                label="Gender Equity"
                placeholder="Describe how the project will ensure gender equity"
              />
            </div>
            <div>
              <TextArea
                label="Adaptation"
                placeholder="Describe how the project will ensure adaptation"
              />
            </div>
          </div>
          <div className="grid gap-y-3">
            <div>
              <p className="font-bold text-[20px]">Technology Transfer</p>
            </div>
            <div>
              <TextArea
                label="Transfer of Appropriate and Best Available Technology"
                placeholder='E.g., "Introduction of drone-based forest monitoring systems to improve carbon stock assessments."'
              />
            </div>
            <div>
              <TextArea
                label="Capacity Building"
                placeholder='E.g., "Workshops on solar panel maintenance for 30 local technicians, ensuring long-term project sustainability."'
              />
            </div>
          </div>
          <div className="grid gap-y-3">
            <div>
              <p className="font-bold text-[20px]">Economic Benefits</p>
            </div>
            <div>
              <TextArea
                label="Use of Local Businesses and Industries"
                placeholder="Describe how the project will make use of local businesses and industries"
              />
            </div>
            <div>
              <TextArea
                label="Share of Project Budget Spent in Country"
                placeholder="Describe how the project budget will be spent in Country"
              />
            </div>
            <div>
              <TextArea
                label="Reduced Dependence on Fossil Fuels (energy projects only)"
                placeholder="Describe how the project will reduct dependence on Fossil Fuels"
              />
            </div>
            <div>
              <TextArea
                label="Reduced Dependence on Imported Energy (energy projects only)"
                placeholder="Describe how the project will reduct on Imported Energy"
              />
            </div>
          </div>
          <div className="flex gap-x-3">
            <Button
              className="bg-primary text-white"
              name="action"
              value="save"
              type="submit">
              Save
            </Button>
            <Button
              variant="bordered"
              color="primary"
              name="action"
              value="submit-lno-request"
              type="submit">
              Submit LNO Request
            </Button>
            <Button
              className="bg-transparent"
              onPress={() =>
                navigate(`/user/projects/${param.id}/project-information`)
              }>
              Back
            </Button>
          </div>
        </div>
        <div className="space-y-6">
          <SectorInfo />
          <NameInfo />
          <ProjectDescriptionInfo />
          <CoverImageInfo />
          <AddressInfo />
        </div>
      </div>
    </Form>
  )
}

export default ProjectAlignments
