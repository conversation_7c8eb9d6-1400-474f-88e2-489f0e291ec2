import { Accordion, Accordi<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { MinusIcon, PlusIcon } from "lucide-react"
import { useState } from "react"
import ReadyToJoin from "~/components/landingPage/ReadyToJoin"
import GuideLineCard from "~/components/resources/GuideLineCard"
import TopImageTitleAndShortDescription from "~/components/topImageTitleAndShortDescription"

export default function Resources() {
  const keySectors = [
    {
      title: "What is the Cambodia National Carbon Registry?",
      content:
        "The Cambodia National Carbon Registry is a platform established to register, track, and verify Greenhouse Gas (GHG) Emission Reduction (ER) projects in Cambodia. It ensures projects align with national policies and international standards for carbon mitigation."
    },
    {
      title: "Who can submit a project for registration?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "What are the eligibility requirements for project submission?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "What documents are required for project submission?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "How long does the project review and approval process take?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "How is project data monitored and verified?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "Can a project be modified after registration?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title: "What happens if a project does not comply with the guidelines?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    },
    {
      title:
        "Is there any financial or technical support available for project developers?",
      content:
        "Conserve and restore Cambodia’s forests while creating sustainable livelihood opportunities. Our Forestry and Land Use projects aim to reduce deforestation and promote carbon sequestration."
    }
  ]
  const guideLineData = [
    {
      title: "Project Registration & Submission",
      itemList: [
        "Project Submission Guidelines",
        "Project Design Document (PDD) Template",
        "Baseline & Additionality Assessment Guide"
      ]
    },
    {
      title: "Methodologies & Standards",
      itemList: [
        "IPCC Guidelines for National Greenhouse Gas Inventories",
        "REDD+ Methodology for Forestry Projects",
        "Emission Reduction Calculation Templates"
      ]
    },
    {
      title: "Monitoring, Reporting & Verification (MRV)",
      itemList: [
        "Project Submission Guidelines",
        "Project Design Document (PDD) Template",
        "Baseline & Additionality Assessment Guide"
      ]
    },
    {
      title: "Legal & Regulatory Documents",
      itemList: [
        "IPCC Guidelines for National Greenhouse Gas Inventories",
        "REDD+ Methodology for Forestry Projects",
        "Emission Reduction Calculation Templates"
      ]
    }
  ]
  const [activeKey, setActiveKey] = useState("0")

  const handleAccordionChange = (key: string) => {
    setActiveKey(prevKey => (prevKey === key ? prevKey : key))
  }

  return (
    <div className="bg-white">
      <TopImageTitleAndShortDescription
        title="Resources"
        shortDescription="Find guidelines, templates, methodologies, and regulatory documents to support your project development and compliance."
        image="./resources.jpeg"
      />
      <div className="bg-[#F4F4F5]">
        <div className="container mx-auto py-9 w-full sm:w-3/4 md:w-2/3 lg:w-1/2 text-center leading-7 description">
          <p>
            The Cambodia National Carbon Registry is committed to supporting
            project developers, government agencies, and stakeholders in GHG
            emission reduction initiatives. This resource center provides access
            to essential guidelines, methodologies, and regulatory documents to
            ensure project compliance with national and international climate
            frameworks.
          </p>
        </div>
      </div>
      <div className="container mx-auto space-y-12 p-5" id="how-it-words">
        <div>
          <p className="title text-center mt-4">Guidelines and Documents</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {guideLineData.map((item, index) => {
            return (
              <GuideLineCard
                key={index}
                title={item.title}
                itemList={item.itemList}
              />
            )
          })}
        </div>
      </div>
      <div className="container mx-auto p-5">
        <div className="w-full bg-[#F4F4F5] relative">
          <div className="relative w-full min-h-[300px]">
            <img
              className="absolute inset-0 w-full h-full object-cover"
              src="/faq-image.webp"
              alt="FAQ Background"
            />
            <div className="relative w-full p-4 sm:p-6 md:p-8 lg:p-12 bg-[#F5F5F580]">
              <div>
                <p className="text-center title">
                  Frequently Asked Questions (FAQs)
                </p>
              </div>
              <div className="px-0 sm:px-4 md:px-6 lg:px-24 py-4 sm:py-6 md:py-8 lg:py-10">
                <Accordion
                  dividerProps={{ hidden: true }}
                  selectedKeys={[activeKey]}>
                  {keySectors.map((item, index) => {
                    const key = String(index)
                    const isActive = activeKey === key
                    return (
                      <AccordionItem
                        key={index}
                        value={index}
                        title={item?.title}
                        classNames={{
                          indicator: "data-[open=true]:-rotate-0",
                          title: `text-xl data-[open=true]:font-bold ${
                            isActive ? "cursor-default" : "cursor-pointer"
                          }`,
                          content: `py-0 pb-4`
                        }}
                        indicator={({ isOpen }) =>
                          isOpen ? <MinusIcon /> : <PlusIcon />
                        }
                        onPress={() => {
                          handleAccordionChange(index.toString())
                        }}>
                        <div className="flex flex-col gap-4">
                          <p>{item?.content}</p>
                        </div>
                      </AccordionItem>
                    )
                  })}
                </Accordion>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ReadyToJoin />
    </div>
  )
}
