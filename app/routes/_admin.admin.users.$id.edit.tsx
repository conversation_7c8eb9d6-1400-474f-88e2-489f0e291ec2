import {
  ActionFunction<PERSON>rgs,
  json,
  LoaderFunction<PERSON>rgs,
  MetaFunction
} from "@remix-run/cloudflare"
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
  useNavigation,
  useSubmit
} from "@remix-run/react"
import { useRef } from "react"
import Forehead from "~/components/layout/admin/forehead"
import { fieldError } from "~/helpers/form"
import { getFormDataFromObject, getUpdatedFormData } from "~/lib/form"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { UserValidator } from "~/validators/user"

export const handle = {
  title: "Users",
  backable: true
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new UserValidator(env.DB)
  const { data, error } = await validator.validateEdit(payload)
  if (!data) return json({ error, saved: null })
  const user = await new UserService(env.DB).update(parseInt(id), data)
  return json({ error: null, saved: user !== null })
}

export async function loader({ context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const user = await new UserService(env.DB).getById(parseInt(id))
  return json({ id, user })
}

export default function EditUser() {
  const { id, user } = useLoaderData<typeof loader>()
  const userForm = useRef<HTMLFormElement | null>(null)
  const navigation = useNavigation()
  const navigate = useNavigate()
  const submit = useSubmit()
  const actionData = useActionData<typeof action>()
  const actionPath = `/admin/users/${id}/edit`
  const isSubmitting = navigation.formAction === actionPath
  const error = actionData?.error

  const handleSubmit = (user: object) => {
    const form = userForm.current
    if (form) {
      const a = new FormData(form)
      const b = getFormDataFromObject(user)
      const formData = getUpdatedFormData(a, b)
      if (Array.from(formData.keys()).length > 0) {
        submit(formData, {
          action: actionPath,
          method: "POST",
          encType: "multipart/form-data"
        })
      }
    }
  }

  return (
    <>
      <Forehead title={`Edit user: ${id}`} backref="/admin/users" />
      <div className="max-w-lg mx-auto">
        {actionData?.saved && (
          <div className="alert alert-info mb-10">Updates has been saved</div>
        )}
        {user && (
          <Form ref={userForm} method="POST">
            <fieldset disabled={isSubmitting}>
              <div className="flex flex-col gap-4">
                <label className="form-control space-y-1">
                  <input
                    className="input input-bordered"
                    type="text"
                    defaultValue={user.firstName}
                    placeholder="First Name"
                    name="firstName"
                    autoFocus={true}
                  />
                  {error && error.firstName && fieldError(error.firstName[0])}
                </label>
                <label className="form-control space-y-1">
                  <input
                    className="input input-bordered"
                    type="text"
                    defaultValue={user.lastName}
                    placeholder="Last Name "
                    name="lastName"
                  />
                  {error && error.lastName && fieldError(error.lastName[0])}
                </label>
                <label className="form-control space-y-1">
                  <input
                    className="input input-bordered"
                    type="email"
                    placeholder="Email"
                    name="email"
                    defaultValue={user.email}
                    autoComplete="off"
                    autoCorrect="off"
                    aria-autocomplete="none"
                  />
                  {error && error.email && fieldError(error.email[0])}
                </label>
                <div className="form-control space-y-1 relative">
                  <label className="cursor-pointer flex gap-2 items-center">
                    <input
                      type="checkbox"
                      name="isAdmin"
                      className="checkbox"
                      defaultChecked={user.isAdmin}
                    />
                    <span className="label-text">Is Admin</span>
                  </label>
                </div>
              </div>
            </fieldset>
            <div className="mt-10 flex flex-col gap-2">
              <button
                type="button"
                onClick={() => handleSubmit(user)}
                className="btn btn-primary"
                disabled={isSubmitting}>
                {isSubmitting ? (
                  <span className="flex gap-2 items-center">
                    <span className="loading loading-spinner"></span>
                    <span>Saving...</span>
                  </span>
                ) : (
                  "Save"
                )}
              </button>
              <button
                className="btn btn-ghost"
                disabled={isSubmitting}
                onClick={() => navigate(-1)}>
                Cancel
              </button>
            </div>
          </Form>
        )}
      </div>
    </>
  )
}
