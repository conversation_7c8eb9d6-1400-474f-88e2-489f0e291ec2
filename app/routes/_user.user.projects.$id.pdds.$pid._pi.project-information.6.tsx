import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import Select from "~/components/ui/select"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import { getKey } from "~/helpers/r2"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const action = data?.action
  const env = context.cloudflare.env as CloudflareENV
  const newData = {
    ...data,
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/6`
  }
  if (data?.detailed_monitoring_plan_file) {
    const fileKey = getKey(
      data?.detailed_monitoring_plan_file,
      "detailed_monitoring_plan_file"
    )
    await env.R2.put(fileKey, data?.detailed_monitoring_plan_file)
    newData.detailed_monitoring_plan_file = fileKey
  }
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), newData))
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/7`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const params = useParams()
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )

  const navigate = useNavigate()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid w-full gap-y-3">
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2">
                <div className="col-span-2">
                  <TextArea
                    label="Monitoring Plan"
                    placeholder="Provide details on how project performance will be monitored."
                    name="monitoring_plan"
                    minRows={5}
                    defaultValue={pdd?.monitoring_plan}
                  />
                </div>
                <div>
                  <Select
                    label="Monitoring Frequency"
                    placeholder="Select Monitoring Frequency"
                    data={[{ id: "1", name: "1" }]?.map(sector => ({
                      key: sector.id.toString(),
                      label: sector.name
                    }))}
                    defaultSelectedKeys={[
                      pdd?.monitoring_frequency?.toString()
                    ]}
                    name="monitoring_frequency"
                  />
                </div>
                <div>
                  <Select
                    label="Tool/Method"
                    placeholder="Select Tool/Method"
                    data={[{ id: "1", name: "1" }]?.map(sector => ({
                      key: sector.id.toString(),
                      label: sector.name
                    }))}
                    defaultSelectedKeys={[pdd?.tool_method?.toString()]}
                    name="tool_method"
                  />
                </div>
                <div className="col-span-2">
                  <FileSelect
                    label="Detailed Monitoring Plan"
                    name="detailed_monitoring_plan_file"
                    fileNameSelected={
                      pdd?.detailed_monitoring_plan_file ?? null
                    }
                    acceptFileTypes=".pdf"
                  />
                </div>
              </div>
            </div>
            <div className="flex-1">
              <NoteInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px] flex gap-x-3">
          <Button
            className="bg-primary text-white"
            name="action"
            value="save-and-continue"
            type="submit">
            Save & Continue
          </Button>
          <Button
            variant="bordered"
            color="primary"
            name="action"
            value="save"
            type="submit">
            Save
          </Button>
          <Button
            className="bg-transparent"
            onPress={() => {
              navigate(`/pdds/${params.pid}`)
            }}>
            Cancel
          </Button>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
