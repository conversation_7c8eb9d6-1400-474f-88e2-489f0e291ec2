import { <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import { Form, useNavigate } from "@remix-run/react"
import { SignJWT } from "jose"
import { useTranslation } from "react-i18next"
import Logo from "~/components/logo"
import Title from "~/components/title"
import Input from "~/components/ui/input"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const email = payload.get("email")
  if (!email) {
    return json({ error: "Email is required", sent: null })
  }

  const secret = new TextEncoder().encode(env.RESET_PASSWORD_SECRET)

  const token = await new SignJWT({ email })
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime("30m")
    .sign(secret)

  const url = new URL(request.url)
  const host = url.origin
  const link = `${host}/new-password/${token}`

  try {
    await new UserService(env.DB).updateResetTokenKey(email?.toString() || "", {
      resetTokenKey: token
    })
    await new MailService(env).ResetPasswordEmailNotification(
      email?.toString() || "",
      link
    )
    return redirect("/forgot-password-success")
  } catch (error) {
    return json({ error, sent: null })
  }
}

export default function ForgotPassword() {
  const navigate = useNavigate()
  const { t } = useTranslation()

  return (
    <div className="grid xs:grid-cols-1 md:grid-cols-2 h-screen">
      <div className="container max-w-3xl mx-auto py-8 md:py-12 relative box-border">
        <div className="px-5 md:px-10 lg:px-20">
          <div className="flex justify-between items-center">
            <div className="text-center">
              <Logo href="/" />
            </div>
            <div>
              <Title title="Forgot Password" />
            </div>
          </div>
        </div>
        <div className="mt-32">
          <Form method="POST">
            <div className="grid grid-cols-1 gap-4 px-5 md:px-10 lg:px-20 ">
              <div>
                <p className="font-bold text-[30px]">Account Recovery</p>
              </div>
              <div>
                <p className={"text"}>
                  We will email you instructions for setting your password if an
                  account exists with the email you entered. If you don’t
                  receive an email, please make sure you have entered the
                  address connected to the system and check your spam folder.
                </p>
              </div>
              <div>
                <Input
                  isRequired
                  type="email"
                  label={t("auth.registration.email")}
                  name="email"
                  placeholder={t("auth.registration.enter_email")}
                />
              </div>

              <div className="flex space-x-3">
                <div>
                  <Button type="submit" color="primary" radius="sm">
                    Submit
                  </Button>
                </div>
                <div>
                  <Button
                    radius="sm"
                    variant="light"
                    onPress={() => navigate(-1)}>
                    {t("auth.registration.cancel")}
                  </Button>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </div>
      <div className="hidden md:block">
        <Image
          radius="none"
          src="/login-image.jpeg"
          classNames={{
            wrapper: "w-full h-full",
            img: "object-cover w-full h-full"
          }}
        />
      </div>
    </div>
  )
}
