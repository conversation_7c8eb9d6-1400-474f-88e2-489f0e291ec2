import type { LoaderFunctionArgs } from "@remix-run/cloudflare"
import {
  Outlet,
  useLoaderData,
  useLocation,
  useMatches,
  useNavigate
} from "@remix-run/react"
import { ChevronLeft } from "lucide-react"
import SideMenu from "~/components/layout/admin/menu"
import Logo from "~/components/logo"
import DropDownMenu from "~/components/menu/DropDownMenu"
import { ERole } from "~/enums/EUserRole"
import { getAuthenticator } from "~/services/auth"
import NotificationService from "~/services/db/notification"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const { authenticator } = getAuthenticator(
    context.cloudflare.env as CloudflareENV
  )
  const env = context.cloudflare.env as CloudflareENV
  const path = new URL(request.url).pathname
  const redirectUrl = `/login?redirect=${path}`
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: redirectUrl
  })
  const notifications =
    user?.id &&
    (await new NotificationService(env.DB).getNotificationsByUserId({
      userId: user?.id
    }))

  if (user.role === ERole.ProjectDeveloper) {
    throw new Response("Forbidden", { status: 403 })
  }
  return { user, notifications }
}

export default function AdminLayout() {
  const location = useLocation()
  const navigate = useNavigate()
  const matches = useMatches()
  const m = matches.slice(-1)[0] // last item
  const h = m.handle as { title?: string; backable?: boolean } | undefined
  const title = h?.title ?? ""
  const backable = h?.backable ?? false
  const { user, notifications } = useLoaderData<typeof loader>()

  return (
    <div className="flex h-screen overflow-hidden">
      <aside className="w-60 bg-base-200 ">
        <header className="border-b border-b-base-300">
          <div className="h-14 flex justify-between items-center p-4">
            <div className="flex max-w-32">
              <Logo href="/" size="sm" />
            </div>
          </div>
        </header>
        <div>{user.role && <SideMenu pathname={location.pathname} />}</div>
      </aside>
      <div className="flex-1">
        <header className="border-b border-b-base-300">
          <div className="h-14 flex justify-between items-center p-4">
            <div className="flex gap-2 items-center">
              {backable && (
                <button
                  type="button"
                  className="btn btn-sm btn-square"
                  onClick={() => navigate(-1)}>
                  <ChevronLeft size={23} />
                </button>
              )}
              <h2 className="text-lg">{title}</h2>
            </div>
            <div className="text-right">
              {user && (
                <DropDownMenu notifications={notifications} user={user} />
              )}
            </div>
          </div>
        </header>
        <main>
          <div className="p-4 pb-24 h-screen overflow-hidden overflow-y-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}
