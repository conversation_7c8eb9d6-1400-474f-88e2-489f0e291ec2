import {
  <PERSON><PERSON>,
  CalendarDate,
  DateValue,
  Radio,
  RadioGroup
} from "@heroui/react"
import { type ActionFunctionArgs, json, redirect } from "@remix-run/cloudflare"
import { Form } from "@remix-run/react"
import { useState } from "react"
import Forehead from "~/components/layout/admin/forehead"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import { typeOfCarbon, useOfGhgErs } from "~/lib/performanceDetail"
import ImplementationService from "~/services/db/implementation"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const user = await getAuthUser(context, request)
  const data = Object.fromEntries(payload.entries())
  const newData = {
    ...data,
    user_id: user.id,
    project_id: id,
    carbon_credit: Number(data?.share_co2)
      ? Number(data?.co2) - Number(data?.share_co2)
      : Number(data?.co2)
  }

  const result = await new ImplementationService(env.DB).create(newData)
  if (!result?.length) {
    return json({ error: "Something went wrong", saved: null })
  }
  return redirect(`/performance-report/${result[0].id}`)
}
export default function NewImplementation() {
  const [selectedTypeOfCarbon, setSelectedTypeOfCarbon] = useState<
    string | null
  >(null)

  const [otherTypeOfCarbon, setOtherTypeOfCarbon] = useState("")

  const [selectedUseOfGhgErs, setSelectedUseOfGhgErs] = useState<string | null>(
    null
  )

  const [startDate, setStartDate] = useState<CalendarDate | null>(null)
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)

  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }

  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }
  return (
    <>
      <Forehead title="Add New Implementation" />
      <Form method="post" encType="multipart/form-data">
        <div className="container max-w-3xl mx-auto space-y-6 px-5">
          <div>
            <p className="text-[22px] text-black">Authorization Information</p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Input
                isRequired
                label={"Volume of in tCO2eq"}
                name="co2"
                placeholder="Enter Volume of in tCO2eq"></Input>
            </div>
            <div>
              <Input
                label={"Share of in tCO2eq"}
                name="share_co2"
                placeholder="Enter Share of in tCO2eq"></Input>
            </div>
            <div>
              <DatePicker
                isRequired
                variant="bordered"
                label="Start Date"
                name="start_date"
                value={startDate}
                onChange={handleStartDateChange}
                maxValue={endDate}
              />
            </div>
            <div>
              <DatePicker
                isRequired
                variant="bordered"
                label="End Date"
                name="end_date"
                value={endDate}
                onChange={handleEndDateChange}
                minValue={startDate}
              />
            </div>
            <div>
              <RadioGroup
                label={"Type of carbon mechanism"}
                isRequired
                color="primary"
                name="type_of_carbon_mechanism"
                value={selectedTypeOfCarbon}
                onValueChange={setSelectedTypeOfCarbon}
                classNames={{
                  label: "text-lg font-medium"
                }}>
                {typeOfCarbon.map(item => (
                  <Radio key={item.id} value={String(item.id)}>
                    {item.name}
                  </Radio>
                ))}
              </RadioGroup>
              {selectedTypeOfCarbon === "other" && (
                <Input
                  variant="underlined"
                  isRequired
                  name="other_type_of_carbon_mechanism"
                  label={"Other carbon mechanism name"}
                  value={otherTypeOfCarbon}
                  onChange={e => setOtherTypeOfCarbon(e.target.value)}
                  placeholder="Enter other sector"></Input>
              )}
            </div>
            <div>
              <RadioGroup
                isRequired
                label={"Use of GHG ERs"}
                color="primary"
                name="user_of_ghg_ers"
                value={selectedUseOfGhgErs}
                onValueChange={setSelectedUseOfGhgErs}
                classNames={{
                  label: "text-lg font-medium"
                }}>
                {useOfGhgErs.map(item => (
                  <Radio key={item.id} value={String(item.id)}>
                    {item.name}
                  </Radio>
                ))}
              </RadioGroup>
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit" color="primary">
              Submit
            </Button>
          </div>
        </div>
      </Form>
    </>
  )
}
