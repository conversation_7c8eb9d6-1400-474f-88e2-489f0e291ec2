import ReadyToJoin from "~/components/landingPage/ReadyToJoin"
import TopImageTitleAndShortDescription from "~/components/topImageTitleAndShortDescription"

export default function HowItWork() {
  const processSteps = [
    {
      imgUrl: "/process-step1.webp",
      stepNumber: "01",
      title: "Idea & Request for Letter of No Objection (LNO)",
      description:
        "Before officially registering a project, proponents must request a Letter of No Objection (LNO) from the Designated National Authority (DNA) to ensure alignment with national climate policies.",
      requiredList: [
        "Project summary (objectives, location, methodology)",
        "Expected emission reductions",
        "Environmental & social safeguards compliance",
        "Stakeholder consultation summary"
      ],
      whatHappensNextList: [
        "The National Authority reviews the request.",
        "If the project aligns with Cambodia's climate strategies, an LNO is issued."
      ]
    },
    {
      isReverse: true,
      imgUrl: "/process-step2.webp",
      stepNumber: "02",
      title: "Project Design & Request for Letter of Authorization (LOA)",
      description:
        "After obtaining the LNO, proponents must develop a detailed Project Design Document (PDD) and submit a request for the Letter of Authorization (LOA).",
      requiredList: [
        "Project Design Document (PDD)",
        "Baseline scenario & additionality justification",
        "GHG reduction calculation methodology",
        "Environmental & social impact assessment",
        "Monitoring, Reporting, and Verification (MRV) Plan"
      ],
      whatHappensNextList: [
        "The National Authority reviews the PDD.",
        "If the project meets compliance requirements, the LOA is issued."
      ]
    },
    {
      imgUrl: "/process-step3.webp",
      stepNumber: "03",
      title: "Project Implementation & Monitoring",
      description:
        "Once the project is authorized, it enters the implementation phase where emission reductions are monitored, reported, and prepared for verification.",
      requiredList: [
        "Implement project activities according to the approved PDD.",
        "Collect and monitor project data based on the MRV Plan.",
        "Submit data periodically to the system for tracking and evaluation."
      ],
      whatHappensNextList: [
        "Compiled data into Monitoring & Emission Reduction Reports for verification."
      ]
    },
    {
      isReverse: true,
      imgUrl: "/process-step4.webp",
      stepNumber: "04",
      title: "Verification & Credit Issuance",
      description:
        "After project implementation and monitoring, the project proponent must submit an MRV report and request verification to get carbon credits issued and complete the project registration.",
      requiredList: [
        "Monitoring & Emission Reduction Report",
        "Supporting documentation for measured emission reductions"
      ],
      whatHappensNextList: [
        "MRV Reports will be validated and verified by independent third-party verifiers (VVB).",
        "Carbon Credits will be authorized and issued to the project.",
        "Project will be marked as Registered and available for public access."
      ]
    }
  ]

  return (
    <div className="bg-[#FEFEFE]">
      <TopImageTitleAndShortDescription
        title="How it Works"
        shortDescription="A step-by-step guide to navigating the project submission, monitoring, and verification process."
        image="./how-it-works.jpeg"
      />
      <HowItWorkDescription />
      <div>
        <div className="flex justify-center mb-4">
          <p className="title">The Process</p>
        </div>
        {processSteps.map((step, index) => (
          <HowItProcess key={index} {...step} />
        ))}
      </div>

      <ReadyToJoin />
    </div>
  )
}

type HowItProcessProps = {
  imgUrl: string
  stepNumber: string
  title: string
  description: string
  requiredList: string[]
  whatHappensNextList: string[]
  isReverse?: boolean
}

const HowItProcess = ({
  imgUrl,
  stepNumber,
  title,
  description,
  requiredList,
  whatHappensNextList,
  isReverse = false
}: HowItProcessProps) => {
  return (
    <div className="py-8">
      <div
        className={`flex flex-col lg:flex-row gap-8 items-stretch  ${
          isReverse ? "lg:flex-row-reverse" : ""
        }
        ${isReverse ? "md:pl-10 md:pr-0" : "md:pl-0 md:pr-10"}
        `}>
        {/* Left side - Image */}
        <div className="w-full lg:w-1/2 max-h-[450px] overflow-hidden">
          <img
            src={imgUrl}
            alt={`Step ${stepNumber} illustration`}
            className="object-cover w-full h-full "
          />
        </div>

        {/* Right side - Content */}
        <div className="w-full lg:w-1/2 space-y-3 px-5">
          {/* Step Header */}
          <div>
            <h2 className="text-xl ">STEP {stepNumber}</h2>
            <h3 className="text-xl font-semibold  mt-2">{title}</h3>
          </div>

          {/* Description */}
          <div>
            <p className="text-gray-600">{description}</p>
          </div>

          {/* Requirements Section */}
          <div className="space-y-2">
            <h4 className="text-lg font-semibold">What are required?</h4>
            <div className="bg-[#F4F4F5] p-4 rounded-lg">
              <ul className="space-y-2 list-disc pl-5 ">
                {requiredList.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          </div>

          {/* Next Steps Section */}
          <div className="space-y-2">
            <h4 className="text-lg font-semibold text-gray-800 ">
              What Happens Next?
            </h4>
            <ul className="space-y-2 list-disc pl-5 text-gray-600">
              {whatHappensNextList.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

type ProcessStepProps = {
  number: string
  title: string
  description: string
}

const ProcessStep = ({ number, title, description }: ProcessStepProps) => {
  return (
    <div className="bg-[#F4F4F5] border-[4px] border-[#D4D4D8] rounded-[20px] p-4 flex items-center text-base">
      <span className="text-gray-400 font-semibold mr-4">{number}</span>
      <div>
        <h3 className="font-semibold text-gray-900 inline">{title}</h3>
        <span className="ml-4 text-gray-600">{description}</span>
      </div>
    </div>
  )
}

const HowItWorkDescription = () => {
  const steps = [
    {
      number: "01",
      title: "IDEA & LNO",
      description:
        "Submit project idea and request for the Letter of No Objection (LNO)"
    },
    {
      number: "02",
      title: "PDD & LOA",
      description:
        "Design project plan (PDD), and request for the Letter of Authorization (LOA)"
    },
    {
      number: "03",
      title: "IMPLEMENT & MONITOR",
      description:
        "Implement the project and track Emission Reduction (ER) performance."
    },
    {
      number: "04",
      title: "REPORT & VERIFIED",
      description:
        "Submit the performance report, and get verified by (VVB) to receive credits."
    }
  ]

  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8 mx-auto container">
      {/* Header text */}
      <div className="text-center mx-auto mb-12 text-lg">
        <p className="text-gray-700 mb-4">
          The Cambodia National Carbon Registry provides a structured process
          for project proponents to submit and verify Greenhouse Gas (GHG)
          Emission Reduction (ER) projects. This ensures compliance with
          Cambodia's national policies and various international carbon
          mechanisms.
        </p>
        <p className="text-gray-700">
          The process consists of four key steps, ensuring that projects are
          properly reviewed, monitored, and verified before receiving
          recognition and authorization for their GHG ERs.
        </p>
      </div>

      {/* Process steps */}
      <div className="space-y-4">
        {steps.map(step => (
          <ProcessStep
            key={step.number}
            number={step.number}
            title={step.title}
            description={step.description}
          />
        ))}
      </div>
    </div>
  )
}
