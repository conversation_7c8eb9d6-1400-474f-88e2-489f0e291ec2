import { But<PERSON> } from "@heroui/react"
import {
  ActionFunctionArgs,
  json,
  MetaFunction,
  redirect
} from "@remix-run/cloudflare"
import { Form, useActionData, useNavigate } from "@remix-run/react"
import { useTranslation } from "react-i18next"
import ButtonPrimary from "~/components/ui/Button"
import Input from "~/components/ui/input"
import InputPassword from "~/components/ui/inputPassword"
import Select from "~/components/ui/select"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"
import { fieldError } from "~/helpers/form"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { UserValidator } from "~/validators/user"

export const handle = {
  title: "New user",
  backable: true
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function action({ request, context }: ActionFunctionArgs) {
  const payload = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const validator = new UserValidator(env.DB)
  const { data, error } = await validator.validateCreate(payload)
  if (!data) return json({ error, user: null })
  const user = await new UserService(env.DB).createUserBySecretariat({
    ...data,
    status: EStatus.Approve
  })
  if (user) return redirect("/admin/users")
  return json({ error: null, user: null })
}

export default function AdminUsersNew() {
  const actionData = useActionData<typeof action>()
  const error = actionData?.error
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div>
      {actionData && !actionData?.user && !error && (
        <div className="alert alert-error">
          Oops! Something went wrong. Please try again later.
        </div>
      )}
      <div className="max-w-md mx-auto space-y-5">
        <Form method="POST">
          <div className="grid grid-cols-1 gap-y-2">
            <Input
              isRequired
              label={t("auth.registration.first_name")}
              name="firstName"
              placeholder={t("auth.registration.enter_first_name")}
            />
            {error && error.firstName && fieldError(error.firstName[0])}
            <Input
              isRequired
              label={t("auth.registration.last_name")}
              name="lastName"
              placeholder={t("auth.registration.enter_last_name")}
            />
            {error && error.lastName && fieldError(error.lastName[0])}

            <Input
              isRequired
              label={t("auth.registration.email")}
              name="email"
              type="email"
              placeholder={t("auth.registration.enter_email")}
            />
            {error && error.email && fieldError(error.email[0])}

            <InputPassword
              label={t("auth.registration.password")}
              name="password"
              isRequired
              placeholder={t("auth.registration.enter_password")}
            />
            {error && error.password && fieldError(error.password[0])}

            <InputPassword
              label={t("auth.registration.confirm_password")}
              name="confirmPassword"
              isRequired
              placeholder={t("auth.registration.enter_confirm_password")}
            />
            {error &&
              error.confirmPassword &&
              fieldError(error.confirmPassword[0])}
            <Select
              isRequired
              name="role"
              label={t("common.role")}
              placeholder={"Select your project type"}
              data={Object.values(ERole).map(role => ({
                key: role,
                label: t(`user.role.${role}`)
              }))}
            />
            <ButtonPrimary
              type="submit"
              buttonText={t("common.submit")}></ButtonPrimary>
            <Button className=" text-[18px]" onPress={() => navigate(-1)}>
              {t("auth.registration.cancel")}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  )
}
