import {
  type ActionFunction<PERSON>rgs,
  defer,
  json,
  type LoaderFunctionArgs
} from "@remix-run/cloudflare"
import { useLoaderData } from "@remix-run/react"
import NotificationCard from "~/components/notification/NotificationCard"
import Pagination from "~/components/pagination"
import { getAuthenticator } from "~/services/auth"
import NotificationService from "~/services/db/notification"
import type { CloudflareENV } from "~/types"

export async function action({ request, context }: ActionFunctionArgs) {
  const data = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const notificationService = new NotificationService(env.DB)
  try {
    await notificationService.update(Number(data.get("id")))
    return json({ success: true })
  } catch (err) {
    console.log(err)
    return json({ error: true })
  }
}

export async function loader({ context, params, request }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator } = getAuthenticator(env)
  const user = await authenticator.isAuthenticated(request)
  const url = new URL(request.url)
  const page = Number(url.searchParams.get("page") || "1")
  const notifications =
    user?.id &&
    (await new NotificationService(env.DB).getNotificationsByUserId({
      userId: user?.id,
      page,
      pageSize: 12
    }))

  return defer({ user, url, notifications })
}

const Notification = () => {
  const { url, notifications } = useLoaderData<typeof loader>()

  return (
    <div className=" flex justify-center bg-gray-200">
      <div className=" w-2/6 p-4">
        <p className="text-xl font-bold mb-5">Notifications</p>
        {notifications &&
          notifications?.data?.map((notification, index) => (
            <div className="mb-2 rounded-md p-2 bg-gray-100">
              <NotificationCard index={index} notification={notification} />
            </div>
          ))}
        {notifications && notifications.paginate.pageCount > 1 && (
          <div className="mt-10 flex justify-center">
            <Pagination pageUrl={url.toString()} {...notifications.paginate} />
          </div>
        )}
      </div>
    </div>
  )
}

export default Notification
