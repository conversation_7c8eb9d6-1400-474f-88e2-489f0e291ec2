import {
  type ActionFunction<PERSON>rgs,
  defer,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import { Suspense } from "react"
import Loading from "~/components/loading"
import ProjectDetail from "~/components/project/ProjectDetail"
import { ENotification } from "~/enums/ENotification"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"
import CommentService from "~/services/db/comment"
import IdeaNoteService from "~/services/db/ideaNote"
import ImplementationService from "~/services/db/implementation"
import NotificationService from "~/services/db/notification"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = new ProjectService(env.DB).getById(parseInt(id))
  const user = await getAuthUser(context, request)
  const implementationList = await new ImplementationService(
    env.DB
  ).getByProjectId(parseInt(id))
  const commentList = await new CommentService(env.DB).getCommentsByProjectId(
    parseInt(id)
  )
  const pdd = await new PDDService(env.DB).getByProjectID(parseInt(id))
  const locationFile = await env.R2.get(project?.location)
  return defer({
    id,
    project,
    user,
    implementationList,
    commentList,
    pdd,
    locationFile
  })
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()
  const projectId = Number(data.get("id"))
  const pddId = Number(data.get("pdd_id"))
  const projectService = new ProjectService(env.DB)
  const actionType = data.get("action")
  if (actionType === "delete") {
    const project = await projectService.delete(Number(id))
    if (project) return json({ success: true, redirect: "/user/projects" })
  }
  if (actionType === "submit") {
    const project = await projectService.update(Number(id), {
      status: EProjectStatus.PendingLnoRequest
    })
    const url = new URL(request.url)
    const host = url.origin
    const link = `${host}/admin/projects/${id}`
    if (project) {
      const administrators = await new UserService(env.DB).getByRole(
        ERole.Administrator
      )
      await new IdeaNoteService(env.DB).update(project?.idea_note_id, {
        status: EStatus.Pending
      })

      if (administrators) {
        for (const administrator of administrators) {
          try {
            await new MailService(env).NewProjectSubmitEmailNotification(
              // administrator?.email,
              env.RESEND_CONTACT_TO_EMAIL,
              link
            )
            await new NotificationService(env.DB).create({
              type: ENotification.Project,
              user_id: administrator.id,
              data: id,
              link: `/admin/projects/${id}`
            })
          } catch (error) {
            console.error("Error sending email:", error)
          }
        }
      }
      return json({ success: true, redirect: `/user/projects/${id}` })
    }
    return json({ error: true })
  }
  if (actionType === "submit-pdd") {
    await new PDDService(env.DB).update(pddId, {
      status: EStatus.Pending
    })
    return redirect(`/user/projects/${projectId}`)
  }
  return json({ success: true })
}

export default function ViewProject() {
  const { project, user, implementationList, commentList, pdd, locationFile } =
    useLoaderData<typeof loader>()

  return (
    <>
      <Suspense fallback={<Loading />}>
        <Await resolve={project}>
          {project => (
            <ProjectDetail
              locationFile={locationFile}
              implementationList={implementationList}
              pdd={pdd}
              user={user}
              project={project}
              isAddImplementation={true}
              isSubmit={project?.status === EProjectStatus.Draft}
              isEdit={
                project?.status === EProjectStatus.Draft ||
                project?.status === EProjectStatus.Submitted
              }
              isDelete={
                project?.status === EProjectStatus.Draft ||
                project?.status === EProjectStatus.Submitted
              }
              commentList={commentList}
            />
          )}
        </Await>
      </Suspense>
    </>
  )
}
