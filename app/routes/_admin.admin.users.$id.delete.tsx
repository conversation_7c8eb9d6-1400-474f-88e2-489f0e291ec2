import {
  ActionFunctionArgs,
  json,
  LoaderFunctionArgs,
  MetaFunction,
  redirect
} from "@remix-run/cloudflare"
import {
  Await,
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
  useNavigation
} from "@remix-run/react"
import { Suspense } from "react"
import Forehead from "~/components/layout/admin/forehead"
import Loading from "~/components/loading"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"

export const handle = {
  title: "Users",
  backable: true
}

export const meta: MetaFunction = () => {
  return [{ title: handle.title }]
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()
  const userId = Number(data.get("id"))
  const user = await new UserService(env.DB).delete(userId)
  if (user) return redirect("/admin/users")
  return json({ error: true })
}

export async function loader({ context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const user = await new UserService(env.DB).getById(parseInt(id))
  return json({ id, user })
}

export default function DeleteUser() {
  const { id, user } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()
  const navigation = useNavigation()
  const navigate = useNavigate()
  const actionPath = `/admin/users/${id}/delete`
  const isSubmitting = navigation.formAction === actionPath

  return (
    <>
      <Forehead title={`Delete user: ${id}`} backref="/admin/users" />
      {actionData?.error && (
        <div className="alert alert-error my-5">
          Oops! Something went wrong.
        </div>
      )}
      <Suspense fallback={<Loading />}>
        <Await resolve={user}>
          {user => (
            <>
              {user && (
                <div className="max-w-lg mx-auto">
                  <div className="card border border-base-200 shadow-md p-8">
                    <h3 className="text-lg font-bold">
                      Are you sure, you want to delete user id: {user.id}?
                    </h3>
                    <p className="my-5">Delete cannot be undone.</p>
                    <Form method="POST" className="flex gap-1 mt-4">
                      <input type="hidden" name="id" value={user.id} />
                      <button className="btn btn-error" disabled={isSubmitting}>
                        {isSubmitting ? (
                          <span className="flex gap-2 items-center">
                            <span className="loading loading-spinner"></span>
                            <span>Deleting...</span>
                          </span>
                        ) : (
                          "Delete"
                        )}
                      </button>
                      <button
                        type="button"
                        className="btn btn-ghost"
                        disabled={isSubmitting}
                        onClick={() => navigate(-1)}>
                        Cancel
                      </button>
                    </Form>
                  </div>
                </div>
              )}
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}
