import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Select,
  SelectItem,
  SharedSelection
} from "@heroui/react"
import type { defer, json, LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import React, { Suspense } from "react"
import AssignCard from "~/components/assign/assignCard"
import AssignItem from "~/components/assign/assignItem"
import DownloadButton from "~/components/buttonDownload"
import { useDialog } from "~/components/dialog"
import ImplementationDetail from "~/components/implementation"
import Forehead from "~/components/layout/admin/forehead"
import Loading from "~/components/loading"
import Input from "~/components/ui/input"
import { EModules, ERole } from "~/enums/EUserRole"
import { getImage, getKey } from "~/helpers/r2"
import ImplementationService from "~/services/db/implementation"
import ImplementationAssignService from "~/services/db/implementationAssign"
import RequestLPEService from "~/services/db/requestLPE"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { hasPermissions } from "~/utils/permission"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const implementation = await new ImplementationService(env.DB).getById(
    parseInt(id)
  )
  const ImplementationAssignList = await new ImplementationAssignService(
    env.DB
  ).getByImplementationId(parseInt(id))
  const internalReviewerList = await new UserService(env.DB).getByRole(
    ERole.InternalReviewer
  )
  const externalAuditorList = await new UserService(env.DB).getByRole(
    ERole.IndependentAuditor
  )

  const coordinatorList = await new UserService(env.DB).getByRole(
    ERole.Coordinator
  )

  return defer({
    implementation,
    internalReviewerList,
    ImplementationAssignList,
    externalAuditorList,
    user,
    coordinatorList
  })
}

export async function action({ request, context, params }: LoaderFunctionArgs) {
  const id = params.id
  const data = await request.formData()
  const env = context.cloudflare.env as CloudflareENV
  const actionType = data.get("action")
  const implementationId = Number(data.get("id"))
  const implementationService = new ImplementationService(env.DB)
  const file = data.get("support_document")
  const isInternalReviewer = data.get("isInternalReviewer")
  if (actionType === "assign") {
    const assignees = data.getAll("assign")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      implementation_id: implementationId,
      user_ids: assigneesTypeNumber,
      support_document: "",
      isInternalReviewer: isInternalReviewer === "true" ? true : false
    }
    if (file) {
      const fileKey = getKey(file as File, "assign_doucment")
      await env.R2.put(fileKey, data.get("support_document"))
      newAssignData.support_document = fileKey
    }
    await new ImplementationAssignService(env.DB).create(newAssignData)
  }
  if (actionType === "reviewer_approve" || actionType === "reviewer_reject") {
    const implementationAssign_id = Number(data.get("implementation_id"))
    const updateData = {
      status: actionType === "reviewer_approve" ? "approved" : "rejected",
      review_document: ""
    }
    const review_document = data.get("review_document")
    if (review_document) {
      const fileKey = getKey(review_document as File, "review_document")
      await env.R2.put(fileKey, review_document)
      updateData.review_document = fileKey
    }
    try {
      await new ImplementationAssignService(env.DB).update(
        implementationAssign_id,
        updateData
      )
    } catch (error) {
      return { error: true }
    }
  }

  if (actionType === "request_lpe") {
    const assignees = data.getAll("request_from")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      implementation_id: Number(id),
      user_ids: assigneesTypeNumber
    }
    const result = await new RequestLPEService(env.DB).create(newAssignData)

    if (result.length > 0) {
      await implementationService.update(Number(id), {
        status: "LPE_Request__Review"
      })
    } else {
      console.error("No reviewers were successfully assigned.")
    }
    return json({ success: true })
  }

  if (actionType === "approve" || actionType === "reject") {
    try {
      const updateData = {
        status: actionType === "approve" ? "Signed_LPE" : "Rejected_LPE",
        review_implementation_document: ""
      }
      const review_implementation_document = data.get(
        "review_implementation_document"
      )

      if (review_implementation_document) {
        const fileKey = getKey(
          review_implementation_document as File,
          "review_implementation_document"
        )
        await env.R2.put(fileKey, review_implementation_document)
        updateData.review_implementation_document = fileKey
      }
      await implementationService.update(implementationId, updateData)
    } catch (error) {
      return { error: true }
    }
  }
  return json({ success: true })
}

const AdminViewImplementation = () => {
  const {
    implementation,
    internalReviewerList,
    ImplementationAssignList,
    externalAuditorList,
    user,
    coordinatorList
  } = useLoaderData<typeof loader>()
  const { openDialog } = useDialog()
  const implementationAssignInternalReviewer = ImplementationAssignList.filter(
    (item: any) => item?.isInternalReviewer
  )
  const implementationAssignExternalAuditor = ImplementationAssignList.filter(
    (item: any) => !item?.isInternalReviewer
  )
  const implementationByUser = ImplementationAssignList.filter(
    (item: any) => item?.user_id === user?.id
  )

  const AssignComponent = ({
    isInternalReviewer
  }: {
    isInternalReviewer: any
  }) => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={
            isInternalReviewer ? internalReviewerList : externalAuditorList
          }
          label="Assigned to"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>
        <Input required type="file" name="support_document" />
        <input type="hidden" name="id" value={implementation?.id} />
        <input
          type="hidden"
          name="isInternalReviewer"
          value={isInternalReviewer}
        />
        <input type="hidden" name="action" value="assign" />
        {Array.from(values).map((value, index) => (
          <input name="assign" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }

  const handleAssign = ({
    isInternalReviewer
  }: {
    isInternalReviewer?: any
  }) => {
    openDialog({
      title: "Assign",
      component: <AssignComponent isInternalReviewer={isInternalReviewer} />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/performance-reports/${implementation?.id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: "Assign"
      }
    })
  }

  const onApproveRejectImplementation = ({
    approve,
    id,
    name
  }: {
    approve: boolean
    id: any
    name: any
  }) => {
    const approveRejectLabel = approve ? "Approve" : "Reject"
    openDialog({
      title: approveRejectLabel,
      component: (
        <>
          <DownloadButton
            projectID={Number(implementation?.project_id)}
            projectName={name}
            type="letter-of-positive-examination"
          />
          <input type="hidden" name="id" value={id} />
          <input
            type="hidden"
            name="action"
            value={approve ? "approve" : "reject"}
          />
          <Input required type="file" name="review_implementation_document" />
        </>
      ),
      submitOption: {
        options: {
          method: "post",
          action: `/admin/performance-reports/${id}`,
          encType: "multipart/form-data"
        }
      },
      props: {
        primaryButtonText: approveRejectLabel
      }
    })
  }
  const RequestLPEComponent = () => {
    const [values, setValues] = React.useState<SharedSelection>(new Set())
    return (
      <>
        <Select
          className="w-full"
          isMultiline={true}
          items={coordinatorList}
          label="Request LNO from"
          labelPlacement="outside"
          placeholder="Select a user"
          selectedKeys={values}
          onSelectionChange={setValues}
          renderValue={items => (
            <div className="flex flex-wrap gap-2">
              {items.map((item: any, index: number) => (
                <Chip key={index}>
                  {item.data.firstName} {item.data.lastName}
                </Chip>
              ))}
            </div>
          )}
          selectionMode="multiple"
          variant="bordered">
          {(user: any) => (
            <SelectItem key={user.id}>
              <div className="flex gap-2 items-center">
                <div className="flex flex-col">
                  <span className="text-small">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
            </SelectItem>
          )}
        </Select>
        <input
          type="hidden"
          name="project_id"
          value={Number(implementation?.project_id)}
        />
        <input type="hidden" name="action" value="request_lpe" />
        {Array.from(values).map((value, index) => (
          <input name="request_from" value={value} type="hidden" key={value} />
        ))}
      </>
    )
  }
  const onRequestLPEReview = () => {
    openDialog({
      title: "Request LPE Review",
      component: <RequestLPEComponent />,
      submitOption: {
        options: {
          method: "post",
          action: `/admin/performance-reports/${implementation?.id}`,
          encType: "multipart/form-data"
        }
      }
    })
  }

  return (
    <>
      <Suspense fallback={<Loading />}>
        <Await resolve={implementation}>
          {implementation => (
            <>
              <Forehead
                title="Implementation Detail"
                backref={`/admin/performance-reports`}
              />
              <ImplementationDetail
                implementation={implementation}
                linkToProject={"/admin/projects/" + implementation?.project_id}
              />
              {(user.role === ERole.InternalReviewer ||
                user.role === ERole.IndependentAuditor) &&
                implementationByUser.length > 0 && (
                  <div>
                    <Card className="p-4 my-4">
                      <div className=" flex justify-between items-center">
                        <div className="mb-2">
                          {user.role === ERole.InternalReviewer
                            ? "Internal Reviewer"
                            : "External Auditor"}
                        </div>
                      </div>
                      <AssignCard
                        assignList={implementationByUser}
                        data={implementation}
                        type="implementation"
                      />
                    </Card>
                    {implementationByUser[0]?.support_document && (
                      <>
                        <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                          Doucment:&nbsp;
                          <Link
                            className="text-black"
                            target="_blank"
                            href={getImage(
                              implementationByUser[0]?.support_document
                            )}>
                            Support Document
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                )}
              {hasPermissions(user?.role, [
                EModules.ReviewPerformanceReport
              ]) && (
                <>
                  <div>
                    <Card className="p-4 my-4">
                      <div className=" flex justify-between items-center">
                        <div>Internal Reviewer</div>
                      </div>
                      {implementationAssignInternalReviewer?.length > 0 ? (
                        implementationAssignInternalReviewer?.map(
                          (item, index) => {
                            return (
                              <div key={index}>
                                <div className="bg-gray-200 p-3 grid gap-3 rounded-md my-2">
                                  <AssignItem item={item} />
                                </div>
                              </div>
                            )
                          }
                        )
                      ) : (
                        <>
                          <div className="flex justify-center">
                            {implementation?.status === "LPE_Request"
                              ? "  There is no Internal Reviewer Assign yet, please Assign by click button below"
                              : "There is not Internal Reviewer Assign"}
                          </div>
                          {implementation?.status === "LPE_Request" && (
                            <div className="flex justify-center mt-3">
                              <Button
                                color="primary"
                                type="submit"
                                className="bg-primary text-white"
                                onPress={() =>
                                  handleAssign({ isInternalReviewer: true })
                                }>
                                Assign
                              </Button>
                            </div>
                          )}
                        </>
                      )}
                    </Card>
                  </div>
                  <div>
                    {implementationAssignInternalReviewer[0]
                      ?.support_document && (
                      <>
                        <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                          Internal Reviewer Doucment:&nbsp;
                          <Link
                            className="text-black"
                            target="_blank"
                            href={getImage(
                              implementationAssignInternalReviewer[0]
                                .support_document
                            )}>
                            Supprt Doucment
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                  <div>
                    <Card className="p-4 my-4">
                      <div className=" flex justify-between items-center">
                        <div>External Auditor</div>
                      </div>
                      {implementationAssignExternalAuditor?.length > 0 ? (
                        <>
                          {implementationAssignExternalAuditor?.map(
                            (item, index) => {
                              return (
                                <div key={index}>
                                  <div className="bg-gray-200 p-3 grid gap-3 rounded-md my-2">
                                    <AssignItem item={item} />
                                  </div>
                                </div>
                              )
                            }
                          )}
                        </>
                      ) : (
                        <>
                          <div className="flex justify-center">
                            {implementation?.status === "LPE_Request"
                              ? " There is no External Auditor Assign yet, please Assign by click button below"
                              : "There is not External Auditor Assign"}
                          </div>
                          {implementation?.status === "LPE_Request" && (
                            <div className="flex justify-center mt-3">
                              <Button
                                color="primary"
                                type="submit"
                                className="bg-primary text-white"
                                onPress={() =>
                                  handleAssign({ isInternalReviewer: false })
                                }>
                                Assign
                              </Button>
                            </div>
                          )}
                        </>
                      )}
                    </Card>
                  </div>
                  <div>
                    {implementationAssignExternalAuditor[0]
                      ?.support_document && (
                      <>
                        <div className="bg-blue-500 p-3 rounded-md mt-4 mb-4">
                          External Auditor Doucment:&nbsp;
                          <Link
                            className="text-black"
                            target="_blank"
                            href={getImage(
                              implementationAssignExternalAuditor[0]
                                .support_document
                            )}>
                            Support Document
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                </>
              )}

              {hasPermissions(user?.role, [EModules.ReviewPerformanceReport]) &&
                implementation?.status === "LPE_Request" && (
                  <div className="flex justify-end">
                    <Button
                      color="primary"
                      onPress={() => {
                        onRequestLPEReview()
                      }}>
                      Request LPE Reivew
                    </Button>
                  </div>
                )}
              {hasPermissions(user?.role, [
                EModules.ApproveAndRejectReviewformanceReport
              ]) &&
                implementation?.status === "LPE_Request__Review" && (
                  <>
                    <DownloadButton
                      projectID={Number(implementation?.project_id)}
                      projectName={implementation?.project?.name ?? ""}
                      type="letter-of-positive-examination"
                    />
                    <div className="flex justify-end space-x-3">
                      <Button
                        color="primary"
                        onPress={() => {
                          onApproveRejectImplementation({
                            approve: true,
                            id: implementation?.id,
                            name: implementation?.project?.name
                          })
                        }}>
                        Approve
                      </Button>
                      <Button
                        color="danger"
                        onPress={() => {
                          onApproveRejectImplementation({
                            approve: false,
                            id: implementation?.id,
                            name: implementation?.project?.name
                          })
                        }}>
                        Reject
                      </Button>
                    </div>
                  </>
                )}
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}

export default AdminViewImplementation
