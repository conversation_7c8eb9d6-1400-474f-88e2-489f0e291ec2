import { LoaderFunctionArgs } from "@remix-run/cloudflare"
import { getAuthenticator } from "~/services/auth"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const { authenticator } = getAuthenticator(
    context.cloudflare.env as CloudflareENV
  )
  await authenticator.logout(request, { redirectTo: "/login" })
}

export function Logout() {
  return <></>
}
