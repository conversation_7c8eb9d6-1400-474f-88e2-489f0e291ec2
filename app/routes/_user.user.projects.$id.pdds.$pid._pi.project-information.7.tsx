import { <PERSON><PERSON> } from "@heroui/react"
import { type ActionFunctionArgs, redirect } from "@remix-run/cloudflare"
import {
  Form,
  useNavigate,
  useParams,
  useRouteLoaderData
} from "@remix-run/react"
import NoteInfo from "~/components/projectRegistration/NoteInfo"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import { getKey } from "~/helpers/r2"
import PDDService from "~/services/db/pdd"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const formData = await request.formData()
  const data = Object.fromEntries(formData)
  const action = data?.action
  const env = context.cloudflare.env as CloudflareENV
  const newData = {
    ...data,
    step: `/user/projects/${params.id}/pdds/${params.pid}/project-information/7`
  }
  if (data?.community_consultation_records_file) {
    const fileKey = getKey(
      data?.community_consultation_records_file,
      "community_consultation_records_file"
    )
    await env.R2.put(fileKey, data?.community_consultation_records_file)
    newData.community_consultation_records_file = fileKey
  }
  params?.pid &&
    (await new PDDService(env.DB).update(parseInt(params?.pid), newData))
  if (action === "save-and-continue") {
    return redirect(
      `/user/projects/${params.id}/pdds/${params.pid}/project-information/8`
    )
  } else {
    return redirect(`/pdds/${params.pid}`)
  }
}

const ProjectInformation = () => {
  const params = useParams()
  const { pdd } = useRouteLoaderData<any>(
    "routes/_user.user.projects.$id.pdds.$pid"
  )
  const navigate = useNavigate()
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid w-full gap-y-3">
        <div className="">
          <div className="flex gap-x-8">
            <div className="w-[900px]">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2">
                <div className="col-span-2">
                  <TextArea
                    label="Community Involvement"
                    placeholder="Describe the engagement process with local communities, including consultations and benefit-sharing mechanisms."
                    name="community_involvement"
                    minRows={5}
                    defaultValue={pdd?.community_involvement}
                  />
                </div>
                <div className="col-span-2">
                  <TextArea
                    label="Stakeholder Consultations"
                    placeholder="Include records of consultation sessions and feedback."
                    name="stakeholder_consultations"
                    minRows={5}
                    defaultValue={pdd?.stakeholder_consultations}
                  />
                </div>
                <div className="col-span-2">
                  <FileSelect
                    label="Community Consultation Records"
                    name="community_consultation_records_file"
                    fileNameSelected={pdd?.community_consultation_records_file}
                    acceptFileTypes=".pdf"
                  />
                </div>
              </div>
            </div>
            <div className="flex-1">
              <NoteInfo />
            </div>
          </div>
        </div>
        <div className="w-[900px] flex gap-x-3">
          <Button
            className="bg-primary text-white"
            name="action"
            value="save-and-continue"
            type="submit">
            Save & Continue
          </Button>
          <Button
            variant="bordered"
            color="primary"
            name="action"
            value="save"
            type="submit">
            Save
          </Button>
          <Button
            className="bg-transparent"
            onPress={() => {
              navigate(`/pdds/${params.pid}`)
            }}>
            Cancel
          </Button>
        </div>
      </div>
    </Form>
  )
}

export default ProjectInformation
