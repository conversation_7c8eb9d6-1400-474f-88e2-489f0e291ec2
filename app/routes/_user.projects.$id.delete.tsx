import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import {
  Await,
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
  useNavigation
} from "@remix-run/react"
import { Suspense } from "react"
import Forehead from "~/components/layout/admin/forehead"
import Loading from "~/components/loading"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()
  const projectId = Number(data.get("id"))
  const project = await new ProjectService(env.DB).delete(projectId)
  if (project) return redirect("/user/projects")
  return json({ error: true })
}

export async function loader({ context, params }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = await new ProjectService(env.DB).getById(parseInt(id))
  return json({ id, project })
}

export default function DeleteProject() {
  const { id, project } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()
  const navigation = useNavigation()
  const navigate = useNavigate()
  const actionPath = `/projects/${id}/delete`
  const isSubmitting = navigation.formAction === actionPath

  return (
    <>
      <Forehead title={`Delete project: ${id}`} backref="/projects" />
      {actionData?.error && (
        <div className="alert alert-error my-5">
          Oops! Something went wrong.
        </div>
      )}
      <Suspense fallback={<Loading />}>
        <Await resolve={project}>
          {project => (
            <>
              {project && (
                <div className="max-w-lg mx-auto">
                  <div className="card border border-base-200 shadow-md p-8">
                    <h3 className="text-lg font-bold">
                      Are you sure, you want to delete project id: {project.id}?
                    </h3>
                    <p className="my-5">Delete cannot be undone.</p>
                    <Form method="POST" className="flex gap-1 mt-4">
                      <input type="hidden" name="id" value={project.id} />
                      <button className="btn btn-error" disabled={isSubmitting}>
                        {isSubmitting ? (
                          <span className="flex gap-2 items-center">
                            <span className="loading loading-spinner"></span>
                            <span>Deleting...</span>
                          </span>
                        ) : (
                          "Delete"
                        )}
                      </button>
                      <button
                        type="button"
                        className="btn btn-ghost"
                        disabled={isSubmitting}
                        onClick={() => navigate(-1)}>
                        Cancel
                      </button>
                    </Form>
                  </div>
                </div>
              )}
            </>
          )}
        </Await>
      </Suspense>
    </>
  )
}
