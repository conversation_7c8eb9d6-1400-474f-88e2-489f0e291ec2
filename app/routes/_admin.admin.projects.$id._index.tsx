import {
  ActionFunctionArgs,
  defer,
  json,
  LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Await, useLoaderData } from "@remix-run/react"
import { Suspense } from "react"
import Loading from "~/components/loading"
import ProjectDetail from "~/components/project/ProjectDetail"
import { ENotification } from "~/enums/ENotification"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { EStatus } from "~/enums/EStatus"
import { ERole } from "~/enums/EUserRole"
import { getKey } from "~/helpers/r2"
import AssignService from "~/services/db/assign"
import CommentService from "~/services/db/comment"
import IdeaNoteService from "~/services/db/ideaNote"
import ImplementationService from "~/services/db/implementation"
import NotificationService from "~/services/db/notification"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import RequestLNOService from "~/services/db/requestLON"
import UserService from "~/services/db/user"
import { MailService } from "~/services/mail"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ context, params, request }: LoaderFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const project = new ProjectService(env.DB).getById(parseInt(id))
  const implementationList = await new ImplementationService(
    env.DB
  ).getByProjectId(parseInt(id))
  const pdd = await new PDDService(env.DB).getByProjectID(parseInt(id))
  const assingList = await new AssignService(env.DB).getByProjectId(
    parseInt(id)
  )

  const user = await getAuthUser(context, request)

  const assignInternal = assingList?.filter(
    (item: any) => item?.isInternalReviewer === true
  )
  const assingExternal = assingList?.filter(
    (item: any) => item?.isInternalReviewer === false
  )

  const assignReviewProjectByUser = assingList?.filter(
    (item: any) => item?.user_id === user?.id
  )

  const commentList = await new CommentService(env.DB).getCommentsByProjectId(
    parseInt(id)
  )

  const users = await new UserService(env.DB).getByRoles([
    ERole.Coordinator,
    ERole.InternalReviewer,
    ERole.IndependentAuditor
  ])
  const coordinatorList = users?.filter(u => u.role === ERole.Coordinator)
  const externalAuditorList = users?.filter(
    u => u.role === ERole.IndependentAuditor
  )
  const internalReviewerList = users?.filter(
    u => u.role === ERole.InternalReviewer
  )
  return defer({
    id,
    project,
    user,
    internalReviewerList,
    externalAuditorList,
    coordinatorList,
    assingList,
    assignInternal,
    assingExternal,
    assignReviewProjectByUser,
    implementationList,
    commentList,
    pdd
  })
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const id = params.id
  if (!id) throw new Response("Not found", { status: 404 })
  const env = context.cloudflare.env as CloudflareENV
  const data = await request.formData()
  if (!data) return null
  const projectId = Number(data.get("id"))
  const projectService = new ProjectService(env.DB)
  const actionType = data.get("action")
  const file = data.get("support_document")
  const url = new URL(request.url)
  const host = url.origin
  const link = `${host}/admin/projects/${projectId}`
  const notificationLink = `/user/projects/${projectId}`
  const isInternalReviewer = data.get("isInternalReviewer")

  if (actionType === "review") {
    const project = await projectService.update(projectId, {
      status: EProjectStatus.PendingReviewLnoRequest
    })
    if (project) {
      new MailService(env).ProjectHasBeenReviewEmailNotification(
        env.RESEND_CONTACT_TO_EMAIL
      )
      await new NotificationService(env.DB).create({
        type: ENotification.Review,
        user_id: project.user_id,
        data: projectId,
        link: notificationLink
      })
      return redirect(`/admin/projects/${projectId}`)
    }
    return json({ error: true })
  }
  if (actionType === "feedback") {
    return null
  }
  if (actionType === "assign") {
    const assignees = data.getAll("assign")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      project_id: projectId,
      user_ids: assigneesTypeNumber,
      support_document: "",
      isInternalReviewer: isInternalReviewer === "true" ? true : false
    }

    if (file) {
      const fileKey = getKey(file as File, "assign_doucment")
      await env.R2.put(fileKey, data.get("support_document"))
      newAssignData.support_document = fileKey
    }
    const result = await new AssignService(env.DB).create(newAssignData)
    if (result.length > 0) {
      for (const item of result) {
        try {
          await new MailService(env).AssignReviewersEmailNotification(
            //assing to email of assignee
            env.RESEND_CONTACT_TO_EMAIL,
            link
          )
          await new NotificationService(env.DB).create({
            type: ENotification.Assign,
            user_id: item.user_id,
            data: item.id,
            link: `/admin/projects/${projectId}`
          })
        } catch (error) {
          console.error("Error sending email:", error)
        }
      }
    } else {
      console.error("No reviewers were successfully assigned.")
    }
  }
  if (actionType === "invalid") {
    const reason = data.get("reason")
    const project = await projectService.update(projectId, {
      reject_reason: reason,
      status: EProjectStatus.Rejected
    })
    await new IdeaNoteService(env.DB).update(project?.idea_note_id, {
      status: EStatus.Reject
    })
  }
  if (actionType === "request_lno") {
    const assignees = data.getAll("request_from")
    const assigneesTypeNumber = assignees.map(item => Number(item))
    const newAssignData = {
      project_id: projectId,
      user_ids: assigneesTypeNumber
    }
    const result = await new RequestLNOService(env.DB).create(newAssignData)

    if (result.length > 0) {
      await projectService.update(projectId, {
        status: EProjectStatus.LnoIssuingRequest
      })
    } else {
      console.error("No reviewers were successfully assigned.")
    }
  }

  if (actionType === "reviewer_approve" || actionType === "reviewer_reject") {
    const assign_id = Number(data.get("assign_id"))
    const updateData = {
      status: actionType === "reviewer_approve" ? "approved" : "rejected",
      review_document: ""
    }
    const review_document = data.get("review_document")
    if (review_document) {
      const fileKey = getKey(review_document as File, "review_document")
      await env.R2.put(fileKey, review_document)
      updateData.review_document = fileKey
    }
    try {
      await new AssignService(env.DB).update(assign_id, updateData)
      const administrators = await new UserService(env.DB).getByRole(
        ERole.Administrator
      )
      if (administrators) {
        for (const administrator of administrators) {
          try {
            await new MailService(env).AssigneeApproveRejectEmailNotification(
              // administrator?.email,
              env.RESEND_CONTACT_TO_EMAIL,
              link,
              actionType === "reviewer_approve" ? true : false
            )
            await new NotificationService(env.DB).create({
              type:
                actionType === "reviewer_approve"
                  ? ENotification.ReviewApproved
                  : ENotification.ReviewRejected,
              user_id: administrator.id,
              data: assign_id,
              link: `/admin/projects/${id}`
            })
          } catch (error) {
            console.error("Error sending email:", error)
          }
        }
      }
    } catch (error) {
      return { error: true }
    }
  }
  if (actionType === "approve" || actionType === "reject") {
    try {
      const updateData = {
        status:
          actionType === "approve"
            ? EProjectStatus.SignedLNO
            : EProjectStatus.Rejected,
        review_project_document: ""
      }
      const review_project_document = data.get("review_project_document")
      if (review_project_document) {
        const fileKey = getKey(
          review_project_document as File,
          "review_project_document"
        )
        await env.R2.put(fileKey, review_project_document)
        updateData.review_project_document = fileKey
      }
      const reason = data.get("reason")
      if (reason) {
        updateData.reject_reason = reason
      }
      const project = await projectService.update(projectId, updateData)
      project?.idea_note_id &&
        (await new IdeaNoteService(env.DB).update(project?.idea_note_id, {
          status: actionType === "approve" ? EStatus.Approve : EStatus.Reject
        }))
      if (project) {
        new MailService(env).ProjectHasBeenApproveRejectEmailNotification(
          env.RESEND_CONTACT_TO_EMAIL,
          true
        )
        await new NotificationService(env.DB).create({
          type:
            actionType === "approve"
              ? ENotification.ProjectApproved
              : ENotification.ProjectRejected,
          user_id: project?.user_id,
          data: project?.id,
          link: notificationLink
        })
      }
    } catch (error) {
      return { error: true }
    }
  }
  return json({ success: true })
}

const AdminViewProject = () => {
  const {
    project,
    user,
    internalReviewerList,
    externalAuditorList,
    assingList,
    implementationList,
    commentList,
    coordinatorList,
    pdd
  } = useLoaderData<typeof loader>()

  return (
    <div>
      <Suspense fallback={<Loading />}>
        <Await resolve={project}>
          {project => (
            <ProjectDetail
              implementationList={implementationList}
              assingList={assingList}
              internalReviewerList={internalReviewerList}
              externalAuditorList={externalAuditorList}
              coordinatorList={coordinatorList}
              user={user}
              pdd={pdd}
              project={project}
              isReview={project?.status === EProjectStatus.PendingLnoRequest}
              isApproveReject={
                project?.status === EProjectStatus.LnoIssuingRequest
              }
              isFeedback={
                project?.status === EProjectStatus.PendingReviewLnoRequest
              }
              commentList={commentList}
            />
          )}
        </Await>
      </Suspense>
    </div>
  )
}

export default AdminViewProject
