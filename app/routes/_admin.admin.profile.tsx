import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import {
  ActionFunctionArgs,
  json,
  LoaderFunctionArgs
} from "@remix-run/cloudflare"
import { Form, useFetcher, useLoaderData } from "@remix-run/react"
import { User } from "lucide-react"
import { useState } from "react"
import { useDialog } from "~/components/dialog"
import InformationItem from "~/components/profile/InformationItem"
import ChangePasswordComponent from "~/components/userProfile/ChangePassword"
import { getImage, getKey } from "~/helpers/r2"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const UserDetail = await new UserService(env.DB).getById(user.id)
  return json({ user, UserDetail })
}

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)

  const formData = await request.formData()
  const file = formData.get("cover_img") as File | null

  if (!file) {
    return json({ success: false, error: "No file uploaded" })
  }

  try {
    const fileKey = getKey(file, "profile_cover")
    await env.R2.put(fileKey, file)
    const photoUrl = fileKey
    await new UserService(env.DB).updateUserInformation(user.id, {
      photoUrl
    })
    return json({ success: true })
  } catch (error) {
    console.error("Image upload error:", error)
    return json({ success: false, error: "Failed to upload image" })
  }
}

const AdminProfile = () => {
  const { user, UserDetail } = useLoaderData<typeof loader>()
  const { openDialog } = useDialog()
  const userDetailInformation = [
    { name: "First Name", value: user.firstName },
    { name: "Last Name", value: user.lastName },
    { name: "Email", value: user.email },
    { name: "Role", value: user.role }
  ]

  const handleChangePassword = ({}) => {
    openDialog({
      title: "Change Password",
      component: <ChangePasswordComponent user={user} />,
      submitOption: {
        options: {
          method: "post",
          action: "/reset-password"
        },
        fetcherKey: "reset-password"
      }
    })
  }

  const fetcher = useFetcher()
  const [cover, setCover] = useState<string | null>(
    getImage(UserDetail?.photoUrl)
  )

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const file = e.target.files[0]
      setCover(URL.createObjectURL(file))
      const formData = new FormData()
      formData.append("cover_img", file)
      fetcher.submit(formData, {
        method: "post",
        encType: "multipart/form-data"
      })
    }
  }

  return (
    <>
      <div className="grid grid-cols-3 space-x-3 gap-y-4">
        <Form method="POST" encType="multipart/form-data">
          <div className="col-span-3  lg:col-span-1 space-y-2">
            <div className="w-full h-[420px] flex justify-center">
              <Avatar
                className="w-96 h-96 text-large border-4 border-gray-300"
                src={
                  cover
                    ? cover
                    : "https://i.pinimg.com/736x/f8/06/3f/f8063f5eaa670e1c3f4d6eb590256ae5.jpg"
                }
              />
              <input
                id="cover_img"
                type="file"
                name="cover_img"
                hidden
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>
            <div className="flex justify-center">
              <Button
                type="submit"
                as="label"
                htmlFor="cover_img"
                color="primary">
                Change Profile
              </Button>
            </div>
          </div>
        </Form>
        <div className="col-span-3 lg:col-span-2 space-y-4">
          <Card className="rounded-lg p-4 space-y-3">
            <div className="flex space-x-2">
              <User className="size-6 lg:size-8" />
              <p className="text-[16px] lg:text-[20px] font-bold flex items-center">
                User Detail
              </p>
            </div>
            <div className="space-y-2">
              {userDetailInformation.map((item, index) => {
                return <InformationItem item={item} index={index} />
              })}
            </div>
          </Card>
          <div className="flex justify-end space-x-2">
            <Button color="primary" onPress={handleChangePassword}>
              Change Password
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

export default AdminProfile
