import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react"
import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs
} from "@remix-run/cloudflare"
import { Form, useFetcher, useLoaderData } from "@remix-run/react"
import { Landmark, User } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useDialog } from "~/components/dialog"
import InformationItem from "~/components/profile/InformationItem"
import ChangePasswordComponent from "~/components/userProfile/ChangePassword"
import { getImage, getKey } from "~/helpers/r2"
import { getAuthenticator } from "~/services/auth"
import OrganizationService from "~/services/db/organization"
import OverviewService from "~/services/db/overview"
import UserService from "~/services/db/user"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator } = getAuthenticator(env)
  const user = await authenticator.isAuthenticated(request)
  const UserDetail = await new UserService(env.DB).getById(user.id)
  const organization =
    user?.org_id &&
    (await new OrganizationService(env.DB).getById(user?.org_id))

  const projectsOverview =
    user?.id &&
    (await new OverviewService(env.DB).getProjectOverview({
      user_id: user.id
    }))
  const carbonCreditOverview =
    user?.id &&
    (await new OverviewService(env.DB).getCarbonCreditOverview({
      user_id: user.id
    }))
  return json({
    user,
    UserDetail,
    organization,
    carbonCreditOverview,
    projectsOverview
  })
}

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const formData = await request.formData()
  const file = formData.get("cover_img") as File | null

  if (!file) {
    return json({ success: false, error: "No file uploaded" })
  }

  try {
    const fileKey = getKey(file, "profile_cover")
    await env.R2.put(fileKey, file)
    const photoUrl = fileKey
    await new UserService(env.DB).updateUserInformation(user.id, {
      photoUrl
    })
    return json({ success: true })
  } catch (error) {
    console.error("Image upload error:", error)
    return json({ success: false, error: "Failed to upload image" })
  }
}

const UserProfile = () => {
  const {
    user,
    organization,
    UserDetail,
    carbonCreditOverview,
    projectsOverview
  } = useLoaderData<typeof loader>()
  const { t } = useTranslation()
  const userDetailInformation = [
    { name: "Name", value: user?.lastName },
    { name: "Email", value: user?.email },
    { name: "National", value: user?.nationality },
    { name: "Role", value: t(`user.role.${user?.role}`) },
    { name: "Job Title", value: user?.job_title },
    {
      name: "Passport ID",
      value: user?.passport_id ? user?.passport_id : "N/A"
    }
  ]
  const organizationDetailInformation = [
    {
      name: "Organization Name",
      value: organization?.name
    },
    { name: "Address", value: organization?.address },
    { name: "Province", value: organization?.province },
    { name: "District", value: organization?.district },
    { name: "Commune", value: organization?.commune },
    { name: "Postal Code", value: organization?.postal_code },
    { name: "Organization Email", value: organization?.email },
    { name: "Organization Phone", value: organization?.phone },
    { name: "Organization Website", value: organization?.website },
    { name: "Number of Project", value: projectsOverview?.totalProject || 0 },
    {
      name: "Credit Balance",
      value: carbonCreditOverview?.totalIssuedCarbon || 0
    }
  ]
  const { openDialog } = useDialog()

  const handleChangePassword = ({}) => {
    openDialog({
      title: "Change Password",
      component: <ChangePasswordComponent user={user} />,
      submitOption: {
        options: {
          method: "post",
          action: "/reset-password"
        },
        fetcherKey: "reset-password"
      }
    })
  }

  const fetcher = useFetcher({ key: "reset-password" })
  const { data, state } = useFetcher<{ error: any }>({
    key: "reset-password"
  })

  const [cover, setCover] = useState<string | null>(
    getImage(UserDetail?.photoUrl)
  )

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const file = e.target.files[0]
      setCover(URL.createObjectURL(file))
      const formData = new FormData()
      formData.append("cover_img", file)
      fetcher.submit(formData, {
        method: "post",
        encType: "multipart/form-data"
      })
    }
  }

  return (
    <>
      <div className="grid grid-cols-3 space-x-3 gap-y-4">
        <Form method="POST" encType="multipart/form-data">
          <div className="col-span-3  lg:col-span-1 space-y-2">
            <div className="flex justify-center">
              <Avatar
                className="w-96 h-96  text-large border-4 border-gray-300"
                src={
                  cover
                    ? cover
                    : "https://i.pinimg.com/736x/f8/06/3f/f8063f5eaa670e1c3f4d6eb590256ae5.jpg"
                }
              />
              <input
                id="cover_img"
                type="file"
                name="cover_img"
                hidden
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>
            <div className="flex justify-center">
              <Button
                type="submit"
                as="label"
                htmlFor="cover_img"
                color="primary">
                Change Profile
              </Button>
            </div>
          </div>
        </Form>
        <div className="col-span-3 lg:col-span-2 space-y-4">
          <Card className="rounded-lg p-4 space-y-3">
            <div className="flex space-x-2">
              <User className="size-6 lg:size-8" />
              <p className="text-[16px] lg:text-[20px] font-bold flex items-center">
                User Detail
              </p>
            </div>
            <div className="space-y-2">
              {userDetailInformation.map((item, index) => {
                return <InformationItem item={item} index={index} key={index} />
              })}
            </div>
          </Card>
          <Card className="rounded-lg p-4 space-y-3">
            <div className="flex space-x-2">
              <Landmark className="size-6 lg:size-8" />
              <p className="text-[16px] lg:text-[20px] font-bold flex items-center">
                Organization Detail
              </p>
            </div>
            <div className="space-y-2">
              {organizationDetailInformation.map((item, index) => {
                return <InformationItem item={item} index={index} key={index} />
              })}
            </div>
          </Card>
          <div className="flex justify-end space-x-2">
            <Button color="primary" onPress={handleChangePassword}>
              Change Password
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

export default UserProfile
