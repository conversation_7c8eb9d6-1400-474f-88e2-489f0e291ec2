import { json, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Outlet, useLocation } from "@remix-run/react"
import { PDD_LAYOUT_STEPS } from "~/enums/EPddStep"
import OrganizationService from "~/services/db/organization"
import PDDService from "~/services/db/pdd"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"

export async function loader({ request, params, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const project = await new ProjectService(env.DB).getById(params.id)
  const pdd = await new PDDService(env.DB).getById(params.pid)
  const org = await new OrganizationService(env.DB).getById(project?.org_id)
  return json({ project, pdd, org })
}

const pddLayoutStep = [
  {
    label: "Project Information",
    value: PDD_LAYOUT_STEPS.PROJECT_INFO
  },
  {
    label: "Project Design",
    value: PDD_LAYOUT_STEPS.PROJECT_DESIGN
  },
  {
    label: "LOA Request",
    value: PDD_LAYOUT_STEPS.LOA_REQUEST
  }
]

export default function PDDForm() {
  const location = useLocation()
  const activeStep = pddLayoutStep.find(item => {
    const paths = location.pathname.split("/")
    return paths.includes(item.value)
  })

  return (
    <div className="px-10">
      <Stepper activeStep={activeStep?.value as string} steps={pddLayoutStep} />
      <Outlet />
    </div>
  )
}

type Step = {
  label: string
  value?: string
}

type StepperProps = {
  activeStep: string
  steps: Step[]
}

const Stepper = ({ steps, activeStep }: StepperProps) => {
  return (
    <div className="border-y flex items-center gap-8 py-4">
      {steps.map((step, index) => {
        const isActive = step.value === activeStep

        return (
          <>
            <div key={index} className={!isActive ? "opacity-50 " : ""}>
              <p
                className={`flex space-x-2 text-sm ${
                  isActive ? "font-bold" : ""
                }`}>
                <span className={isActive ? "text-primary" : ""}>
                  0{index + 1}
                </span>
                <span>{step.label}</span>
              </p>
            </div>
            {index < steps.length - 1 ? <div className="border flex-1" /> : ""}
          </>
        )
      })}
    </div>
  )
}
