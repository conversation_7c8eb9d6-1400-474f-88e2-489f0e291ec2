import {
  type ActionFunction<PERSON>rgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Form, useLoaderData } from "@remix-run/react"
import { useState } from "react"
import Forehead from "~/components/layout/admin/forehead"
import ProjectRegistrationForm from "~/components/projectRegistration/ProjectRegistrationForm"
import SelectProjectSectorStep from "~/components/projectRegistration/SelectProjectSector"
import { EProjectStatus } from "~/enums/EProjectStatus"
import { getKey } from "~/helpers/r2"
import ProjectService from "~/services/db/project"
import ProjectSectorService from "~/services/db/projectService"
import ProjectTypeService from "~/services/db/projectType"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"
import { ProjectValidator } from "~/validators/project"

export async function action({ request, context }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const payload = await request.formData()
  const validator = new ProjectValidator(env.DB)
  const { data, error } = await validator.validateCreate(payload)
  const user = await getAuthUser(context, request)

  if (!data) return json({ error, saved: null })

  const newProjectData = {
    ...data,
    cover_img: "",
    location: "",
    user_id: user.id,
    status: EProjectStatus.Draft
  }

  if (data?.cover_img) {
    const fileKey = getKey(data.cover_img, "project_cover")
    await env.R2.put(fileKey, data.cover_img)

    newProjectData.cover_img = fileKey
  }

  if (data?.location) {
    const fileKey = getKey(data?.location, "project_location")
    await env.R2.put(fileKey, data?.location)
    newProjectData.location = fileKey
  }

  const result = await new ProjectService(env.DB).create(newProjectData)

  if (!result?.project?.id) {
    return json({ error: "project create unsuccessfully", saved: null })
  }
  return redirect("/user/projects/" + result.project.id)
}

export const loader = async ({ context }: LoaderFunctionArgs) => {
  const env = context.cloudflare.env as CloudflareENV
  const sectors = await new ProjectSectorService(env.DB).getMany()
  const types = await new ProjectTypeService(env.DB).getMany()
  return json({ sectors, types })
}

export default function NewProject() {
  enum EProjectRegistrationFormStep {
    SelectSector = "select-sector",
    ProjectRegistrationForm = "project-registration-form"
  }
  const [step, setStep] = useState<EProjectRegistrationFormStep>(
    EProjectRegistrationFormStep.SelectSector
  )

  const [selectedSector, setSelectedSector] = useState<string | null>(null)
  const { sectors, types } = useLoaderData<typeof loader>()

  const availableTypes = types.filter(
    type => type.sector_id === parseInt(selectedSector || "0")
  )

  return (
    <>
      <Forehead title="Create project" backref="/user/projects" />
      <Form method="POST" encType="multipart/form-data">
        {step === EProjectRegistrationFormStep.SelectSector && (
          <>
            <SelectProjectSectorStep
              sectors={sectors}
              setSelectedSector={setSelectedSector}
              selectedSector={selectedSector}
              onContinue={() => {
                setStep(EProjectRegistrationFormStep.ProjectRegistrationForm)
              }}
            />
          </>
        )}
        {step === EProjectRegistrationFormStep.ProjectRegistrationForm && (
          <ProjectRegistrationForm
            types={availableTypes}
            selectedSector={selectedSector}
            onBack={() => {
              setStep(EProjectRegistrationFormStep.SelectSector)
            }}
          />
        )}
      </Form>
    </>
  )
}
