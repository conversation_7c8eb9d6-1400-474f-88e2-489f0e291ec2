import { But<PERSON>, <PERSON>Date, DateValue } from "@heroui/react"
import {
  type ActionFunctionArgs,
  json,
  type LoaderFunctionArgs,
  redirect
} from "@remix-run/cloudflare"
import { Form, useLoaderData, useRouteLoaderData } from "@remix-run/react"
import { Info } from "lucide-react"
import { useState } from "react"
import AddressInfo from "~/components/projectRegistration/AddressInfor"
import CoverImageInfo from "~/components/projectRegistration/CoverImageInfo"
import NameInfo from "~/components/projectRegistration/NameInfo"
import ProjectDescriptionInfo from "~/components/projectRegistration/ProjectDescriptionInfo"
import SectorInfo from "~/components/projectRegistration/SectorInfo"
import DatePicker from "~/components/ui/DatePicker"
import Input from "~/components/ui/input"
import Select from "~/components/ui/select"
import TextArea from "~/components/ui/textArea"
import FileSelect from "~/components/userRegistration/FileSelect"
import OrganizationService from "~/services/db/organization"
import ProjectSectorService from "~/services/db/projectService"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ context, request }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const sectors = await new ProjectSectorService(env.DB).getMany()
  const user = await getAuthUser(context, request)
  const org = await new OrganizationService(env.DB).getById(user.org_id)
  return json({ sectors, user, org })
}

export async function action({ request, context, params }: ActionFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const user = await getAuthUser(context, request)
  const isNew = params.id === "new"
  const payload = await request.formData()
  const data = Object.fromEntries(payload)
  const action = data?.action
  if (isNew) {
    try {
      // Call to api when PDD is NEW then we get pdd id so go to pdd detail
      const id = 2
      if (action === "save-and-continue") {
        return redirect(`/user/projects/${params.project_id}/pdds/${id}/step2`)
      } else {
        return redirect(`/user/pdd/${id}`)
      }
    } catch (error) {
      console.log("Error", error)
      return null
    }
  } else {
    try {
      // Call to api when already have pdd for Update pdd
      if (action === "save-and-continue") {
        return redirect(
          `/user/projects/${params.project_id}/pdds/${params.id}/step2`
        )
      } else {
        return redirect(`/user/pdd/${params.id}`)
      }
    } catch (error) {
      console.log("Error", error)
      return null
    }
  }
}

const Step1 = () => {
  const { sectors, user, org } = useLoaderData<typeof loader>()

  const data = useRouteLoaderData(
    "routes/_user.user.projects.$project_id.pdds.$id._pddform"
  )
  const [startDate, setStartDate] = useState<CalendarDate | null>(null)
  const [endDate, setEndDate] = useState<CalendarDate | null>(null)
  const handleStartDateChange = (date: DateValue | null) => {
    setStartDate(date as CalendarDate | null)
  }
  const handleEndDateChange = (date: DateValue | null) => {
    setEndDate(date as CalendarDate | null)
  }
  return (
    <Form method="POST" encType="multipart/form-data">
      <div className="grid grid-cols-3">
        <div className="col-span-2 pr-24 space-y-6">
          <div className="flex gap-x-3 items-center bg-[#EDECEC] p-3 rounded-lg">
            <div>
              <Info width={22} height={22} />
            </div>
            <div>
              <p className="font-bold">
                Project Information from previous step
              </p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Select
                label="Sector"
                isRequired
                placeholder="Select Project Sector"
                data={sectors.map(sector => ({
                  key: sector.id.toString(),
                  label: sector.name
                }))}
                name="sector_id"
              />
            </div>
            <div>
              <Select
                label="Sub Sector"
                isRequired
                placeholder="Select Project Sub Sector"
                data={sectors.map(sector => ({
                  key: sector.id.toString(),
                  label: sector.name
                }))}
                name="sector_id"
              />
            </div>
            <div className="grid col-span-2">
              <Input
                label="Name"
                isRequired
                placeholder="Enter Project Name"
                name="name"
              />
            </div>
            <div className="grid col-span-2">
              <TextArea
                label="Description"
                placeholder="Enter project description"
                name="description"
              />
            </div>
          </div>
          <div>
            <div className="grid grid-cols-1 space-y-3">
              <div>
                <p className="font-bold text-[20px]">Timeline</p>
              </div>
              <div className="grid grid-cols-2 space-x-3">
                <div>
                  <DatePicker
                    isRequired
                    variant="bordered"
                    label="Planned Start Date"
                    name="start_date"
                    value={startDate}
                    onChange={handleStartDateChange}
                    maxValue={endDate}
                  />
                </div>
                <div>
                  <DatePicker
                    isRequired
                    variant="bordered"
                    label="Expected Completion Date"
                    name="end_date"
                    value={endDate}
                    onChange={handleEndDateChange}
                    minValue={startDate}
                  />
                </div>
              </div>
            </div>
          </div>
          <div>
            <div className="grid gap-y-3">
              <div>
                <p className="font-bold text-[20px]">Location</p>
              </div>
              <div className="grid grid-cols-2 gap-x-3 gap-y-3">
                <div>
                  <Select
                    label="City/Province"
                    isRequired
                    placeholder="Select City/Province"
                    name="province"
                    data={[
                      { key: "kompong cham", label: "Kompong Cham" },
                      { key: "phnom penh", label: "Phnom Penh" },
                      { key: "kompng thom", label: "Kompong Thom" },
                      { key: "banteay meanchey", label: "Banteay Meanchey" },
                      { key: "kompong speu", label: "Kompong Speu" },
                      { key: "kompong chhnang", label: "Kompong Chhnang" },
                      { key: "kampot", label: "Kampot" }
                    ]}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    label="Address"
                    placeholder="Enter project address"
                    name="address"
                  />
                </div>
                <div>
                  <Input
                    label="Latitude"
                    isRequired
                    placeholder="Enter project latitude"
                    name="lat"
                  />
                </div>
                <div>
                  <Input
                    label="Longitude"
                    isRequired
                    placeholder="Enter project longitude"
                    name="lng"
                  />
                </div>
                <div className="col-span-2">
                  <FileSelect label="Area Map (KML)" name="kml_file" />
                </div>
              </div>
            </div>
          </div>
          <div>
            <div className="grid grid-cols-1 space-y-3">
              <div>
                <p className="font-bold text-[20px]">
                  Project Developer (Proponent)
                </p>
              </div>
              <div className="grid grid-cols-3 gap-x-3 gap-y-3">
                <div className="col-span-2">
                  <Input
                    label="Organization Name"
                    isRequired
                    value={org?.name}
                    isDisabled
                    placeholder="Enter your organization name"
                  />
                </div>
                <div>
                  <Select
                    label="Type"
                    placeholder="Select Type of Organization"
                    isDisabled
                    data={[
                      { key: "1", label: "1" },
                      { key: "2", label: "2" }
                    ]}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    label="Address"
                    isRequired
                    placeholder="Enter project address"
                    value={org?.address}
                    isDisabled
                  />
                </div>
                <div>
                  <Select
                    isDisabled
                    label="Province/City"
                    placeholder="Select Province/City"
                    data={[
                      { key: "kompong cham", label: "Kompong Cham" },
                      { key: "phnom penh", label: "Phnom Penh" }
                    ]}
                  />
                </div>
                <div className="col-span-3">
                  <Input
                    isDisabled
                    label="Contact Person Name"
                    isRequired
                    placeholder="Enter your organization contact person full name"
                    value={org?.name}
                  />
                </div>
                <div className="col-span-3">
                  <div className="grid grid-cols-2 gap-x-3">
                    <div>
                      <Input
                        isDisabled
                        label="Contact Email"
                        isRequired
                        placeholder="Enter your organization email"
                        value={org?.email}
                      />
                    </div>
                    <div>
                      <Input
                        isDisabled
                        label="Contact Number"
                        value={org?.phone}
                        isRequired
                        placeholder="Enter your organization number"
                      />
                    </div>
                  </div>
                </div>
                <div className="col-span-3">
                  <Input
                    isDisabled
                    label="Website"
                    value={org?.website}
                    isRequired
                    placeholder="Enter your organization website"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-x-3">
            <Button
              className="bg-primary text-white"
              name="action"
              value="save-and-continue"
              type="submit"
              //   isLoading={isSubmitting}
            >
              Save & Continue
            </Button>
            <Button
              variant="bordered"
              color="primary"
              name="action"
              value="save"
              type="submit"
              //   isLoading={isSubmitting}
            >
              Save
            </Button>
            <Button className="bg-transparent">Cancel</Button>
          </div>
        </div>
        <div className="space-y-6">
          <SectorInfo />
          <NameInfo />
          <ProjectDescriptionInfo />
          <CoverImageInfo />
          <AddressInfo />
        </div>
      </div>
    </Form>
  )
}

export default Step1
