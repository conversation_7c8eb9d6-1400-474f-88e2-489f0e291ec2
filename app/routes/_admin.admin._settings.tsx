import { Tab, Tabs } from "@heroui/react"
import { Outlet, useLocation } from "@remix-run/react"

export default function AdminSettings() {
  const location = useLocation()

  const tabs = [
    { key: "general", href: "/admin/settings", title: "General" },
    { key: "sectors", href: "/admin/settings/sectors", title: "Sectors" }
  ]

  const active = tabs.find(tab => tab.href === location.pathname)?.key || null

  return (
    <div>
      <h1 className="text-4xl font-bold mb-4">Settings</h1>
      <div className="">
        <Tabs
          size="lg"
          selectedKey={active}
          aria-label="Tabs variants"
          disableAnimation
          className="flex border-b border-b-base-300"
          classNames={{ tabList: "p-0" }}
          variant="underlined">
          {tabs.map(tab => (
            <Tab as="a" href={tab.href} key={tab.key} title={tab.title} />
          ))}
        </Tabs>
      </div>
      <main className="py-5 px-2">
        <Outlet />
      </main>
    </div>
  )
}
