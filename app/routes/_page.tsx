import type { LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Outlet, useLoaderData } from "@remix-run/react"
import Footer from "~/components/footer"
import Header from "~/components/header"
import { getAuthenticator } from "~/services/auth"
import NotificationService from "~/services/db/notification"
import type { CloudflareENV } from "~/types"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const { authenticator } = getAuthenticator(env)
  const user = await authenticator.isAuthenticated(request)
  const notifications =
    user?.id &&
    (await new NotificationService(env.DB).getNotificationsByUserId({
      userId: user?.id
    }))

  return { user, notifications }
}

export default function AuthLayout() {
  const { user } = useLoaderData<typeof loader>()
  return (
    <div>
      <header>
        <Header user={user} />
      </header>
      <main>
        <Outlet />
      </main>
      <footer>
        <Footer />
      </footer>
    </div>
  )
}
