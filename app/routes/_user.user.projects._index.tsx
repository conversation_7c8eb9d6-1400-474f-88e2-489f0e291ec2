import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react"
import { defer, type LoaderFunctionArgs } from "@remix-run/cloudflare"
import { Await, useLoaderData, useNavigate } from "@remix-run/react"
import dayjs from "dayjs"
import { <PERSON>R<PERSON>, BeanOff, MapPin, Plus } from "lucide-react"
import { Suspense } from "react"
import Loading from "~/components/loading"
import { EStatus } from "~/enums/EStatus"
import { getImage } from "~/helpers/r2"
import { getProvinceName } from "~/lib/location"
import ProjectService from "~/services/db/project"
import type { CloudflareENV } from "~/types"
import { getAuthUser } from "~/utils/user"

export async function loader({ request, context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env as CloudflareENV
  const url = new URL(request.url)

  const page = Number(url.searchParams.get("page") || "1")
  const user = await getAuthUser(context, request)

  const res = new ProjectService(env.DB).getMany({
    page,
    pageSize: 25,
    user_id: user.id
  })
  return defer({ url, res, user })
}

export default function UserProjects() {
  const { url, res, user } = useLoaderData<typeof loader>()

  const navigate = useNavigate()

  return (
    <Suspense fallback={<Loading />}>
      <div className="p-0 lg:p-10">
        <div className="grid grid-cols-1 xl:flex justify-between items-center gap-6">
          <div>
            <p className="font-bold text-2xl">PROJECTS</p>
          </div>
          {user?.status === EStatus.Approve && (
            <div>
              <Button
                startContent={<Plus />}
                color="primary"
                onPress={() => {
                  navigate("/user/projects/new/project-information")
                }}>
                New Project
              </Button>
            </div>
          )}
        </div>
        <div className="mt-10">
          <Await resolve={res}>
            {res =>
              res.data.length > 0 ? (
                <>
                  <div className="gap-9 grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-center items-center">
                    {res.data.map((item, index) => {
                      return (
                        <Card
                          key={index}
                          isPressable
                          shadow="sm"
                          onPress={() => {
                            navigate(`/user/projects/${item?.id}`)
                          }}>
                          <CardBody className="overflow-visible p-0">
                            <Image
                              alt={"index"}
                              className="w-full object-cover h-[300px] rounded-b-none"
                              radius="lg"
                              shadow="sm"
                              src={
                                item.cover_img
                                  ? getImage(item.cover_img)
                                  : "/project1.jpeg"
                              }
                              width="100%"
                            />
                            <div className="flex-1 justify-center flex w-full bg-base-200 p-2 ">
                              <p className="text-[18px]">{item?.status}</p>
                            </div>
                          </CardBody>
                          <CardFooter className="p-6 grid grid-cols-1 gap-2">
                            <div className="flex justify-between">
                              <div>
                                <p className="text-sm uppercase">
                                  {item?.sector?.type}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm">
                                  {dayjs(item?.created_at).format(
                                    "DD MMM, YYYY"
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="h-[100px]">
                              <p className="text-start text-[20px] leading-7 line-clamp-3">
                                {item.name}
                              </p>
                            </div>
                            <div className="flex justify-between">
                              <div className="flex items-center">
                                <MapPin className="mr-2" />
                                <p className="text-sm">
                                  {getProvinceName(item?.province)}
                                </p>
                              </div>
                              <div className="flex items-center">
                                <p className="text-sm">View</p>
                                <ArrowRight className="ml-2" />
                              </div>
                            </div>
                          </CardFooter>
                        </Card>
                      )
                    })}
                    {user?.status === EStatus.Approve && (
                      <Card
                        isPressable
                        shadow="sm"
                        onPress={() =>
                          navigate("/user/projects/new/project-information")
                        }
                        className="bg-[#E4E4E7] flex justify-center align-middle items-center h-[520px]">
                        <div className="grid gap-4">
                          <div className="flex justify-center">
                            <Plus className="size-20" color="gray" />
                          </div>
                          <div>
                            <p className="text-gray-500">Add a new Project</p>
                          </div>
                        </div>
                      </Card>
                    )}
                  </div>
                </>
              ) : (
                <div className="bg-[#E4E4E7] shadow-md border rounded-2xl py-14 text-center grid gap-y-5 w-full">
                  <div className="flex justify-center">
                    <BeanOff className="w-24 h-24 text-gray-400" />
                  </div>
                  <div className="text-[#52525B] text-[20px]">
                    <p>You have no active projects</p>
                    <p>
                      Click on the&nbsp;
                      <span
                        className="font-bold cursor-pointer"
                        onClick={() => {
                          navigate("/user/projects/new/project-information")
                        }}>
                        Register New Project
                      </span>{" "}
                      to begin
                    </p>
                  </div>
                </div>
              )
            }
          </Await>
        </div>
      </div>
    </Suspense>
  )
}
