import i18next from "i18next"
import { I18nextProvider, initReactI18next } from "react-i18next"
import LanguageDetector from "i18next-browser-languagedetector"
import i18nConfig from "./i18n.config"
import { startTransition, StrictMode } from "react"
import { hydrateRoot } from "react-dom/client"
import { RemixBrowser } from "@remix-run/react"

async function hydrate() {
  // Get the HTML element's lang attribute which was set by the server
  const lang = document.documentElement.lang

  // Import translations dynamically
  const translations = {
    en: (await import("./assets/locales/en.json")).default,
    km: (await import("./assets/locales/km.json")).default
  }

  await i18next
    .use(initReactI18next) // Tell i18next to use the react-i18next plugin
    .use(LanguageDetector) // Setup a client-side language detector
    .init({
      ...i18nConfig, // spread the configuration
      lng: lang, // Set initial language to match server
      ns: ["common"],
      defaultNS: "common",
      resources: {
        en: { common: translations.en },
        km: { common: translations.km }
      },
      detection: {
        // Here only enable htmlTag detection, we'll detect the language only
        // server-side with remix-i18next, by using the `<html lang>` attribute
        // we can communicate to the client the language detected server-side
        order: ["htmlTag"],
        // Because we only use htmlTag, there's no reason to cache the language
        // on the browser, so we disable it
        caches: []
      }
    })

  startTransition(() => {
    hydrateRoot(
      document,
      <I18nextProvider i18n={i18next}>
        <StrictMode>
          <RemixBrowser />
        </StrictMode>
      </I18nextProvider>
    )
  })
}

if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate)
} else {
  // Safari doesn't support requestIdleCallback
  // https://caniuse.com/requestidlecallback
  window.setTimeout(hydrate, 1)
}
