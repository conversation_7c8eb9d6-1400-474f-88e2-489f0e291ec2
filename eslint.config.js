import js from "@eslint/js"
import importPlugin from "eslint-plugin-import"
import jsxA11yPlugin from "eslint-plugin-jsx-a11y"
import pluginReact from "eslint-plugin-react"
import { defineConfig } from "eslint/config"
import globals from "globals"
import { configs as tsConfigs } from "typescript-eslint"

const globalIgnores = [
  ".vercel/**",
  "dist/**",
  "build/**",
  "node_modules/**",
  "**/migrations/*",
  "!**/.server", // Un-ignore .server files/folders
  "!**/.client" // Un-ignore .client files/folders
]

export default defineConfig([
  {
    ignores: globalIgnores
  },
  {
    ...js.configs.recommended,
    files: ["**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    languageOptions: {
      globals: { ...globals.browser, ...globals.node }
    }
  },
  tsConfigs.recommended,
  {
    files: ["**/*.{ts,tsx,mts,cts}"],
    languageOptions: {
      parserOptions: {
        project: true, // Enable type-aware linting
        tsconfigRootDir: import.meta.dirname // Helps ESLint find your tsconfig.json
      }
    },
    rules: {
      "@typescript-eslint/prefer-as-const": "off",
      "@typescript-eslint/only-throw-error": "off",
      "@typescript-eslint/consistent-type-imports": "error"
    }
  },
  // React configurations
  pluginReact.configs.flat.recommended,
  jsxA11yPlugin.flatConfigs.recommended,
  {
    files: ["**/*.{jsx,tsx}"], // Apply to the same files as jsx-a11y rules
    settings: {
      formComponents: ["Form"], // Identifies custom Form components for a11y checks
      linkComponents: [
        // Identifies custom Link components
        { name: "Link", linkAttribute: "to" },
        { name: "NavLink", linkAttribute: "to" }
      ]
    },
    rules: {
      "react/react-in-jsx-scope": "off"
    }
  },
  // Import plugin configurations
  importPlugin.flatConfigs.recommended,
  importPlugin.flatConfigs.typescript,
  {
    // Custom settings for eslint-plugin-import
    files: ["**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    settings: {
      "import/resolver": {
        typescript: {
          alwaysTryTypes: true, // Recommended for TS projects
          project: ["./tsconfig.json"] // Explicitly point to tsconfig for path alias resolution
        },
        node: {
          // Fallback and for JS modules
          extensions: [
            ".js",
            ".jsx",
            ".ts",
            ".tsx",
            ".mjs",
            ".cjs",
            ".mts",
            ".cts"
          ]
        }
      },
      "import/internal-regex": "^~/" // for Remix's `~/` convention
    }
  },
  {
    files: ["**/*.{js,mjs,cjs}"],
    rules: {
      "no-unused-vars": "error", // For JS files, TS uses @typescript-eslint/no-unused-vars
      "import/no-dynamic-require": "warn"
    }
  },
  {
    // Configuration for the ESLint config file itself
    files: ["eslint.config.js"],
    languageOptions: {
      globals: {
        ...globals.node // ESLint config is a Node.js module
      }
    }
  }
])
