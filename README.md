# Cambodia Carbon Registry

This project uses Remix and Cloudflare, to learn more checkout these docs.
- 📖 [Remix docs](https://remix.run/docs)
- 📖 [Remix Cloudflare docs](https://remix.run/guides/vite#cloudflare)

## Development

01. First, you need <PERSON><PERSON><PERSON> as the package manager. Usually you can run the following command to install pnpm into your system.

```sh
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

If that does not work for you, check at the installation docs at https://pnpm.io/installation

02. Add ENV variables

```sh
cp env.template .dev.vars
```

03. Install packages and its dependencies

```sh
pnpm install
```

04. Run the dev server:

```sh
pnpm dev
```

To run Wrangler:

```sh
pnpm build
pnpm start
```

## Typegen

Generate types for your Cloudflare bindings in `wrangler.toml`:

```sh
pnpm typegen
```

You will need to rerun typegen whenever you make changes to `wrangler.toml`.

## Database

This project use Cloudflare D1 (SQLite) as the database. Integration and bindings are configured in the wrangler.toml config file.

To apply the migrations into local dev database

```sh
pnpm db:migrate:local
```

To generate new migration file from schema changes

```sh
pnpm db:generate
```

** Note: usually Drizzle will generate new migration file with prefix 0000_* into the migration folder. Your should manually rename the generated file to the sequence number which continue from other migrations prefix like eg: 0004_create_user_table.sql and so on.

To apply the migrations to the remote database, you can run

```sh
pnpm db:migrate:remote
```

### Seed

To create the first super user for the system

Local
```sh
pnpm db:seed:super:local
```

Remote
```sh
pnpm db:seed:super:remote
```

## Deployment

First, build your app for production:

```sh
pnpm build
```

Then, deploy your app to Cloudflare Pages:

```sh
pnpm deploy
```

## Styling

This template comes with [Tailwind CSS](https://tailwindcss.com/) and [DaisyUI](https://daisyui.com/) plugin for simple UI components.

[HeadlessUI](https://headlessui.com/) is also included for some UI that requires more JS like modal and dropdown, popoever etc.


