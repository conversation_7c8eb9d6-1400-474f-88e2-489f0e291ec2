import { heroui } from "@heroui/react"
import type { Config } from "tailwindcss"

export default {
  content: [
    "./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          '"Inter"',
          "ui-sans-serif",
          "system-ui",
          "sans-serif",
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"'
        ]
      }
    }
  },
  plugins: [
    heroui({
      layout: {
        radius: {
          small: "4px", // rounded-small
          medium: "6px", // rounded-medium
          large: "12px" // rounded-large
        }
      },
      themes: {
        light: {
          colors: {
            primary: {
              DEFAULT: "#0C8442",
              foreground: "#FFFFFF"
            },
            secondary: {
              DEFAULT: "#DF1C41",
              foreground: "#000000"
            }
          }
        }
      }
    })
  ]
} satisfies Config
