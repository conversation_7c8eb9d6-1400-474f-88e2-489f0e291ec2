{"name": "carbon-copyregistry", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "deploy": "pnpm run build && wrangler pages deploy", "dev": "remix vite:dev", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint --fix .", "start": "wrangler pages dev ./build/client", "typecheck": "tsc", "typegen": "wrangler types", "preview": "pnpm run build && wrangler pages dev", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate --dialect=sqlite --schema=./app/db/schema --out=./app/db/migrations", "db:migrate:local": "wrangler d1 migrations apply cop --local", "db:migrate:remote": "wrangler d1 migrations apply cop --remote", "db:migrate:preview": "wrangler d1 migrations apply --env preview cop-preview --remote", "db:seed:super:local": "wrangler d1 execute DB --local --file=app/db/seeds/super.sql", "db:seed:super:remote": "wrangler d1 execute DB --remote --file=app/db/seeds/super.sql", "db:seed:project:local": "wrangler d1 execute DB --local --file=app/db/seeds/project.sql", "db:seed:project:remote": "wrangler d1 execute DB --remote --file=app/db/seeds/project.sql", "cf-typegen": "wrangler types"}, "dependencies": {"@epic-web/totp": "^2.2.0", "@headlessui/react": "^2.2.4", "@heroui/react": "2.6.13", "@internationalized/date": "3.6.0", "@remix-run/cloudflare": "^2.16.8", "@remix-run/cloudflare-pages": "^2.16.8", "@remix-run/react": "^2.16.8", "@vis.gl/react-google-maps": "^1.5.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^11.18.2", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "isbot": "^4.4.0", "jose": "^6.0.11", "lucide-react": "^0.454.0", "nanoid": "^5.1.5", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.2", "remix-auth": "^3.7.0", "remix-auth-form": "^1.5.0", "remix-i18next": "^7.2.1", "resend": "^4.5.1", "zod": "^3.25.49"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250601.0", "@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@faker-js/faker": "^9.8.0", "@libsql/client": "^0.15.8", "@remix-run/dev": "^2.16.8", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.9", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.2", "eslint": "^9.29.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.15.0", "postcss": "^8.5.4", "prettier": "3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vite": "^5.4.19", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9", "wrangler": "4.19.1"}, "engines": {"node": ">=20.0.0"}, "packageManager": "pnpm@10.10.0"}