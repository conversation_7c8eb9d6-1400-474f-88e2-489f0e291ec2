SHELL=/bin/bash

colorOff=\033[0m
colorGreen=\033[92m

define echo_title
	@echo -e "${colorGreen}*** ${1} ***${colorOff}"
endef

.PHONY: setup

# initial local working environment
setup:
	$(call echo_title, "Create .dev.vars")
	@if [ -f ".dev.vars" ]; then\
		echo "* The .dev.vars is already exist";\
	else\
		cp env.template .dev.vars;\
		echo "* The .env is copied form .env.example";\
	fi

	$(call echo_title, "Create .dev.vars")
	@if [ -f "wrangler.toml" ]; then\
		echo "* The wrangler.toml is already exist";\
	else\
		cp wrangler.toml.example wrangler.toml;\
		echo "* The wrangler.toml is copied form wrangler.toml.example";\
	fi

	$(call echo_title, "Install package dependency")
	pnpm install

	$(MAKE) dev

dev:
	$(call echo_title, "Run dev")
	pnpm dev

migration:
	pnpm db:generate

db-migrate:
	$(call echo_title, "Run database migration")
	pnpm db:migrate:local

db-seed:
	$(call echo_title, "Generate database seeder")
	pnpm db:seed:super:local
	pnpm db:seed:project:local

db-fresh:
	$(call echo_title, "Drop database and run migration")
	rm -rf .wrangler

db-table-list:
	pnpm wrangler d1 execute DB --local --command "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';"

db-table-desc:
	@if [ -z "$(table)" ]; then \
		echo "Error: --table parameter is required."; \
		exit 1; \
	fi
	pnpm wrangler d1 execute DB --local --command "PRAGMA table_info('$(table)');"

db-exec:
	@if [ -z "$(command)" ]; then \
		echo "Error: --command parameter is required."; \
		exit 1; \
	fi
	pnpm wrangler d1 execute DB --local --command "$(command)"

fresh-seeder:
	$(MAKE) db-fresh
	$(MAKE) db-migrate
	$(MAKE) db-seed

lint:
	pnpm lint

lint-fix:
	pnpm lint:fix

tsc:
	pnpm typecheck
